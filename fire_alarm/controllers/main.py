# -*- coding: utf-8 -*-
from odoo import http, fields, _
from odoo.http import request
from werkzeug.wrappers import Response
import json
import logging

_logger = logging.getLogger(__name__)

class FireAlarmController(http.Controller):
    """
    Controller for Fire Alarm API endpoints.
    Provides endpoints for external systems to push alarm events.
    """

    @http.route('/api/fire_alarm/event', type='json', auth='public', methods=['POST'], csrf=False)
    def create_alarm_event(self, **kwargs):
        """
        Endpoint to create a new fire_alarm.event record from external systems.

        Expected JSON payload:
        {
            "device_id": "SD-HQ-GF-001",  # Device serial number
            "event_type": "alarm",        # One of: alarm, fault, test, restore
            "severity": "high",           # One of: low, medium, high, critical
            "description": "Smoke detected in reception area",
            "latitude": 24.7136,          # Optional
            "longitude": 46.6753,         # Optional
            "additional_data": {}         # Optional JSON object with additional data
        }

        Returns:
            JSON response with status and details
        """
        try:
            # Get JSON data
            data = request.jsonrequest
            _logger.info(f"Received alarm event data: {data}")

            # Validate required fields
            required_fields = ['device_id', 'event_type', 'severity', 'description']
            for field in required_fields:
                if field not in data:
                    return self._json_response(
                        success=False,
                        message=f"Missing required field: {field}",
                        status=400
                    )

            # Validate event_type
            valid_event_types = ['alarm', 'fault', 'test', 'restore']
            if data['event_type'] not in valid_event_types:
                return self._json_response(
                    success=False,
                    message=f"Invalid event_type. Must be one of: {', '.join(valid_event_types)}",
                    status=400
                )

            # Validate severity
            valid_severities = ['low', 'medium', 'high', 'critical']
            if data['severity'] not in valid_severities:
                return self._json_response(
                    success=False,
                    message=f"Invalid severity. Must be one of: {', '.join(valid_severities)}",
                    status=400
                )

            # Find the device by serial number
            device = request.env['fire_alarm.device'].sudo().search(
                [('serial_number', '=', data['device_id'])], limit=1
            )

            if not device:
                return self._json_response(
                    success=False,
                    message=f"Device with serial number {data['device_id']} not found",
                    status=404
                )

            # Prepare event data
            event_data = {
                'device_id': device.id,
                'building_id': device.building_id.id,
                'zone_id': device.zone_id.id,
                'event_type': data['event_type'],
                'severity': data['severity'],
                'description': data['description'],
                'event_time': fields.Datetime.now(),
                'acknowledged': False,
                'resolved': False,
            }

            # Add optional fields if provided
            if 'latitude' in data and data['latitude']:
                event_data['latitude'] = data['latitude']

            if 'longitude' in data and data['longitude']:
                event_data['longitude'] = data['longitude']

            if 'additional_data' in data and data['additional_data']:
                event_data['notes'] = json.dumps(data['additional_data'])

            # Generate a name for the event based on event type and current time
            event_prefix = {
                'alarm': 'ALM',
                'fault': 'FLT',
                'test': 'TST',
                'restore': 'RST'
            }.get(data['event_type'], 'EVT')

            current_time = fields.Datetime.now()
            event_name = f"{event_prefix}-{current_time.strftime('%Y%m%d-%H%M%S')}"

            # Add name to event data
            event_data['name'] = event_name

            # Create the event
            event = request.env['fire_alarm.event'].sudo().create(event_data)

            # Update device status if it's an alarm or fault
            if data['event_type'] in ['alarm', 'fault']:
                device.sudo().write({'status': data['event_type']})

            # Return success response
            return self._json_response(
                success=True,
                message="Event created successfully",
                data={
                    'event_id': event.id,
                    'name': event.name,
                    'event_time': fields.Datetime.to_string(event.event_time),
                },
                status=201
            )

        except Exception as e:
            _logger.error(f"Error creating alarm event: {str(e)}")
            return self._json_response(
                success=False,
                message=f"Server error: {str(e)}",
                status=500
            )

    @http.route('/api/fire_alarm/device/<string:serial_number>/status', type='http', auth='public', methods=['GET'], csrf=False)
    def get_device_status(self, serial_number, **kwargs):
        """
        Endpoint to get the status of a device by its serial number.

        Args:
            serial_number: The serial number of the device

        Returns:
            JSON response with device status and details
        """
        try:
            # Find the device by serial number
            device = request.env['fire_alarm.device'].sudo().search(
                [('serial_number', '=', serial_number)], limit=1
            )

            if not device:
                return self._http_response(
                    success=False,
                    message=f"Device with serial number {serial_number} not found",
                    status=404
                )

            # Get the latest event for this device
            latest_event = request.env['fire_alarm.event'].sudo().search(
                [('device_id', '=', device.id)],
                limit=1,
                order='event_time desc'
            )

            # Prepare response data
            response_data = {
                'device_id': device.id,
                'serial_number': device.serial_number,
                'name': device.name,
                'status': device.status,
                'device_type': device.device_type,
                'building': {
                    'id': device.building_id.id,
                    'name': device.building_id.name,
                    'code': device.building_id.code,
                } if device.building_id else None,
                'zone': {
                    'id': device.zone_id.id,
                    'name': device.zone_id.name,
                    'code': device.zone_id.code,
                } if device.zone_id else None,
                'latest_event': {
                    'id': latest_event.id,
                    'name': latest_event.name,
                    'event_type': latest_event.event_type,
                    'severity': latest_event.severity,
                    'event_time': fields.Datetime.to_string(latest_event.event_time),
                    'acknowledged': latest_event.acknowledged,
                    'resolved': latest_event.resolved,
                } if latest_event else None,
            }

            # Return success response
            return self._http_response(
                success=True,
                message="Device status retrieved successfully",
                data=response_data,
                status=200
            )

        except Exception as e:
            _logger.error(f"Error getting device status: {str(e)}")
            return self._http_response(
                success=False,
                message=f"Server error: {str(e)}",
                status=500
            )

    @http.route('/api/fire_alarm/events', type='http', auth='public', methods=['GET'], csrf=False)
    def get_events(self, **kwargs):
        """
        Endpoint to get a list of events with filtering options.

        Query parameters:
            event_type: Filter by event type (alarm, fault, test, restore)
            building_id: Filter by building ID
            zone_id: Filter by zone ID
            device_id: Filter by device ID
            resolved: Filter by resolved status (true/false)
            acknowledged: Filter by acknowledged status (true/false)
            limit: Maximum number of events to return (default: 50)
            offset: Number of events to skip (default: 0)

        Returns:
            JSON response with list of events
        """
        try:
            # Parse query parameters
            event_type = kwargs.get('event_type')
            building_id = kwargs.get('building_id')
            zone_id = kwargs.get('zone_id')
            device_id = kwargs.get('device_id')
            resolved = kwargs.get('resolved')
            acknowledged = kwargs.get('acknowledged')
            limit = int(kwargs.get('limit', 50))
            offset = int(kwargs.get('offset', 0))

            # Build domain
            domain = []

            if event_type:
                domain.append(('event_type', '=', event_type))

            if building_id:
                domain.append(('building_id', '=', int(building_id)))

            if zone_id:
                domain.append(('zone_id', '=', int(zone_id)))

            if device_id:
                domain.append(('device_id', '=', int(device_id)))

            if resolved is not None:
                domain.append(('resolved', '=', resolved.lower() == 'true'))

            if acknowledged is not None:
                domain.append(('acknowledged', '=', acknowledged.lower() == 'true'))

            # Get events
            events = request.env['fire_alarm.event'].sudo().search(
                domain,
                limit=limit,
                offset=offset,
                order='event_time desc'
            )

            # Prepare response data
            events_data = []
            for event in events:
                events_data.append({
                    'id': event.id,
                    'name': event.name,
                    'event_type': event.event_type,
                    'severity': event.severity,
                    'description': event.description,
                    'event_time': fields.Datetime.to_string(event.event_time),
                    'acknowledged': event.acknowledged,
                    'resolved': event.resolved,
                    'device': {
                        'id': event.device_id.id,
                        'name': event.device_id.name,
                        'serial_number': event.device_id.serial_number,
                    } if event.device_id else None,
                    'building': {
                        'id': event.building_id.id,
                        'name': event.building_id.name,
                        'code': event.building_id.code,
                    } if event.building_id else None,
                    'zone': {
                        'id': event.zone_id.id,
                        'name': event.zone_id.name,
                        'code': event.zone_id.code,
                    } if event.zone_id else None,
                })

            # Get total count for pagination
            total_count = request.env['fire_alarm.event'].sudo().search_count(domain)

            # Return success response
            return self._http_response(
                success=True,
                message="Events retrieved successfully",
                data={
                    'events': events_data,
                    'total_count': total_count,
                    'limit': limit,
                    'offset': offset,
                },
                status=200
            )

        except Exception as e:
            _logger.error(f"Error getting events: {str(e)}")
            return self._http_response(
                success=False,
                message=f"Server error: {str(e)}",
                status=500
            )

    def _json_response(self, success=True, message='', data=None, status=200):
        """Helper method to create a JSON response for JSON type routes"""
        response = {
            'success': success,
            'message': message,
        }

        if data is not None:
            response['data'] = data

        return response

    def _http_response(self, success=True, message='', data=None, status=200):
        """Helper method to create an HTTP response for HTTP type routes"""
        response = {
            'success': success,
            'message': message,
        }

        if data is not None:
            response['data'] = data

        return Response(
            json.dumps(response),
            status=status,
            content_type='application/json'
        )

    @http.route('/fire_alarm/trigger_event', type='http', auth='public', methods=['GET'], csrf=False)
    def trigger_event(self, device_serial=None, event_type=None, severity=None, description=None, **kwargs):
        """
        Simple endpoint to quickly create a fire alarm event for testing purposes.
        Can be called directly from a browser or with a simple curl command.

        Query parameters:
            device_serial: The serial number of the device (required)
            event_type: The type of event (alarm, fault, test, restore) (required)
            severity: The severity of the event (low, medium, high, critical) (required)
            description: A description of the event (required)

        Example URL:
            /fire_alarm/trigger_event?device_serial=SD-HQ-GF-001&event_type=alarm&severity=high&description=Test%20alarm
        """
        # Validation: Check if required parameters are provided
        if not device_serial:
            return "Error: 'device_serial' parameter is required."

        if not event_type:
            return "Error: 'event_type' parameter is required."

        if not severity:
            return "Error: 'severity' parameter is required."

        if not description:
            return "Error: 'description' parameter is required."

        # Validate event_type
        valid_event_types = ['alarm', 'fault', 'test', 'restore']
        if event_type not in valid_event_types:
            return f"Error: 'event_type' must be one of: {', '.join(valid_event_types)}"

        # Validate severity
        valid_severities = ['low', 'medium', 'high', 'critical']
        if severity not in valid_severities:
            return f"Error: 'severity' must be one of: {', '.join(valid_severities)}"

        try:
            # Find the device by serial number
            device = request.env['fire_alarm.device'].sudo().search(
                [('serial_number', '=', device_serial)], limit=1
            )

            if not device:
                return f"Error: Device with serial number '{device_serial}' not found."

            # Generate a name for the event based on event type and current time
            event_prefix = {
                'alarm': 'ALM',
                'fault': 'FLT',
                'test': 'TST',
                'restore': 'RST'
            }.get(event_type, 'EVT')

            current_time = fields.Datetime.now()
            event_name = f"{event_prefix},device.name,{current_time.strftime('%Y%m%d-%H%M%S')}"

            # Create the event
            event = request.env['fire_alarm.event'].sudo().create({
                'name': event_name,
                'device_id': device.id,
                'building_id': device.building_id.id,
                'zone_id': device.zone_id.id,
                'event_type': event_type,
                'severity': severity,
                'description': description,
                'event_time': current_time,
                'acknowledged': False,
                'resolved': False,
            })

            # Update device status if it's an alarm or fault
            if event_type in ['alarm', 'fault']:
                device.sudo().write({'status': event_type})

            # Prepare a success response
            response_data = {
                'success': True,
                'message': 'Event created successfully!',
                'event_id': event.id,
                'name': event.name,
                'event_time': fields.Datetime.to_string(event.event_time),
                'device': {
                    'id': device.id,
                    'name': device.name,
                    'serial_number': device.serial_number
                },
                'building': {
                    'id': device.building_id.id,
                    'name': device.building_id.name
                } if device.building_id else None,
                'zone': {
                    'id': device.zone_id.id,
                    'name': device.zone_id.name
                } if device.zone_id else None
            }

            # Return the response as JSON
            return Response(
                json.dumps(response_data),
                status=200,
                content_type='application/json'
            )

        except Exception as e:
            _logger.error(f"Error triggering event: {str(e)}")
            return f"Error: {str(e)}"

    @http.route('/fire_alarm/test_bus', type='http', auth='public', methods=['GET'], csrf=False)
    def test_bus_notification(self, **kwargs):
        """Test endpoint to manually trigger a bus notification"""
        try:
            # Use the exact same approach as ams_counter module
            payload = {
                'model': 'fire_alarm.event',
                'id': 0
            }

            # Send a test notification through the bus
            request.env['bus.bus']._sendone('auto_refresh', 'auto_refresh', payload)

            _logger.info(f"Test auto_refresh notification sent: {payload}")

            return Response(
                json.dumps({
                    'success': True,
                    'message': 'Test bus notification sent successfully!'
                }),
                status=200,
                content_type='application/json'
            )
        except Exception as e:
            _logger.error(f"Error sending test bus notification: {str(e)}")
            return f"Error: {str(e)}"