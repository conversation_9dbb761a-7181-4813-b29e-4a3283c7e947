# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class FireAlarm(models.Model):
    _name = 'fire_alarm.fire_alarm'
    _description = 'Fire Alarm'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Name', required=True, tracking=True)
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    building_ids = fields.One2many('fire_alarm.building', 'fire_alarm_id', string='Buildings')
    building_count = fields.Integer(string='Building Count', compute='_compute_building_count')
    device_count = fields.Integer(string='Device Count', compute='_compute_device_count')
    zone_count = fields.Integer(string='Zone Count', compute='_compute_zone_count')

    @api.depends('building_ids')
    def _compute_building_count(self):
        for record in self:
            record.building_count = len(record.building_ids)

    @api.depends('building_ids.device_ids')
    def _compute_device_count(self):
        for record in self:
            record.device_count = len(record.building_ids.mapped('device_ids'))

    @api.depends('building_ids.zone_ids')
    def _compute_zone_count(self):
        for record in self:
            record.zone_count = len(record.building_ids.mapped('zone_ids'))

    def action_view_buildings(self):
        self.ensure_one()
        return {
            'name': _('Buildings'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.building',
            'domain': [('fire_alarm_id', '=', self.id)],
            'context': {'default_fire_alarm_id': self.id},
        }

    def action_view_zones(self):
        self.ensure_one()
        return {
            'name': _('Zones'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.zone',
            'domain': [('building_id.fire_alarm_id', '=', self.id)],
            'context': {'default_fire_alarm_id': self.id},
        }

    def action_view_devices(self):
        self.ensure_one()
        return {
            'name': _('Devices'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.device',
            'domain': [('building_id.fire_alarm_id', '=', self.id)],
            'context': {'default_fire_alarm_id': self.id},
        }
