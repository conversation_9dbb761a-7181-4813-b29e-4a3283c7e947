# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class AlarmZone(models.Model):
    _name = 'fire_alarm.zone'
    _description = 'Alarm Zone'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Name', required=True, tracking=True)
    code = fields.Char(string='Code', required=True, tracking=True)
    building_id = fields.Many2one('fire_alarm.building', string='Building', required=True, ondelete='cascade')
    floor = fields.Integer(string='Floor Number')
    description = fields.Text(string='Description')
    device_ids = fields.One2many('fire_alarm.device', 'zone_id', string='Devices')
    device_count = fields.Integer(string='Device Count', compute='_compute_device_count')
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', related='building_id.company_id', store=True)

    _sql_constraints = [
        ('code_building_uniq', 'unique(code, building_id)', 'Zone code must be unique per building!')
    ]

    def action_view_devices(self):
        self.ensure_one()
        return {
            'name': _('Devices'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.device',
            'domain': [('zone_id', '=', self.id)],
            'context': {'default_zone_id': self.id, 'default_building_id': self.building_id.id},
        }

    @api.depends('device_ids')
    def _compute_device_count(self):
        for record in self:
            record.device_count = len(record.device_ids)
