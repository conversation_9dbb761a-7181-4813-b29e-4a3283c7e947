# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class AlarmEvent(models.Model):
    _name = 'fire_alarm.event'
    _description = 'Alarm Event'
    _order = 'event_time desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Event ID', compute='_compute_name', store=True)
    device_id = fields.Many2one('fire_alarm.device', string='Device', required=True, ondelete='cascade')
    zone_id = fields.Many2one('fire_alarm.zone', string='Zone', related='device_id.zone_id', store=True)
    building_id = fields.Many2one('fire_alarm.building', string='Building', related='device_id.building_id', store=True)
    event_type = fields.Selection([
        ('alarm', 'Alarm Activated'),
        ('fault', 'Fault Detected'),
        ('restore', 'System Restored'),
        ('test', 'Test Activation'),
        ('maintenance', 'Maintenance'),
    ], string='Event Type', required=True, tracking=True)
    event_time = fields.Datetime(string='Event Time', required=True)
    severity = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], string='Severity', tracking=True)
    description = fields.Text(string='Description')
    latitude = fields.Float(string='Latitude', related='device_id.latitude', store=True, digits=(16, 8), help="Latitude where the event occurred")
    longitude = fields.Float(string='Longitude', related='device_id.longitude', store=True, digits=(16, 8), help="Longitude where the event occurred")
    device_location = fields.Char(string='Device Location', related='device_id.location', store=True, readonly=True)
    acknowledged = fields.Boolean(string='Acknowledged', default=False, tracking=True)
    acknowledged_by = fields.Many2one('res.users', string='Acknowledged By', tracking=True)
    acknowledged_time = fields.Datetime(string='Acknowledged Time')
    resolved = fields.Boolean(string='Resolved', default=False, tracking=True)
    resolved_by = fields.Many2one('res.users', string='Resolved By', tracking=True)
    resolved_time = fields.Datetime(string='Resolved Time')
    notes = fields.Text(string='Notes')
    company_id = fields.Many2one('res.company', string='Company', related='device_id.company_id', store=True)

    @api.depends('device_id', 'device_id.name', 'event_time')
    def _compute_name(self):
        for event in self:
            if event.device_id and event.event_time:
                device_name = event.device_id.name or 'Unknown Device'
                event_time_str = fields.Datetime.context_timestamp(event,event.event_time).strftime('%Y-%m-%d-%H:%M:%S')
                event_type_prefix = dict(self._fields['event_type'].selection).get(event.event_type, 'EVT')
                event_type_code = event_type_prefix[:3].upper()
                event.name = f"{event_type_code},{device_name},{event_time_str}"
            else:
                event.name = f"NEW-{fields.Datetime.now().strftime('%Y%m%d-%H%M%S')}"

    @api.model_create_multi
    def create(self, vals_list):
        events = super(AlarmEvent, self).create(vals_list)
        # Send notification for real-time dashboard update
        self._send_dashboard_update()
        return events

    def write(self, vals):
        result = super(AlarmEvent, self).write(vals)
        # Send notification for real-time dashboard update
        self._send_dashboard_update()
        return result

    def action_acknowledge(self):
        self.ensure_one()
        if not self.acknowledged:
            self.write({
                'acknowledged': True,
                'acknowledged_by': self.env.user.id,
                'acknowledged_time': fields.Datetime.now(),
            })

    def action_resolve(self):
        self.ensure_one()
        if not self.resolved:
            self.write({
                'resolved': True,
                'resolved_by': self.env.user.id,
                'resolved_time': fields.Datetime.now(),
            })

    def _send_dashboard_update(self):
        """Send notification through the bus for real-time dashboard updates"""
        # Use the exact same approach as ams_counter module
        _logger.info(f"Sending auto_refresh notification for model {self._name}, id {self.id}")
        self.env['bus.bus']._sendone('fire_alarm_auto_refresh', 'fire_alarm_auto_refresh',
                                   {'model': self._name, 'id': self.id if len(self) == 1 else False})

    @api.model
    def get_alarm_statistics(self):
        """Get statistics for the dashboard"""
        # Count active alarms
        active_alarms = self.search_count([
            ('event_type', '=', 'alarm'),
            ('resolved', '=', False),
        ])

        # Count active faults
        active_faults = self.search_count([
            ('event_type', '=', 'fault'),
            ('resolved', '=', False),
        ])

        # Count critical alarms
        critical_alarms = self.search_count([
            ('event_type', '=', 'alarm'),
            ('severity', '=', 'critical'),
            ('resolved', '=', False),
        ])

        # Count buildings and devices
        building_count = self.env['fire_alarm.building'].search_count([])
        device_count = self.env['fire_alarm.device'].search_count([])

        # Count online and fault devices
        online_devices = self.env['fire_alarm.device'].search_count([('status', '=', 'normal')])
        fault_devices = self.env['fire_alarm.device'].search_count([('status', '=', 'fault')])

        # Count total zones
        total_zones = self.env['fire_alarm.zone'].search_count([])

        # Get event type distribution for pie chart
        event_types = ['alarm', 'fault', 'test', 'restore']
        event_type_counts = []
        event_type_colors = ['#dc3545', '#ffc107', '#0dcaf0', '#198754']

        for event_type in event_types:
            count = self.search_count([('event_type', '=', event_type)])
            if count > 0:
                event_type_counts.append(count)
            else:
                event_type_counts.append(0)

        event_type_data = {
            'labels': event_types,
            'datasets': [{
                'data': event_type_counts,
                'backgroundColor': event_type_colors,
            }]
        }

        # Get alarms by building for bar chart
        buildings = self.env['fire_alarm.building'].search([])
        building_names = []
        building_alarm_counts = []

        for building in buildings:
            building_names.append(building.name)
            count = self.search_count([
                ('building_id', '=', building.id),
                ('event_type', '=', 'alarm'),
            ])
            building_alarm_counts.append(count)

        building_data = {
            'labels': building_names,
            'datasets': [{
                'label': 'Alarms',
                'data': building_alarm_counts,
                'backgroundColor': '#dc3545',
            }]
        }

        # Get recent events
        recent_events = self.search_read(
            domain=[],
            fields=['name', 'device_id', 'building_id', 'zone_id', 'event_type', 'severity', 'event_time', 'acknowledged', 'resolved'],
            limit=10,
            order='event_time desc'
        )

        return {
            'active_alarms': active_alarms,
            'active_faults': active_faults,
            'critical_alarms': critical_alarms,
            'building_count': building_count,
            'device_count': device_count,
            'online_devices': online_devices,
            'fault_devices': fault_devices,
            'total_buildings': building_count,  # For backward compatibility
            'total_devices': device_count,      # For backward compatibility
            'total_zones': total_zones,
            'event_type_data': event_type_data,
            'building_data': building_data,
            'recent_events': recent_events,
        }
