# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class Building(models.Model):
    _name = 'fire_alarm.building'
    _description = 'Building'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Name', required=True, tracking=True)
    code = fields.Char(string='Code', required=True, tracking=True)
    fire_alarm_id = fields.Many2one('fire_alarm.fire_alarm', string='Fire Alarm System', required=True, ondelete='cascade')
    address = fields.Text(string='Address')
    floors = fields.Integer(string='Number of Floors', default=1)
    zone_ids = fields.One2many('fire_alarm.zone', 'building_id', string='Alarm Zones')
    device_ids = fields.One2many('fire_alarm.device', 'building_id', string='Alarm Devices')
    zone_count = fields.Integer(string='Zone Count', compute='_compute_zone_count')
    device_count = fields.Integer(string='Device Count', compute='_compute_device_count')
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    _sql_constraints = [
        ('code_uniq', 'unique(code, company_id)', 'Building code must be unique per company!')
    ]

    def action_view_zones(self):
        self.ensure_one()
        return {
            'name': _('Zones'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.zone',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }

    def action_view_devices(self):
        self.ensure_one()
        return {
            'name': _('Devices'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.device',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }

    @api.depends('zone_ids')
    def _compute_zone_count(self):
        for record in self:
            record.zone_count = len(record.zone_ids)

    @api.depends('device_ids')
    def _compute_device_count(self):
        for record in self:
            record.device_count = len(record.device_ids)
