# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class AlarmDevice(models.Model):
    _name = 'fire_alarm.device'
    _description = 'Alarm Device'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    @api.model
    def get_alarm_statistics(self):
        """Get statistics for the fire alarm dashboard"""
        # Get counts
        active_alarms = self.env['fire_alarm.event'].search_count([
            ('event_type', '=', 'alarm'),
            ('resolved', '=', False)
        ])

        active_faults = self.env['fire_alarm.event'].search_count([
            ('event_type', '=', 'fault'),
            ('resolved', '=', False)
        ])

        critical_alarms = self.env['fire_alarm.event'].search_count([
            ('event_type', '=', 'alarm'),
            ('severity', '=', 'critical'),
            ('resolved', '=', False)
        ])

        total_devices = self.search_count([])
        online_devices = self.search_count([('status', '=', 'normal')])
        fault_devices = self.search_count([('status', '=', 'fault')])

        total_buildings = self.env['fire_alarm.building'].search_count([])
        total_zones = self.env['fire_alarm.zone'].search_count([])

        # Get recent events
        recent_events = self.env['fire_alarm.event'].search([], limit=10, order='event_time desc')

        # Prepare data for event type chart
        event_types = ['alarm', 'fault', 'test', 'restore']
        event_type_counts = []
        event_type_colors = ['#dc3545', '#ffc107', '#0d6efd', '#198754']

        for event_type in event_types:
            count = self.env['fire_alarm.event'].search_count([('event_type', '=', event_type)])
            event_type_counts.append(count)

        event_type_data = {
            'labels': ['Alarms', 'Faults', 'Tests', 'Restores'],
            'datasets': [{
                'data': event_type_counts,
                'backgroundColor': event_type_colors,
                'borderColor': event_type_colors,
                'borderWidth': 1
            }]
        }

        # Prepare data for building chart
        buildings = self.env['fire_alarm.building'].search([], limit=5)
        building_names = []
        building_alarm_counts = []

        for building in buildings:
            building_names.append(building.name)
            count = self.env['fire_alarm.event'].search_count([
                ('building_id', '=', building.id),
                ('event_type', '=', 'alarm')
            ])
            building_alarm_counts.append(count)

        building_data = {
            'labels': building_names,
            'datasets': [{
                'label': 'Alarms',
                'data': building_alarm_counts,
                'backgroundColor': '#dc3545',
                'borderColor': '#dc3545',
                'borderWidth': 1
            }]
        }

        return {
            'active_alarms': active_alarms,
            'active_faults': active_faults,
            'critical_alarms': critical_alarms,
            'total_devices': total_devices,
            'online_devices': online_devices,
            'fault_devices': fault_devices,
            'total_buildings': total_buildings,
            'total_zones': total_zones,
            'recent_events': recent_events.read(),
            'event_type_data': event_type_data,
            'building_data': building_data
        }

    name = fields.Char(string='Name', required=True, tracking=True)
    serial_number = fields.Char(string='Serial Number', required=True, tracking=True)
    device_type = fields.Selection([
        ('smoke_detector', 'Smoke Detector'),
        ('heat_detector', 'Heat Detector'),
        ('manual_call_point', 'Manual Call Point'),
        ('multi_sensor', 'Multi-Sensor'),
        ('control_panel', 'Control Panel'),
        ('gas_suppression', 'Gas Suppression System'),
    ], string='Device Type', required=True, tracking=True)
    building_id = fields.Many2one('fire_alarm.building', string='Building', required=True, ondelete='cascade')
    zone_id = fields.Many2one('fire_alarm.zone', string='Zone', required=True, domain="[('building_id', '=', building_id)]", ondelete='cascade')
    location = fields.Char(string='Specific Location')
    latitude = fields.Float(string='Latitude', digits=(16, 8), help="Latitude in decimal degrees")
    longitude = fields.Float(string='Longitude', digits=(16, 8), help="Longitude in decimal degrees")
    last_maintenance = fields.Date(string='Last Maintenance')
    next_maintenance = fields.Date(string='Next Maintenance')
    status = fields.Selection([
        ('normal', 'Normal'),
        ('alarm', 'Alarm'),
        ('fault', 'Fault'),
        ('disabled', 'Disabled'),
        ('testing', 'Testing'),
    ], string='Status', default='normal', tracking=True)
    ip_address = fields.Char(string='IP Address')
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', related='building_id.company_id', store=True)
    event_ids = fields.One2many('fire_alarm.event', 'device_id', string='Events')
    event_count = fields.Integer(string='Event Count', compute='_compute_event_count')

    _sql_constraints = [
        ('serial_uniq', 'unique(serial_number, company_id)', 'Device serial number must be unique per company!')
    ]

    @api.onchange('building_id')
    def _onchange_building_id(self):
        self.zone_id = False

    def action_view_events(self):
        self.ensure_one()
        return {
            'name': _('Events'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'fire_alarm.event',
            'domain': [('device_id', '=', self.id)],
            'context': {'default_device_id': self.id},
        }

    def action_test_device(self):
        self.ensure_one()
        # Create a test event
        self.env['fire_alarm.event'].create({
            'name': _('Test Event - %s') % self.name,
            'device_id': self.id,
            'event_type': 'test',
            'event_time': fields.Datetime.now(),
            'severity': 'low',
            'description': _('Manual test initiated by %s') % self.env.user.name,
        })
        # Set device to testing status
        self.write({'status': 'testing'})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Device Test'),
                'message': _('Test event created for device %s') % self.name,
                'sticky': False,
                'type': 'success',
            }
        }

    @api.depends('event_ids')
    def _compute_event_count(self):
        for record in self:
            record.event_count = len(record.event_ids)
