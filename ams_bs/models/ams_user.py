# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>, datetime
import json

from odoo import api, fields, models, Command, _
from odoo.exceptions import UserError, ValidationError


class AMSUser(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.user'
    _description = "AMS User"
    _inherit = 'ams_base.user'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # email = fields.Char()
    card_number = fields.Char()
    finger_quality = fields.Selection([
        ('20', 'Low Quality'),
        ('40', 'Medium Quality'),
        ('60', 'High Quality'),
        ('80', 'Highest Quality')  # Matches QUALITY_HIGHEST from your image
    ], string="Fingerprint Quality")

    bs_fp1_template0 = fields.Text(string="Fingerprint 1 Template 0")
    bs_fp1_template1 = fields.Text(string="Fingerprint 1 Template 1")
    bs_fp2_template0 = fields.Text(string="Fingerprint 2 Template 0")
    bs_fp2_template1 = fields.Text(string="Fingerprint 2 Template 1")

    bs_face_template = fields.Text(string="Face Template ")
    bs_face_json_text = fields.Text(string="Face JSON Text")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('ams_bs.device_group', string="Device Group")
    user_group_id = fields.Many2one('ams_bs.user_group', string="User Group")
    ac_group_ids = fields.Many2many('ams_bs.access_group', string="Access Groups")
    device_id = fields.Many2one('ams_bs.device', string="Enroll Device")
    card_ids = fields.One2many('ams_bs.card', 'user_id', string="Cards")
    employee_id = fields.Many2one('hr.employee', string="Employee")
    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('enroll_number', 'employee_id')
    def _onchange_enroll_number(self):
        for record in self:
            if record.employee_id:
                record.employee_id.enroll_number = record.enroll_number
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def _notify_push_success(self, message='', type='success'):
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': message,
                'type': type,
            }
        }

    def create(self, vals):

        if isinstance(vals, list):
            for val in vals:
                if val.get('enroll_number', 0) == 0:
                    val['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams_bs.seq_user') or 0)
        else:
            if vals.get('enroll_number', 0) == 0:
                vals['enroll_number'] = int(self.env['ir.sequence'].next_by_code('ams_bs.seq_user') or 0)

        res = super(AMSUser, self).create(vals)
        return res


    # endregion

    # region ---------------------- TODO[IMP]: Properties --------------------------------------
    @property
    def sync_fields(self):
        """Property to return array of field names that should be checked for sync. Override this property in inherited models."""
        return ['name', 'email','card_number', 'user_group_id', 'ac_group_ids', 'device_group_id' , 'bs_fp1_template0', 'bs_fp1_template1', 'bs_fp2_template0', 'bs_fp2_template1', 'bs_face_template', 'bs_face_json_text']

    @property
    def api_client(self):
        return self.env['ams_bs.biostar_user_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.user'].sudo()

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_push_user(self):
        """Push user data to BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url

        # Prepare user values using the API client
        user_vals = self.prepare_user_payload()
        user = self.api_client.get_user(base_url, user_id=self.enroll_number)

        if self.api_client.is_success_response(user):
            # Update existing user
            result =  self._update_user(base_url=base_url,
                                     user_vals=user_vals)  # self._notify_push_success(message='User updated successfully in BioStar')
        else:
            # Create a new user
            result = self._create_user(base_url=base_url,
                                     user_vals=user_vals)  # self._notify_push_success(message='User created successfully in BioStar')

        if self.synced and (self.bs_fp1_template0 or self.bs_fp2_template0):
            fp_response = self._set_fingerprint()
            if self.api_client.is_success_response(fp_response):
                self.reset_error()
            else:
                self.assign_error(fp_response)
        if self.synced and self.bs_face_json_text:
            face_response = self._set_face()
            if face_response:
                self.reset_error()
            else:
                self.assign_error(face_response)

        return result

    def action_scan_card(self):
        base_url = self.env.company.bs_base_url
        api = self.env['ams_bs.biostar_device_api_client'].sudo()

        response = api.scan_card(base_url, device_id=self.device_id.device_serial)

        if api.is_success_response(response):
            self.card_number = response.card.card_id
            return True
        else:
            self.assign_error(response)

    def action_link_users(self):
        # Logic to link users
        pass

    def action_unlink_users(self):
        # Logic to unlink users
        pass

    def action_scan_fp1(self):
        base_url = self.env.company.bs_base_url
        api = self.env['ams_bs.biostar_device_api_client'].sudo()

        # Default to medium quality if none set
        enroll_quality = self.finger_quality or '40'

        response = api.scan_fingerprint(
            base_url=base_url,
            device_id=self.device_id.device_serial,
            enroll_quality=enroll_quality
        )

        if api.is_success_response(response):
            # Assign scanned fingerprint data
            self.bs_fp1_template0 = response.fingerprint_template.template0 if response.fingerprint_template else ""
            self._notify_status(message='Fingerprint scanned successfully' , type='success')
            return True
        else:
            self.assign_error(response)
            self._notify_status(message='Fingerprint scanned failed' , type='danger')
            return False


    def action_scan_fp2(self):
        base_url = self.env.company.bs_base_url
        api = self.env['ams_bs.biostar_device_api_client'].sudo()

        # Default to medium quality if none set
        enroll_quality = self.finger_quality or '40'

        response = api.scan_fingerprint(
            base_url=base_url,
            device_id=self.device_id.device_serial,
            enroll_quality=enroll_quality
        )

        if api.is_success_response(response):
            # Assign scanned fingerprint data
            self.bs_fp2_template0 = response.fingerprint_template.template0 if response.fingerprint_template else ""
            self._notify_status(message='Fingerprint scanned successfully' , type='success')
            return True
        else:
            self.assign_error(response)
            self._notify_status(message='Fingerprint scanned failed' , type='danger')
            return False

    def action_scan_face(self):
        base_url = self.env.company.bs_base_url
        api = self.env['ams_bs.biostar_device_api_client'].sudo()
        
        response = api.scan_face(base_url=base_url,device_id= self.device_id.device_serial)
        if api.is_success_response(response):
            self.bs_face_json_text = json.dumps(response.to_dict())
            self._notify_status(message='Face scanned successfully', type='success')
            return True
        else:
            self.assign_error(response)
            self._notify_status(message='Face scan failed', type='danger')
            return False

    def _set_face(self):
        base_url = self.env.company.bs_base_url
        response = self.api_client.set_face(base_url, self.enroll_number, self.bs_face_json_text)
        return response

    def _set_fingerprint(self):
        base_url = self.env.company.bs_base_url
        response = self.api_client.set_fingerprint(base_url, self.enroll_number, self.bs_fp1_template0, self.bs_fp2_template0)
        return response




    def action_remove_fp1(self):
        if self.bs_fp1_template0:
            self.bs_fp1_template0 = ""
            self.bs_fp1_template1 = ""

    def action_remove_fp2(self):
        if self.bs_fp2_template0:
            self.bs_fp2_template0 = ""
            self.bs_fp2_template1 = ""

    def action_remove_face(self):
        pass


    # endregion

    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    def _sync_users(self, base_url):
        # Logic to sync users
        response = self.api_client.get_users(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.user_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def action_sync_user(self):
        """Sync single user from BioStar system"""
        base_url = self.env.company.bs_base_url
        user_id = self.enroll_number

        response = self.api_client.get_user(base_url, user_id=user_id)
        if self.api_client.is_success_response(response):
            self._sync_record(response_record=response.user)
        else:
            self.synced = False
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        """Convert Response Record (Row) to Odoo Values"""
        self = self.origin_model if origin else self  # in case search from origin model
        device_group = (
            response_record.permission.filter.device_group[0]
            if response_record.permission and response_record.permission.filter and response_record.permission.filter.device_group
            else False
        )
        access_group_ids = [group.id for group in response_record.access_groups_in_user_group]

        # Prepare common values for both origin and non-origin models
        vals = {
            'name': response_record.name,
            'enroll_number': response_record.user_id,
            'record_id': response_record.user_id,
            'user_type': 'employee' if response_record.user_group_id.id == '1' else 'visitor',
            'start_datetime': response_record.start_datetime,
            'end_datetime': response_record.expiry_datetime,
            'device_group_id': self.device_group_id.get_res_id(device_group) if device_group else False,
            'user_group_id': self.user_group_id.get_res_id(response_record.user_group_id.id),
            'ac_group_ids': [Command.link(id) for id in self.ac_group_ids.get_res_ids(access_group_ids)],
            'activate': response_record.disabled == 'false',
            'email': response_record.email
        }

        # Add fingerprint template fields only for the ams_bs.user model (not for origin model)
        if not origin:
            fp_templates = response_record.fingerprint_templates or []
            bs_fp1_template0 = ''
            bs_fp1_template1 = ''
            bs_fp2_template0 = ''
            bs_fp2_template1 = ''

            if len(fp_templates) >= 2:
                # Use second last and last objects
                bs_fp1_template0 = fp_templates[-2].template0 or ''
                bs_fp1_template1 = fp_templates[-2].template1 or ''
                bs_fp2_template0 = fp_templates[-1].template0 or ''
                bs_fp2_template1 = fp_templates[-1].template1 or ''
            elif len(fp_templates) == 1:
                # Only one template, fill first pair, leave second blank
                bs_fp1_template0 = fp_templates[0].template0 or ''
                bs_fp1_template1 = fp_templates[0].template1 or ''

            vals.update({
                'bs_fp1_template0': bs_fp1_template0,
                'bs_fp1_template1': bs_fp1_template1,
                'bs_fp2_template0': bs_fp2_template0,
                'bs_fp2_template1': bs_fp2_template1
            })

        return vals

    def prepare_user_payload(self) -> dict:
        start = fields.Datetime.context_timestamp(self, self.start_datetime) if self.start_datetime \
            else fields.Datetime.start_of(datetime.now(), 'year')

        end = fields.Datetime.context_timestamp(self, self.end_datetime) if self.end_datetime \
            else fields.Datetime.end_of(datetime.now() + timedelta(days=365 * 10), 'year')

        user_payload = {
            "user_id": str(self.enroll_number),  # Use the enroll_number for user_id
            "user_group_id": {
                "id": str(self.user_group_id.record_id if self.user_group_id else "1")
            },
            "start_datetime": start.strftime("%Y-%m-%dT%H:%M:%S.00Z"),
            "expiry_datetime": end.strftime("%Y-%m-%dT%H:%M:%S.00Z")
            if self.end_datetime else "2030-12-31T23:59:00.00Z",
            "disabled": "false",
            "name": self.name,
            "email": self.email or "",
            # "login_id": str(self.enroll_number),
            # "permission": {
            #     "id": "1"
            # }
        }
        if self.ac_group_ids:
            groups_ids = [{'id': group.record_id} for group in self.ac_group_ids]
            # user_payload.update(access_groups=groups_ids)
            user_payload['access_groups'] = groups_ids

        return {
            "User": user_payload
        }

    def _create_user(self, base_url, user_vals):
        response = self.api_client.create_user(base_url, user_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.activate = True
            self.record_id = str(response.user_collection.rows[0].user_id)
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create user in origin_model
            ams_user = self.origin_model.create_or_update(self._prepare_ams_user_record_vals())
            return ams_user
        else:
            self.assign_error(response)

    def _update_user(self, base_url, user_vals):
        response = self.api_client.update_user(base_url, user_vals, self.enroll_number)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            ams_user = self.origin_model.create_or_update(self._prepare_ams_user_record_vals())
            return ams_user
        else:
            self.assign_error(response)

    def _prepare_ams_user_record_vals(self):
        """ prepare ams user record vals"""
        user_record_vals = {
            'name': self.name,
            'enroll_number': self.enroll_number,
            'record_id': self.enroll_number,
            'user_type': self.user_type,
            'start_datetime': self.start_datetime,
            'end_datetime': self.end_datetime,
            'activate': self.activate,
            'email': self.email,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date
        }
        # Update ac_group_ids field with Command.link or record IDs
        if self.ac_group_ids:
            user_record_vals['ac_group_ids'] = self.env['ams.access_group'].get_res_ids(self.ac_group_ids.record_ids)

        if self.user_group_id:
            user_record_vals['user_group_id'] = self.env["ams.user_group"].get_res_id(self.user_group_id.record_id)

        return user_record_vals

    def action_delete_users(self):
        # Delete users in BioStar
        # Handle multiple user IDs if provided
        # Prevent deletion of restricted users
        user_id = ''
        user_ids_to_delete = [
            rec.enroll_number
            for rec in self
            if rec.synced and rec.enroll_number not in [1, 2]
        ]

        if len(self) > 1:
            user_id = "+".join([str(enroll_number) for enroll_number in user_ids_to_delete])
        elif len(self) == 1:
            user_id = str(self.enroll_number)
        if user_id == '':
            raise UserError(_("Users that you Select already deleted."))

        response = self.api_client.delete_user(self.env.company.bs_base_url, user_id)
        if self.api_client.is_success_response(response):
            deleted_user = self.filtered(lambda rec: rec.enroll_number in user_ids_to_delete)
            for user in deleted_user:
                user.synced = False
                user.activate = False
                user.card_ids.user_id = False
                user.card_ids.is_assigned = False
                origin_user = user.origin_model.get_record(user.record_id)
                if origin_user:
                    origin_user.write({'synced': False, 'activate': False})
                    origin_user.card_ids.user_id = False
                    origin_user.card_ids.is_assigned = False
            return self._notify_status(message='Users deleted successfully in BioStar', type='success')
        else:
            self.assign_error(response)






    # endregion
