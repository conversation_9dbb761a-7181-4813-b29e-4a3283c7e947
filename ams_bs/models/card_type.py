# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class CardType(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.card_type'
    _description = "Card Type"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    type = fields.Char(string="Type", required=True)
    mode = fields.Char(string="Mode", required=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchange ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_user_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.card_type'].sudo()

    def _sync_card_types(self, base_url):
        # Logic to sync device_group
        response = self.api_client.get_card_types(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.card_type_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        return {
            'record_id': response_record.id,
            'name': response_record.name,
            'type': response_record.type,
            'mode': response_record.mode if response_record.mode else None,

        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
