# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError

class Card(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_bs.card"
    _description = "Card"
    _inherit = 'ams_base.card'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    card_type_id = fields.Many2one('ams_bs.card_type', string="Card Type")
    user_id = fields.Many2one('ams_bs.user', string="User",tracking=True)

    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Properties --------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_user_api_client'].sudo()

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.card -> ams.card """
        return self.env['ams.card'].sudo()

    # endregion

    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    def _sync_cards(self, base_url):
        response = self.api_client.get_cards(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.card_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _assign_card_to_user(self, user_id, card_id):
        base_url = self.env.company.bs_base_url
        response = self.api_client.assign_card_to_user(base_url, user_id, card_id)
        if self.api_client.is_success_response(response):
            self.is_assigned = True
            self.last_sync_date = fields.Datetime.now()
        else:
            self.assign_error(response)

    def _prepare_record_vals(self, response_record, origin=False):
        """Convert Response Record (Row) to Odoo Values"""
        self = self.origin_model if origin else self  # in case search from origin model
        vals = {
            'name': response_record.card_id,
            'record_id': response_record.id,
            'card_number': response_record.card_id,
            'display_card_id': response_record.display_card_id,
            'status': response_record.status,
            'is_blocked': response_record.is_blocked,
            'is_assigned': response_record.is_assigned,
            'mobile_card': response_record.mobile_card,
            'issue_count': response_record.issue_count,
            'card_slot': response_record.card_slot,
            'card_mask': response_record.card_mask,
            'card_type_id': self.card_type_id.get_res_id(response_record.card_type.id),
        }

        if hasattr(response_record, 'user_id') and response_record.user_id is not None:
            vals['user_id'] = self.user_id.get_res_id(response_record.user_id.user_id)

        return vals

    def _prepare_ams_card_record_vals(self):
        """ prepare ams card record vals"""
        card_record_vals = {
            'name': self.name,
            'card_number': self.card_number,
            'state': self.state,
            'is_assigned': self.is_assigned,
            'display_card_id': self.display_card_id,
            'card_slot': self.card_slot,
            'card_mask': self.card_mask,
            'mobile_card': self.mobile_card,
            'is_blocked': self.is_blocked,
            'issue_count': self.issue_count,
            'status': self.status,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
            'record_id': self.record_id
        }

        if self.card_type_id:
            card_record_vals['card_type_id'] = self.env["ams.card_type"].get_res_id(self.card_type_id.record_id),

        if self.user_id:
            card_record_vals['user_id'] = self.env["ams.user"].get_res_id(self.user_id.record_id),

        return card_record_vals

    def _prepare_card_payload(self) -> dict:
        """
        Prepare card values
        """
        return {
            "CardCollection": {
                "rows": [
                    {
                        "card_id": self.card_number,
                        "card_type": {
                            "id": str(self.card_type_id.record_id),
                            "type": str(self.card_type_id.record_id)
                        }
                    }
                ]
            }
        }

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_push_card(self):
        base_url = self.env.company.bs_base_url
        payload = self._prepare_card_payload()
        response = self.api_client.create_card(base_url, payload)

        if self.api_client.is_success_response(response):
            self.synced = True
            self.record_id = str(response.card_collection.rows[0].id)
            self.last_sync_date = fields.Datetime.now()
            # assign card to user
            if self.user_id:
                card_id = str(self.record_id)
                user_id = str(self.user_id.record_id)
                self._assign_card_to_user(user_id, card_id)
            # Create user in origin_model
            ams_card = self.origin_model.create_or_update(self._prepare_ams_card_record_vals())
            return ams_card
        else:
            self.assign_error(response)

    def action_delete_card(self):
        base_url = self.env.company.bs_base_url
        card_id=self.record_id
        response = self.api_client.delete_card(base_url, card_id=card_id)
        if self.api_client.is_success_response(response):
            return self._notify_status(message='Card deleted successfully in BioStar',type='danger')
        else:
            raise UserError(response.message)

    @property
    def sync_fields(self):
        """Override sync_fields property to define fields for card synchronization"""
        return [
            "card_number",
            "user_id",
            "card_type",
            "status",
            "issue_date",
            "expiry_date",
        ]

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
