# -*- coding: utf-8 -*-


from odoo import api, fields, models


class Schedule(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.schedule'
    _description = "Schedule"
    _inherit = 'ams_base.api_model_chatter'

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    schedule_type = fields.Selection(selection=[('daily', 'Daily'), ('weekly', 'Weekly')], default='weekly',
                                     required=True, tracking=True)
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_sync_schedule(self):
        """Sync single schedule from BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url
        schedule_id = self.record_id

        response = self.api_client.get_ac_schedule(base_url, schedule_id=schedule_id)
        if self.api_client.is_success_response(response):
            self._sync_record(response_record=response.schedule)
        else:
            self.synced = False
            self.assign_error(response)

        return response
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_ac_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.schedule'].sudo()

    def _sync_schedules(self, base_url):
        response = self.api_client.get_ac_schedules(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.schedule_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        return {
            'name': response_record.name,
            'description': response_record.description,
            'record_id': response_record.id,
        }

    def prepare_schedule_payload(self, is_update=False):
        """
        Prepare schedule payload for BioStar API
        
        Args:
            is_update (bool): Whether this payload is for updating an existing schedule
        
        Returns:
            dict: The prepared payload
        """
        self.ensure_one()
        
        # Basic schedule data
        schedule_payload = {
            "name": self.name,
            "description": self.description or ""
        }
        
        # In the future, we'll add more fields based on schedule_type
        # For now, we're just using the basic fields
        
        return {
            "Schedule": schedule_payload
        }

    def _create_schedule(self, base_url, schedule_vals):
        """Create schedule in BioStar system"""
        response = self.api_client.create_schedule(base_url, schedule_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create schedule in origin_model
            ams_schedule = self.origin_model.create_or_update(self._prepare_ams_schedule_record_vals())
            return ams_schedule
        else:
            self.assign_error(response)
            return False

    def _update_schedule(self, base_url, schedule_vals):
        """Update schedule in BioStar system"""
        response = self.api_client.update_schedule(base_url, str(self.record_id), schedule_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Update schedule in origin_model
            ams_schedule = self.origin_model.create_or_update(self._prepare_ams_schedule_record_vals())
            return ams_schedule
        else:
            self.assign_error(response)
            return False

    def _prepare_ams_schedule_record_vals(self):
        """Prepare values for creating/updating schedule record in origin model"""
        schedule_record_vals = {
            'name': self.name,
            'description': self.description,
            'record_id': self.record_id,
            'schedule_type': self.schedule_type,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date
        }
        
        return schedule_record_vals

    def action_push_schedule(self):
        """Push schedule data to BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url
        
        # Prepare schedule values
        schedule_vals = self.prepare_schedule_payload()
        
        # Check if schedule exists
        schedule = self.api_client.get_ac_schedule(base_url, schedule_id=str(self.record_id))
        
        if self.api_client.is_success_response(schedule):
            # Update existing schedule
            result = self._update_schedule(base_url=base_url, schedule_vals=schedule_vals)
        else:
            # Create a new schedule
            result = self._create_schedule(base_url=base_url, schedule_vals=schedule_vals)
        
        return result

    def action_delete_schedule(self):
        """
        Delete schedule from BioStar system.
        """
        base_url = self.env.company.bs_base_url
        response = self.api_client.delete_schedule(base_url, schedule_id=str(self.record_id))
        if self.api_client.is_success_response(response):
            self.synced = False
            self.need_to_sync = True
            self.record_id = False
            return self._notify_status(message='Schedule deleted successfully in BioStar', type='success')
        else:
            return self.assign_error(response)
    # endregion