# -*- coding: utf-8 -*-
from odoo import api, fields, models
import logging



class ResCompany(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "res.company"
    _inherit = ["res.company" , "ams_base.api_model"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    bs_base_url = fields.Char(string="BioStar Base Url")
    api_username = fields.Char(string="User Name")
    api_password = fields.Char()
    api_token = fields.Char()
    api_token_date = fields.Datetime()
    bs_last_event_log_datetime = fields.Datetime(
        string="Last Event Log Sync",
        help="Datetime of the last synced event from BioStar"
    )

    # endregion

    # region  Special
    # endregion

    # region  Relational

    # endregion
    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------




    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
