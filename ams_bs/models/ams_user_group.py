# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.ams_bs.api.dto_response import BaseResponse, Response


class AMSUserGroup(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.user_group'
    _description = "User Group"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    parent_id = fields.Many2one('ams_bs.user_group', string="Parent Group")
    user_ids = fields.One2many('ams_bs.user', 'user_group_id', string="Users")

    # endregion

    # region  Computed
    # endregion
    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_user_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.user_group'].sudo()

    def _sync_user_groups(self, base_url):
        # Logic to sync user groups
        response = self.api_client.get_user_groups(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.user_group_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)
        return response

    def action_sync_user_group(self):
        """Sync single user group from BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url
        group_id = int(self.record_id) if self.record_id else None

        response = self.api_client.get_user_group(base_url, group_id=group_id)

        if self.api_client.is_success_response(response):
            if response.user_group_collection.rows:
                self._sync_record(response_record=response.user_group_collection.rows[0])
            else:
                self.synced = False
                self.assign_error(response)
        else:
            self.synced = False
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        """Convert Response Record (Row) to Odoo Values"""
        self = self.origin_model if origin else self  # in case search from origin model
        vals = {
            'name': response_record.name,
            'description': response_record.description,
            'record_id': response_record.id
        }

        if hasattr(response_record, 'parent_id') and response_record.parent_id is not None:
            vals['parent_id'] = self.parent_id.get_res_id(response_record.parent_id.id)

        return vals

    def action_push_user_group(self):
        """Push user group data to BioStar system"""
        self.ensure_one()
        base_url = self.env.company.bs_base_url

        # Prepare user group values using the API client
        user_group_vals = self.prepare_user_group_payload()

        if self.synced == True:
            return self._update_user_group(base_url=base_url, user_vals=user_group_vals)
        else:
            return self._create_user_group(base_url=base_url, user_vals=user_group_vals)

    def prepare_user_group_payload(self) -> dict:
        """ prepare ams user group record vals """
        user_group_payload = {
            "parent_id": {
                "id": str(self.parent_id.record_id if self.parent_id else "1")
            },
            "name": self.name

        }

        return {
            "UserGroup": user_group_payload
        }

    def _create_user_group(self, base_url, user_vals):
        response = self.api_client.create_user_group(base_url, user_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = str(response.user_group.id)
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create user group in origin_model
            ams_user_group = self.origin_model.create_or_update(self._prepare_ams_user_group_record_vals())
            return ams_user_group
        else:
            self.assign_error(response)

    def _update_user_group(self, base_url, user_vals):
        response = self.api_client.update_user_group(base_url, user_vals, self.record_id)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            ams_user_group = self.origin_model.create_or_update(self._prepare_ams_user_group_record_vals())
            return ams_user_group
        else:
            self.assign_error(response)

    def action_delete_user_group(self):
        base_url = self.env.company.bs_base_url
        response = self.api_client.delete_user_group(base_url, self.record_id)
        if self.api_client.is_success_response(response):
            self.synced = False  # update list or one record
        else:
            self.assign_error(response)

    def _prepare_ams_user_group_record_vals(self):
        """ prepare ams user group record vals"""
        user_record_vals = {
            'name': self.name,
            'description': self.description,
            'record_id': self.record_id,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date

        }
        if self.parent_id:
            user_record_vals['parent_id'] = self.env["ams.user_group"].get_res_id(self.parent_id.record_id)
        return user_record_vals

    @property
    def sync_fields(self):
        """Override sync_fields property to define fields for user group synchronization"""
        return [
            "name",
            "description",
            "start_datetime",
            "end_datetime",
            "permission_ids",
        ]

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
