# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class DeviceGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.device_group'
    _description = "Device Group"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    parent_id = fields.Many2one('ams_bs.device_group', string="Parent Group")
    device_ids = fields.One2many('ams_bs.device', 'device_group_id', string="Devices")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_device_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.device_group'].sudo()

    @property
    def sync_fields(self):
        """Override sync_fields property to define fields for device group synchronization"""
        return ['name', 'description', 'device_ids']

    def _sync_device_groups(self,base_url):
        # Logic to sync device_group
        response = self.api_client.get_device_groups(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.device_group_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record,origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        vals = {
            'name': response_record.name,
            'record_id': response_record.id
        }
        if hasattr(response_record, 'parent_id') and response_record.parent_id is not None:
            vals['parent_id'] = self.parent_id.get_res_id(response_record.parent_id.id)

        return vals

    def prepare_device_group_payload(self) -> dict:
        """Prepare the payload for creating a device group."""
        return {
            "DeviceGroup": {
                "name": self.name,
                "description": self.description or "",
                "parent_id": {"id": self.parent_id.record_id} if self.parent_id else None
            }
        }

    def _prepare_ams_device_group_record_vals(self):
        vals = {
            'name': self.name,
            'description': self.description or "",
            'record_id': self.record_id,
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
            'need_to_sync': False
        }

        # Add parent_id if it exists
        if self.parent_id:
            vals['parent_id'] = self.env["ams.device_group"].get_res_id(self.parent_id.record_id)

        return vals


    def _create_device_group(self, base_url, payload):
        """
        Create a device group in BioStar system

        Returns:
            Response object with success or error information
        """
        self.ensure_one()

        # Call API to create device group
        response = self.api_client.create_device_group(base_url, payload)

        if self.api_client.is_success_response(response):
            # Update record with API response data
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            # Create device group in origin_model
            ams_device_group = self.origin_model.create_or_update(self._prepare_ams_device_group_record_vals())
            return ams_device_group
        else:
            self.assign_error(response)

    def action_push_device_group(self):
        """
        Action to push device group to BioStar system
        This is a one-time push as there is no update endpoint for device groups
        """
        self.ensure_one()

        # Check if already synced
        if not self.synced:
            # Create device group
            base_url = self.env.company.bs_base_url
            payload = self.prepare_device_group_payload()
            self._create_device_group(base_url, payload)
        else:
            raise UserError(_("Device Group is already Exist in BioStar , you can't update it ."))

    def action_delete_device_group(self):
        """
        Delete a device group from BioStar system

        This method calls the API client to delete the device group
        and updates the record status if successful.
        """
        self.ensure_one()

        # Check if the device group is synced (has a record_id)
        if not self.synced or not self.record_id:
            raise UserError(_("No Device Group with valid record ID found to delete."))

        # Call API to delete device group
        base_url = self.env.company.bs_base_url
        response = self.api_client.delete_device_group(base_url, self.record_id)

        if self.api_client.is_success_response(response):
            # Store record_id before setting it to False
            old_record_id = self.record_id

            # Update record status
            self.synced = False
            self.need_to_sync = True
            self.record_id = False

            # Update origin model record if it exists
            origin_record = self.origin_model.get_record(old_record_id)
            if origin_record:
                origin_record.write({
                    'synced': False,
                    'need_to_sync': True,
                    'record_id': False,
                })

            return self._notify_status(message='Device group deleted successfully from BioStar', type='success')
        else:
            self.assign_error(response)

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------


    # endregion
