from odoo import api, fields, models

class BaseDevicePort(models.AbstractModel):
    _name = 'ams_bs.base_device_port'
    _description = 'Base Device Port'
    _inherit = ['ams_base.api_model']

    # _sql_constraints = [('unique_device_port', 'unique(device_id, port)', 'Device Port must be unique')]

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    device_number = fields.Char(related='device_id.device_serial', string="Device No",
                                help="Device Serial Number", index=True, store=True)
    port = fields.Integer(string="Port")
    type = fields.Selection(string="Switch Type",
                            selection=[('0', 'Normally Open'), ('1', 'Normally Closed')])
    # endregion

    # region  Relational
    device_id = fields.Many2one('ams_bs.device', string="Device")

    # @property
    # def device_id(self):
    #     try:
    #         return int(self.device_id.id)
    #     except:
    #         return 0

     # endregion

     # region ---------------------- TODO[IMP]: Compute Methods ---------------------------------

    def _compute_display_name(self):
        for record in self:
            name = record.name  #
            # str(record.device_number) + ' - ' + str(record.port)
            if record.device_id and name:
                name = f"[{record.device_id.name}] {record.name}"

            record.display_name = name
    # endregion