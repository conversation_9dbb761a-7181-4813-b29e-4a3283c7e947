# -*- coding: utf-8 -*-
import uuid

from odoo import api, fields, models


class DoorSchedule(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.door_schedule'
    _description = "Door Schedule"
    _inherit = 'ams_base.api_model_chatter'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    door_record_id = fields.Integer(string="Door Record")
    schedule_record_id = fields.Integer(string="Schedule Record")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    door_id = fields.Many2one('ams_bs.door', string="Door", required=True)
    schedule_id = fields.Many2one('ams_bs.schedule', string="Schedule", required=True)
    ac_level_id = fields.Many2one('ams_bs.access_level', string="Access Level")
    # endregion

    # region  Computed
    name = fields.Char(string="Name", compute='_compute_name', store=True, readonly=False)

    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    @api.depends('door_id', 'schedule_id', 'ac_level_id')
    def _compute_name(self):
        for record in self:
            if record.ac_level_id and record.door_id and record.schedule_id:
                record.name = f"{record.ac_level_id.name} | {record.door_id.name} , {record.schedule_id.name} | {uuid.uuid4().hex[:6]}"
            else:
                record.name = "N/A Ac Level"

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
