# -*- coding: utf-8 -*-


from odoo import api, fields, models, Command


class AccessGroup(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.access_group'
    _description = "Access Group"
    _inherit = 'ams_base.access_group'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    api_type = fields.Selection([('suprema', 'Suprema'), ('zk', 'ZKTeco')], string="API Type", default='suprema')
    # endregion

    # region  Special
    # endregion

    # region  Relational
    ac_levels_ids = fields.Many2many('ams_bs.access_level', string="Access Levels")
    device_ids = fields.Many2many('ams_bs.device', string="Devices")
    group_users_ids = fields.One2many('ams_bs.access_group_user', 'ac_group_id', string="Users")
    user_groups_ids = fields.One2many('ams_bs.access_group_user_group', 'ac_group_id', string="User Groups")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_ac_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.access_group'].sudo()

    def _sync_access_groups(self, base_url):
        response = self.api_client.get_ac_groups(base_url)
        # Check if the response is valid
        if self.api_client.is_success_response(response):
            for rec in response.access_group_collection.rows:
                ac_group = self._sync_record(rec)
                if rec.users:
                    self.link_users(ac_group, rec.users)
                    self.link_origin_model_users(rec.id, rec.users)
                if rec.user_groups:
                    self.link_user_groups(ac_group, rec.user_groups)
                    self.link_origin_model_user_groups(rec.id, rec.user_groups)
        else:
            self.assign_error(response)

        return response

    def link_origin_model_users(self, ac_group_id, users):
        ac_group = self.env['ams.access_group'].search([('record_id', '=', ac_group_id)], limit=1)
        if ac_group:
            for user in users:
                user_record = self.env['ams.user'].search([('record_id', '=', user.user_id)], limit=1)
                if user_record:
                    access_group_user = self.env['ams.access_group_user'].search(
                        [('user_id.id', '=', user_record.id)],
                        limit=1)
                    if access_group_user:
                        access_group_user.ac_group_id = ac_group.id
                    else:
                        combined_name = f"{user.name} - {ac_group.name}"

                        access_group_user = self.env['ams.access_group_user'].create(
                            {'user_id': user_record.id, 'ac_group_id': ac_group.id, 'record_id': user.user_id,
                             'name': combined_name})

    def link_origin_model_user_groups(self, ac_group_id, user_groups):
        ac_group = self.env['ams.access_group'].search([('record_id', '=', ac_group_id)], limit=1)
        if ac_group:
            for group in user_groups:
                group_record = self.env['ams.user_group'].search([('record_id', '=', group.id)], limit=1)
                if group_record:
                    access_group_user_group = self.env['ams.access_group_user_group'].search(
                        [('user_group_id.id', '=', group_record.id)],
                        limit=1)
                    if access_group_user_group:
                        access_group_user_group.ac_group_id = ac_group.id
                    else:
                        combined_name = f"{group.name} - {ac_group.name}"
                        access_group_user_group = self.env['ams.access_group_user_group'].create(
                            {'user_group_id': group_record.id, 'ac_group_id': ac_group.id, 'record_id': group.id,
                             'name': combined_name})

    def link_users(self, ac_group, users):
        for user in users:
            user_record = self.env['ams_bs.user'].search([('record_id', '=', user.user_id)], limit=1)
            if user_record:
                access_group_user = self.env['ams_bs.access_group_user'].search(
                    [('user_id.id', '=', user_record.id)],
                    limit=1)
                if access_group_user:
                    access_group_user.ac_group_id = ac_group.id
                else:
                    # combined_name = f"{user.name} - {ac_group.name}"
                    access_group_user = self.env['ams_bs.access_group_user'].create(
                        {'user_id': user_record.id, 'ac_group_id': ac_group.id, 'record_id': user.user_id})

    def link_user_groups(self, ac_group, user_groups):
        for group in user_groups:
            user_group_record = self.env['ams_bs.user_group'].search([('record_id', '=', group.id)], limit=1)

            if user_group_record:
                access_group_user_group = self.env['ams_bs.access_group_user_group'].search(
                    [('user_group_id.id', '=', user_group_record.id)],
                    limit=1)
                if access_group_user_group:
                    # Update the existing record if necessary
                    access_group_user_group.ac_group_id = ac_group.id
                else:
                    # combined_name = f"{group.name} - {ac_group.name}"
                    access_group_user_group = self.env['ams_bs.access_group_user_group'].create({
                        'user_group_id': user_group_record.id,
                        'ac_group_id': ac_group.id,
                        'record_id': group.id,

                    })

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        level_ids = [level.id for level in response_record.access_levels]

        return {
            'name': response_record.name,
            'description': response_record.description,
            'record_id': response_record.id,
            'api_type': 'suprema',
            'ac_levels_ids': [Command.link(id) for id in self.ac_levels_ids.get_res_ids(level_ids)],

        }

    def _create_access_group(self, base_url, user_vals):
        response = self.api_client.create_access_group(base_url, user_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.synced = True
            self.record_id = response.id
            self.last_sync_date = fields.Datetime.now()
            self.api_type = 'suprema'
            self.need_to_sync = False
            # Create access group in origin_model
            ams_access_group = self.origin_model.create_or_update(self._prepare_ams_access_group_record_vals())
            return ams_access_group

        else:
            self.assign_error(response)

    def _update_access_group(self, base_url, user_vals):
        response = self.api_client.update_access_group(base_url, self.record_id, user_vals)
        if self.api_client.is_success_response(response):
            self.reset_error()
            self.last_sync_date = fields.Datetime.now()
            self.need_to_sync = False
            ams_access_group = self.origin_model.create_or_update(self._prepare_ams_access_group_record_vals())
            return ams_access_group
        else:
            self.assign_error(response)

    def prepare_access_group_payload(self) -> dict:
        """Prepare the payload for creating an access group."""
        users = [
            {"user_id": user.user_id.record_id} for user in self.group_users_ids if
            user.user_id and user.user_id.record_id
        ]

        user_groups = [
            {"id": user_group.user_group_id.record_id, "name": user_group.user_group_id.name}
            for user_group in self.user_groups_ids
            if user_group.user_group_id and user_group.user_group_id.record_id
        ]

        access_levels = [
            {"id": level.id} for level in self.ac_levels_ids
        ]

        # Prepare the access group payload
        access_group = {
            "name": self.name,
            "description": self.description or "",
            "users": users or [],
            "access_levels": access_levels or [],
            "user_groups": user_groups,
        }

        return {
            "AccessGroup": access_group
        }

    def _prepare_ams_access_group_record_vals(self):
        access_group_record_vals = {
            'name': self.name,
            "description": self.description or "",
            'synced': self.synced,
            'last_sync_date': self.last_sync_date,
            'api_type': self.api_type
        }
        # Update ac_group_ids field with Command.link or record IDs
        if self.ac_levels_ids:
            access_group_record_vals['ac_levels_ids'] = self.env['ams.access_level'].get_res_ids(
                self.ac_levels_ids.record_ids)

        if self.group_users_ids:
            access_group_record_vals['group_users_ids'] = self.env["ams.access_group_user"].get_res_ids(
                self.group_users_ids.user_id.record_ids)

        if self.user_groups_ids:
            access_group_record_vals['user_groups_ids'] = self.env["ams.access_group_user_group"].get_res_ids(
                self.user_groups_ids.user_group_id.record_ids)

        return access_group_record_vals

    def action_push_access_group(self):
        payload = self.prepare_access_group_payload()
        base_url = self.env.company.bs_base_url
        if self.synced == True:
            self._update_access_group(base_url, payload)
        else:
            self._create_access_group(base_url, payload)

    def action_delete_access_group(self):
        base_url = self.env.company.bs_base_url
        response = self.api_client.delete_access_group(base_url, self.record_id)
        if self.api_client.is_success_response(response):
            record_id_before_delete = self.record_id
            self.synced = False
            self.need_to_sync = False
            self.record_id = False
            # Update origin model if it exists
            origin_access_group = self.origin_model.get_record(record_id_before_delete)
            if origin_access_group:
                origin_access_group.write({
                    'synced': False,
                    'need_to_sync': True,
                    'record_id': False
                })
            # update list or one record
        else:
            self.assign_error(response)

    @property
    def sync_fields(self):
        """Override sync_fields property to define fields for access group synchronization"""
        return ["name", "description", "door_ids", "access_level_ids", "status"]

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
