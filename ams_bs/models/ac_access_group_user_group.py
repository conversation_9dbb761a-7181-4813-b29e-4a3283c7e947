# -*- coding: utf-8 -*-


from odoo import api, fields, models


class AccessGroupUserGroup(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.access_group_user_group'
    _inherit = 'ams_base.api_model_chatter'
    _description = "Access Group User Group"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    config_applied = fields.Boolean(string="Configuration Applied", default=False)
    name = fields.Char(string="Name", compute="_compute_name", store=True, readonly=True, required=False)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    user_group_id = fields.Many2one('ams_bs.user_group', string="User Group", required=True)
    ac_group_id = fields.Many2one('ams_bs.access_group', string="Access Group", required=True)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('user_group_id', 'ac_group_id')
    def _compute_name(self):
        for record in self:
            if record.user_group_id and record.ac_group_id:
                record.name = f"{record.user_group_id.name} - {record.ac_group_id.name} ({record.id})"
            else:
                record.name = "Undefined"
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
