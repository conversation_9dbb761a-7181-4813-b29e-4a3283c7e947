from odoo import api, fields, models

class DeviceType(models.Model):
    _name = 'ams_bs.device_type'
    _description = 'Device Type'
    _inherit = 'ams_base.api_model_chatter'



 # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    input_port_num = fields.Integer(string='Input Port Number')
    output_port_num = fields.Integer(string='Output Port Number')
    relay_num = fields.Integer(string='Relay Number')
    enable_face = fields.Bo<PERSON>an(string='Enable Fase')
    enable_fingerprint = fields.Boolean(string='Enable Fingerprint')
    enable_card = fields.Boolean(string='Enable Card')
    enable_wifi = fields.Boolean(string='Enable WiFi')
    # endregion

    # region  Special
    # endregion

    # region  Relational

    # endregion

    # region  Computed
    # endregion

    # region Actions

    # endregion

    # region API Business
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_device_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module """
        return self.env['ams.device_type'].sudo()

    def _sync_device_types(self, base_url):
        # Logic to sync device_group
        response = self.api_client.get_device_types(base_url)
        if self.api_client.is_success_response(response):
            for rec in response.device_type_collection.rows:
                self._sync_record(rec)
        else:
            self.assign_error(response)

        return response

    def _prepare_record_vals(self, response_record, origin=False):
        self = self.origin_model if origin else self  # in case search from origin model
        return {
            'record_id': response_record.id,
            'name': response_record.name,
            'description': response_record.description,
            'input_port_num': response_record.input_port_num,
            'output_port_num': response_record.output_port_num,
            'relay_num': response_record.relay_num,
            'enable_face': response_record.face,
            'enable_fingerprint': response_record.fingerprint,
            'enable_card': response_record.card,
            'enable_wifi': response_record.wifi
        }

    # endregion

