# -*- coding: utf-8 -*-
import os
import json
import time

from datetime import datetime, timedelta, timezone
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class ObjectModel(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_bs.event_log'
    _description = "Event Log"
    _inherit = 'ams_base.event_log'

    _event_code_map = None

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_execute_punch_log(self):
        """
        Action method to manually trigger punch log creation
        """
        for record in self:
            if record.is_punch_log:
                punch_log = record._create_punch_log(
                    enroll_number=record.enroll_number,
                    device_serial=record.device_serial,
                    log_date=record.log_date
                )

        if punch_log:
            # Return action to show created punch logs
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Punch Log',
                    'message': 'punch log was created',
                    'sticky': False,
                    'type': 'success',
                }
            }
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Punch Log',
                'message': 'No punch logs were created',
                'sticky': False,
                'type': 'warning',
            }
        }
    @property
    def api_client(self):
        return self.env['ams_bs.biostar_device_api_client'].sudo()  #

    @property
    def origin_model(self):
        """ return mapping for origin  model in access management module example ams_bs.user_group -> ams.user_group """
        return self.env['ams.event_log'].sudo()

    @staticmethod
    def get_events_payload(start_datetime=None, end_datetime=None):
        """
        Get events payload with datetime filter and specific date range
        Args:
            start_datetime: Start datetime for the range
            end_datetime: End datetime for the range
        Returns:
            dict: Query payload for BioStar API
        """
        if not start_datetime:
            # Default to start of current day instead of month
            start_datetime = datetime.now(timezone.utc).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        
        if isinstance(start_datetime, datetime):
            start_datetime = start_datetime.strftime('%Y-%m-%dT%H:%M:%S.000Z')
            
        if not end_datetime:
            end_datetime = datetime.now(timezone.utc)
            
        if isinstance(end_datetime, datetime):
            end_datetime = end_datetime.strftime('%Y-%m-%dT%H:%M:%S.000Z')

        # Convert string dates back to datetime for comparison
        start_dt = datetime.strptime(start_datetime, '%Y-%m-%dT%H:%M:%S.000Z')
        end_dt = datetime.strptime(end_datetime, '%Y-%m-%dT%H:%M:%S.000Z')
        
        if start_dt > end_dt:
            raise ValidationError(_("Start date cannot be greater than end date."))

        return {
            "Query": {
                "limit": 100,
                "conditions": [{
                    "column": "datetime",
                    "operator": 3,  # BETWEEN operator
                    "values": [start_datetime, end_datetime]
                }],
                "orders": [
                    {
                        "column": "datetime",
                        "descending": False  # Get oldest first for proper pagination
                    }
                ]
            }
        }

    def _sync_events(self, base_url):
        """
        Sync events from BioStar using time windows to prevent timeouts.
        
        Args:
            base_url (str): Base URL for the BioStar API
            
        Returns:
            bool or Response: False if sync failed, Response object if successful
        """
        LIMIT = 100
        WINDOW_HOURS = 24  # Process 24 hours at a time
        
        try:
            last_sync_datetime = self.env.company.bs_last_event_log_datetime
            if not last_sync_datetime:
                last_sync_datetime = datetime.now(timezone.utc).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
            else:
                last_sync_datetime = last_sync_datetime.replace(tzinfo=timezone.utc)

            current_time = datetime.now(timezone.utc)
            if last_sync_datetime >= current_time:
                return True  # Nothing to sync

            code_name_map = self._get_event_code_map()
            newest_event_datetime = None
            window_start = last_sync_datetime
            processed_count = 0
            max_processed = 10000  # Safety limit for total records
            response = None

            while window_start < current_time and processed_count < max_processed:
                window_end = min(
                    window_start + timedelta(hours=WINDOW_HOURS),
                    current_time
                )

                payload = self.get_events_payload(window_start, window_end)
                payload['Query']['limit'] = LIMIT

                while True:
                    response = self.api_client.get_events(base_url, payload)
                    if not response:
                        self._logger.error("Received empty response from API")
                        return False

                    if not hasattr(response, 'event_collection') or not response.event_collection:
                        self._logger.error(f"Invalid response format: {response}")
                        return False

                    if not self.api_client.is_success_response(response):
                        self.assign_error(response)
                        return False

                    if not response.event_collection.rows:
                        break

                    batch_size = len(response.event_collection.rows)
                    processed_count += batch_size

                    # Process records in current batch
                    last_datetime = None
                    for rec in response.event_collection.rows:
                        if not rec.row_datetime:
                            continue

                        event_datetime = rec.row_datetime.replace(tzinfo=timezone.utc)
                        self._sync_record(rec, code_name_map=code_name_map)
                        last_datetime = event_datetime

                        if not newest_event_datetime or event_datetime > newest_event_datetime:
                            newest_event_datetime = event_datetime

                    if batch_size < LIMIT:
                        break

                    if last_datetime:
                        payload = self.get_events_payload(
                            last_datetime + timedelta(microseconds=1),
                            window_end
                        )
                        payload['Query']['limit'] = LIMIT

                window_start = window_end
                self.env.cr.commit()  # pylint: disable=invalid-commit
                time.sleep(0.1)  # Prevent API overload

            if newest_event_datetime:
                self.env.company.write({
                    'bs_last_event_log_datetime': newest_event_datetime.replace(tzinfo=None)
                })

            return response

        except Exception as e:
            self._logger.error(f"Error during event sync: {str(e)}")
            self.assign_error(e)
            return False

    def _prepare_record_vals(self, response_record, origin=False , **kwargs):
        self = self.origin_model if origin else self  # in case search from origin model
        event_code = str(response_record.event_type_id.code or '')
        code_name_map = kwargs.get('code_name_map', {})
        code_name = code_name_map.get(event_code, 'Unknown')

        return {
            'name': str(response_record.id),
            'record_id': response_record.id,
            'enroll_number': response_record.user_id.user_id if response_record.user_id else False,
            'event_datetime': response_record.row_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            'executed_datetime':response_record.row_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            'log_date':response_record.row_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            'event_id': response_record.index,
            'device_serial': response_record.device_id.id,
            'event_code': response_record.event_type_id.code,
            'code_name': code_name,
            'has_image': response_record.user_id.photo_exists,
            # TODO : complelet records

        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    import os
    import json
    import logging

    _logger = logging.getLogger(__name__)

    def _get_event_code_map(self):
        if self.__class__.__dict__.get('_event_code_map') is not None:
            return self.__class__._event_code_map

        # Build absolute path
        json_path = os.path.abspath(os.path.join(
            os.path.dirname(__file__), '..', 'data', '54_all_event_types_dict.json'
        ))

        # Check if the file exists
        if not os.path.exists(json_path):
            self._logger.error(f"[Event Sync] JSON file not found at: {json_path}")
            self.__class__._event_code_map = {}
            return self.__class__._event_code_map

        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                self.__class__._event_code_map = json.load(f)
                self._logger.info(f"[Event Sync] Loaded event code map from: {json_path}")
        except Exception as e:
            self._logger.exception(f"[Event Sync] Failed to load JSON file: {json_path}")
            self.__class__._event_code_map = {}

        return self.__class__._event_code_map

    # endregion
