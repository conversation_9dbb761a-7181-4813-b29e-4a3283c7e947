from .utils import *
from dataclasses import dataclass
from typing import Any

@dataclass
class Response:
    code: int
    link: str
    message: str
    id : int

    def __init__(self, code: int, link: str, message: str, id: int=0) -> None:
        self.code = code
        self.link = link
        self.message = message
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'Response':
        if not obj:
            return None
        code = to_int(from_str(obj.get("code")))
        link = from_str(obj.get("link"))
        message = from_str(obj.get("message"))
        return Response(code, link, message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["code"] = from_str(str(self.code))
        result["link"] = from_str(self.link)
        result["message"] = from_str(self.message)
        return result

class BaseResponse:
    response: Response