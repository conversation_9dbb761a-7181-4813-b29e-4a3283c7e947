from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any, List, TypeVar, Type, cast, Callable


class CardType:
    id: int
    name: str
    type: int

    def __init__(self, id: int, name: str, type: int) -> None:
        self.id = id
        self.name = name
        self.type = type

    @staticmethod
    def from_dict(obj: Any) -> 'CardType':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        type = to_int(from_str(obj.get("type")))
        return CardType(id, name, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["type"] = from_str(str(self.type))
        return result


class Card:
    card_type: CardType
    card_id: str
    id: int
    display_card_id: str

    def __init__(self, card_type: CardType, card_id: str, id: int, display_card_id: str) -> None:
        self.card_type = card_type
        self.card_id = card_id
        self.id = id
        self.display_card_id = display_card_id

    @staticmethod
    def from_dict(obj: Any) -> 'Card':
        if not obj:
            return None
        card_type = CardType.from_dict(obj.get("card_type"))
        card_id = from_str(obj.get("card_id"))
        id = to_int(from_str(obj.get("id")))
        display_card_id = from_str(obj.get("display_card_id"))
        return Card(card_type, card_id, id, display_card_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["card_type"] = to_class(CardType, self.card_type)
        result["card_id"] = from_str(self.card_id)
        result["id"] = from_str(str(self.id))
        result["display_card_id"] = from_str(self.display_card_id)
        return result


class Row:
    id: int
    code: int

    def __init__(self, id: int, code: int) -> None:
        self.id = id
        self.code = code

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        code = to_int(from_str(obj.get("code")))
        return Row(id, code)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["code"] = from_str(str(self.code))
        return result


class DeviceResponse(BaseResponse):
    rows: List[Row]
    result: bool

    def __init__(self, rows: List[Row], result: bool) -> None:
        self.rows = rows
        self.result = result

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceResponse':
        if not obj:
            return None
        rows = from_list(Row.from_dict, obj.get("rows"))
        result = from_stringified_bool(from_str(obj.get("result")))
        return DeviceResponse(rows, result)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        result["result"] = from_str(str(self.result).lower())
        return result


class ScanCardResponse(BaseResponse):
    card: Card
    device_response: DeviceResponse
    response: Response

    def __init__(self, card: Card, device_response: DeviceResponse, response: Response) -> None:
        self.card = card
        self.device_response = device_response
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'ScanCardResponse':
        if not obj:
            return None
        card = Card.from_dict(obj.get("Card"))
        device_response = DeviceResponse.from_dict(obj.get("DeviceResponse"))
        response = Response.from_dict(obj.get("Response"))
        return ScanCardResponse(card, device_response, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["Card"] = to_class(Card, self.card)
        result["DeviceResponse"] = to_class(DeviceResponse, self.device_response)
        result["Response"] = to_class(Response, self.response)
        return result


def scan_card_response_from_dict(s: Any) -> ScanCardResponse:
    return ScanCardResponse.from_dict(s)


def scan_card_response_to_dict(x: ScanCardResponse) -> Any:
    return to_class(ScanCardResponse, x)