from datetime import datetime, time
from typing import Any, List, Optional, TypeVar, Callable, Type, cast, Dict
import dateutil.parser
from enum import Enum

from collections import defaultdict, Counter

T = TypeVar("T")
EnumT = TypeVar("EnumT", bound=Enum)

DATETIME_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss"
DATE_DISPLAY_FORMAT = "dd/MM/yyyy"


def to_enum(c: Type[EnumT], x: Any) -> EnumT:
    assert isinstance(x, c)
    return x.value

def from_dict(f: Callable[[Any], T], x: Any) -> Dict[str, T]:
    assert isinstance(x, dict)
    return { k: f(v) for (k, v) in x.items() }

def sum_group_by(dataset, group_by_key, sum_value_keys):
    dic = defaultdict(Counter)

    for item in dataset:
        key = item[group_by_key]
        vals = {k: item[k] for k in sum_value_keys}
        dic[key].update(vals)
    return dic

def to_int(x: Any) -> int:
    if not x or x == "None" or x == "null" or x == "NaN" or x == "undefined":
        return 0
    return int(x)

def from_str(x: Any) -> str:
    if not x:
        return ''
    assert isinstance(x, str)
    return x


def from_datetime(x: Any) -> datetime:
    if x:
        try:
            return dateutil.parser.parse(x.replace('Z', ''))  # timezone already applied
        except:
            return datetime.min

    return datetime.min


def from_float(x: Any) -> float:
    assert isinstance(x, (float, int)) and not isinstance(x, bool)
    return float(x)


def to_float(x: Any) -> float:
    if not isinstance(x, float):
        print(x)
    assert isinstance(x, float), x
    return x


def from_int(x: Any) -> int:
    assert isinstance(x, int) and not isinstance(x, bool)
    return x


def from_bool(x: Any) -> bool:
    assert isinstance(x, bool)
    return x


def from_list(f: Callable[[Any], T], x: Any) -> List[T]:
    if isinstance(x, list):
        return [f(y) for y in x]
    else:
        return []


def to_class(c: Type[T], x: Any) -> dict:
    assert isinstance(x, c)
    return cast(Any, x).to_dict()


def from_none(x: Any) -> Any:
    assert x is None, f"{x} is null"
    return x


def from_union(fs, x):
    for f in fs:
        try:
            return f(x)
        except:
            pass
    assert False, x


def is_type(t: Type[T], x: Any) -> T:
    assert isinstance(x, t)
    return x


def from_stringified_bool(x: str) -> bool:
    if x == "true" or x == "True" or x == "1" or x == "yes" or x == "TRUE":
        return True
    else:
        return False
    #assert False
