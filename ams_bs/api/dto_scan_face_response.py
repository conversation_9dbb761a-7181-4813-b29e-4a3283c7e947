from .utils import *
from .dto_response import Response, BaseResponse
from typing import Any, List, TypeVar, Type, cast, Callable


class Template:
    template_ex: str
    credential_bin_type: int

    def __init__(self, template_ex: str, credential_bin_type: int) -> None:
        self.template_ex = template_ex
        self.credential_bin_type = credential_bin_type

    @staticmethod
    def from_dict(obj: Any) -> 'Template':
        if not obj:
            return None
        template_ex = from_str(obj.get("template_ex"))
        credential_bin_type = to_int(from_str(obj.get("credential_bin_type")))
        return Template(template_ex, credential_bin_type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["template_ex"] = from_str(self.template_ex)
        result["credential_bin_type"] = from_str(str(self.credential_bin_type))
        return result


class Face:
    template_ex_normalized_image: str
    templates: List[Template]
    flag: int

    def __init__(self, template_ex_normalized_image: str, templates: List[Template], flag: int) -> None:
        self.template_ex_normalized_image = template_ex_normalized_image
        self.templates = templates
        self.flag = flag

    @staticmethod
    def from_dict(obj: Any) -> 'Face':
        if not obj:
            return None
        template_ex_normalized_image = from_str(obj.get("template_ex_normalized_image"))
        templates = from_list(Template.from_dict, obj.get("templates"))
        flag = to_int(from_str(obj.get("flag")))
        return Face(template_ex_normalized_image, templates, flag)

    def to_dict(self) -> dict:
        result: dict = {}
        result["template_ex_normalized_image"] = from_str(self.template_ex_normalized_image)
        result["templates"] = from_list(lambda x: to_class(Template, x), self.templates)
        result["flag"] = from_str(str(self.flag))
        return result


class Credentials:
    faces: List[Face]

    def __init__(self, faces: List[Face]) -> None:
        self.faces = faces

    @staticmethod
    def from_dict(obj: Any) -> 'Credentials':
        if not obj:
            return None
        faces = from_list(Face.from_dict, obj.get("faces"))
        return Credentials(faces)

    def to_dict(self) -> dict:
        result: dict = {}
        result["faces"] = from_list(lambda x: to_class(Face, x), self.faces)
        return result


class ScanFaceResponse(BaseResponse):
    credentials: Credentials
    response: Response

    def __init__(self, credentials: Credentials, response: Response) -> None:
        self.credentials = credentials
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'ScanFaceResponse':
        if not obj:
            return None
        credentials = Credentials.from_dict(obj.get("credentials"))
        response = Response.from_dict(obj.get("Response"))
        return ScanFaceResponse(credentials, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["credentials"] = to_class(Credentials, self.credentials)
        result["Response"] = to_class(Response, self.response)
        return result


def scan_face_response_from_dict(s: Any) -> ScanFaceResponse:
    return ScanFaceResponse.from_dict(s)


def scan_face_response_to_dict(x: ScanFaceResponse) -> Any:
    return to_class(ScanFaceResponse, x)
