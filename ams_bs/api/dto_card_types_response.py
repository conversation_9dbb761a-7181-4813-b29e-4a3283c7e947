from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any


class Row:
    id: int
    name: str
    type: int
    mode: str

    def __init__(self, id: int, name: str, type: int, mode: str) -> None:
        self.id = id
        self.name = name
        self.type = type
        self.mode = mode

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        type = to_int(from_str(obj.get("type")))
        mode = from_str(obj.get("mode"))
        return Row(id, name, type, mode)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["type"] = from_str(str(self.type))
        result["mode"] = from_str(self.mode)
        return result

class CardTypeCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'CardTypeCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return CardTypeCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result

class CardTypesResponse(BaseResponse):
    card_type_collection: CardTypeCollection
    response: Response

    def __init__(self, card_type_collection: CardTypeCollection, response: Response) -> None:
        self.card_type_collection = card_type_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'CardTypesResponse':
        if not obj:
            return None
        card_type_collection = CardTypeCollection.from_dict(obj.get("CardTypeCollection"))
        response = Response.from_dict(obj.get("Response"))
        return CardTypesResponse(card_type_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["CardTypeCollection"] = to_class(CardTypeCollection, self.card_type_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def card_types_response_from_dict(s: Any) -> CardTypesResponse:
    return CardTypesResponse.from_dict(s)


def card_types_response_to_dict(x: CardTypesResponse) -> Any:
    return to_class(CardTypesResponse, x)
