from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any

class ScheduleID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'ScheduleID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return ScheduleID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class AccessLevelItem:
    id: int
    doors: List[ScheduleID]
    schedule_id: ScheduleID

    def __init__(self, id: int, doors: List[ScheduleID], schedule_id: ScheduleID) -> None:
        self.id = id
        self.doors = doors
        self.schedule_id = schedule_id

    @staticmethod
    def from_dict(obj: Any) -> 'AccessLevelItem':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        doors = from_list(ScheduleID.from_dict, obj.get("doors"))
        schedule_id = ScheduleID.from_dict(obj.get("schedule_id"))
        return AccessLevelItem(id, doors, schedule_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["doors"] = from_list(lambda x: to_class(ScheduleID, x), self.doors)
        result["schedule_id"] = to_class(ScheduleID, self.schedule_id)
        return result


class Row:
    id: int
    name: str
    description: str
    access_level_items: List[AccessLevelItem]

    def __init__(self, id: int, name: str, description: str, access_level_items: List[AccessLevelItem]) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.access_level_items = access_level_items

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        access_level_items = from_list(AccessLevelItem.from_dict, obj.get("access_level_items"))
        return Row(id, name, description, access_level_items)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["access_level_items"] = from_list(lambda x: to_class(AccessLevelItem, x), self.access_level_items)
        return result


class AccessLevelCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'AccessLevelCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return AccessLevelCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class AccessLevelsResponse(BaseResponse):
    access_level_collection: AccessLevelCollection
    response: Response

    def __init__(self, access_level_collection: AccessLevelCollection, response: Response) -> None:
        self.access_level_collection = access_level_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'AccessLevelsResponse':
        if not obj:
            return None
        access_level_collection = AccessLevelCollection.from_dict(obj.get("AccessLevelCollection"))
        response = Response.from_dict(obj.get("Response"))
        return AccessLevelsResponse(access_level_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["AccessLevelCollection"] = to_class(AccessLevelCollection, self.access_level_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def access_levels_response_from_dict(s: Any) -> AccessLevelsResponse:
    return AccessLevelsResponse.from_dict(s)


def access_levels_response_to_dict(x: AccessLevelsResponse) -> Any:
    return to_class(AccessLevelsResponse, x)