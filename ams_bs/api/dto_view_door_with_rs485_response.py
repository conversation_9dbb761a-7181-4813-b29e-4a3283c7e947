from .utils import *
from .dto_response import Response
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast

class DoorGroupID:
    id: int

    def __init__(self, id: int) -> None:
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'DoorGroupID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        return DoorGroupID(id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        return result


class EventFilter:
    rows: str
    total: int

    def __init__(self, rows: str, total: int) -> None:
        self.rows = rows
        self.total = total

    @staticmethod
    def from_dict(obj: Any) -> 'EventFilter':
        if not obj:
            return None
        rows = from_str(obj.get("rows"))
        total = to_int(from_str(obj.get("total")))
        return EventFilter(rows, total)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_str(self.rows)
        result["total"] = from_str(str(self.total))
        return result


class Input:
    supervised_inputs: str

    def __init__(self, supervised_inputs: str) -> None:
        self.supervised_inputs = supervised_inputs

    @staticmethod
    def from_dict(obj: Any) -> 'Input':
        if not obj:
            return None
        supervised_inputs = from_str(obj.get("supervised_inputs"))
        return Input(supervised_inputs)

    def to_dict(self) -> dict:
        result: dict = {}
        result["supervised_inputs"] = from_str(self.supervised_inputs)
        return result


class Dtmf:
    mode: int
    exit_button: int

    def __init__(self, mode: int, exit_button: int) -> None:
        self.mode = mode
        self.exit_button = exit_button

    @staticmethod
    def from_dict(obj: Any) -> 'Dtmf':
        if not obj:
            return None
        mode = to_int(from_str(obj.get("mode")))
        exit_button = to_int(from_str(obj.get("exit_button")))
        return Dtmf(mode, exit_button)

    def to_dict(self) -> dict:
        result: dict = {}
        result["mode"] = from_str(str(self.mode))
        result["exit_button"] = from_str(str(self.exit_button))
        return result


class Outbound:
    use_prox_ser: bool
    prox_ip_addrs: str
    prox_ser_port: str

    def __init__(self, use_prox_ser: bool, prox_ip_addrs: str, prox_ser_port: str) -> None:
        self.use_prox_ser = use_prox_ser
        self.prox_ip_addrs = prox_ip_addrs
        self.prox_ser_port = prox_ser_port

    @staticmethod
    def from_dict(obj: Any) -> 'Outbound':
        if not obj:
            return None
        use_prox_ser = from_stringified_bool(from_str(obj.get("use_prox_ser")))
        prox_ip_addrs = from_str(obj.get("prox_ip_addrs"))
        prox_ser_port = from_str(obj.get("prox_ser_port"))
        return Outbound(use_prox_ser, prox_ip_addrs, prox_ser_port)

    def to_dict(self) -> dict:
        result: dict = {}
        result["use_prox_ser"] = from_str(str(self.use_prox_ser).lower())
        result["prox_ip_addrs"] = from_str(self.prox_ip_addrs)
        result["prox_ser_port"] = from_str(self.prox_ser_port)
        return result


class Server:
    address: str
    user_id: str
    password: str
    port: str
    autid: str
    regdur: int

    def __init__(self, address: str, user_id: str, password: str, port: str, autid: str, regdur: int) -> None:
        self.address = address
        self.user_id = user_id
        self.password = password
        self.port = port
        self.autid = autid
        self.regdur = regdur

    @staticmethod
    def from_dict(obj: Any) -> 'Server':
        if not obj:
            return None
        address = from_str(obj.get("address"))
        user_id = from_str(obj.get("user_id"))
        password = from_str(obj.get("password"))
        port = from_str(obj.get("port"))
        autid = from_str(obj.get("autid"))
        regdur = to_int(from_str(obj.get("regdur")))
        return Server(address, user_id, password, port, autid, regdur)

    def to_dict(self) -> dict:
        result: dict = {}
        result["address"] = from_str(self.address)
        result["user_id"] = from_str(self.user_id)
        result["password"] = from_str(self.password)
        result["port"] = from_str(self.port)
        result["autid"] = from_str(self.autid)
        result["regdur"] = from_str(str(self.regdur))
        return result


class Voip:
    server: Server
    dtmf: Dtmf
    phonebook_list: str
    outbound: Outbound
    use_extension_num: bool
    speaker_volume: int
    mic_volume: int
    resolution: int
    server_transport: int

    def __init__(self, server: Server, dtmf: Dtmf, phonebook_list: str, outbound: Outbound, use_extension_num: bool, speaker_volume: int, mic_volume: int, resolution: int, server_transport: int) -> None:
        self.server = server
        self.dtmf = dtmf
        self.phonebook_list = phonebook_list
        self.outbound = outbound
        self.use_extension_num = use_extension_num
        self.speaker_volume = speaker_volume
        self.mic_volume = mic_volume
        self.resolution = resolution
        self.server_transport = server_transport

    @staticmethod
    def from_dict(obj: Any) -> 'Voip':
        if not obj:
            return None
        server = Server.from_dict(obj.get("server"))
        dtmf = Dtmf.from_dict(obj.get("dtmf"))
        phonebook_list = from_str(obj.get("phonebook_list"))
        outbound = Outbound.from_dict(obj.get("outbound"))
        use_extension_num = from_stringified_bool(from_str(obj.get("use_extension_num")))
        speaker_volume = to_int(from_str(obj.get("speakerVolume")))
        mic_volume = to_int(from_str(obj.get("micVolume")))
        resolution = to_int(from_str(obj.get("resolution")))
        server_transport = to_int(from_str(obj.get("serverTransport")))
        return Voip(server, dtmf, phonebook_list, outbound, use_extension_num, speaker_volume, mic_volume, resolution, server_transport)

    def to_dict(self) -> dict:
        result: dict = {}
        result["server"] = to_class(Server, self.server)
        result["dtmf"] = to_class(Dtmf, self.dtmf)
        result["phonebook_list"] = from_str(self.phonebook_list)
        result["outbound"] = to_class(Outbound, self.outbound)
        result["use_extension_num"] = from_str(str(self.use_extension_num).lower())
        result["speakerVolume"] = from_str(str(self.speaker_volume))
        result["micVolume"] = from_str(str(self.mic_volume))
        result["resolution"] = from_str(str(self.resolution))
        result["serverTransport"] = from_str(str(self.server_transport))
        return result


class DeviceID:
    id: int
    input: Input
    event_filter: EventFilter
    voip: Voip

    def __init__(self, id: int, input: Input, event_filter: EventFilter, voip: Voip) -> None:
        self.id = id
        self.input = input
        self.event_filter = event_filter
        self.voip = voip

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        input = Input.from_dict(obj.get("input"))
        event_filter = EventFilter.from_dict(obj.get("event_filter"))
        voip = Voip.from_dict(obj.get("voip"))
        return DeviceID(id, input, event_filter, voip)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["input"] = to_class(Input, self.input)
        result["event_filter"] = to_class(EventFilter, self.event_filter)
        result["voip"] = to_class(Voip, self.voip)
        return result


class InputID:
    device_id: DeviceID
    input_index: int
    type: int
    simulated_unlock: Optional[int]
    apb_use_door_sensor: Optional[int]

    def __init__(self, device_id: DeviceID, input_index: int, type: int, simulated_unlock: Optional[int], apb_use_door_sensor: Optional[int]) -> None:
        self.device_id = device_id
        self.input_index = input_index
        self.type = type
        self.simulated_unlock = simulated_unlock
        self.apb_use_door_sensor = apb_use_door_sensor

    @staticmethod
    def from_dict(obj: Any) -> 'InputID':
        if not obj:
            return None
        device_id = DeviceID.from_dict(obj.get("device_id"))
        input_index = to_int(from_str(obj.get("input_index")))
        type = to_int(from_str(obj.get("type")))
        simulated_unlock = from_union([from_none, lambda x: to_int(from_str(x))], obj.get("simulated_unlock"))
        apb_use_door_sensor = from_union([from_none, lambda x: to_int(from_str(x))], obj.get("apb_use_door_sensor"))
        return InputID(device_id, input_index, type, simulated_unlock, apb_use_door_sensor)

    def to_dict(self) -> dict:
        result: dict = {}
        result["device_id"] = to_class(DeviceID, self.device_id)
        result["input_index"] = from_str(str(self.input_index))
        result["type"] = from_str(str(self.type))
        if self.simulated_unlock is not None:
            result["simulated_unlock"] = from_union([lambda x: from_none((lambda x: is_type(type(None), x))(x)), lambda x: from_str((lambda x: str((lambda x: is_type(int, x))(x)))(x))], self.simulated_unlock)
        if self.apb_use_door_sensor is not None:
            result["apb_use_door_sensor"] = from_union([lambda x: from_none((lambda x: is_type(type(None), x))(x)), lambda x: from_str((lambda x: str((lambda x: is_type(int, x))(x)))(x))], self.apb_use_door_sensor)
        return result


class RelayOutputID:
    device_id: DeviceID
    relay_index: int

    def __init__(self, device_id: DeviceID, relay_index: int) -> None:
        self.device_id = device_id
        self.relay_index = relay_index

    @staticmethod
    def from_dict(obj: Any) -> 'RelayOutputID':
        if not obj:
            return None
        device_id = DeviceID.from_dict(obj.get("device_id"))
        relay_index = to_int(from_str(obj.get("relay_index")))
        return RelayOutputID(device_id, relay_index)

    def to_dict(self) -> dict:
        result: dict = {}
        result["device_id"] = to_class(DeviceID, self.device_id)
        result["relay_index"] = from_str(str(self.relay_index))
        return result


class Row:
    id: int
    name: str
    status: int
    entry_device_id: DeviceID
    open_duration: int
    open_timeout: int
    open_once: bool
    door_group_id: DoorGroupID
    relay_output_id: RelayOutputID
    sensor_input_id: InputID
    exit_button_input_id: InputID
    unconditional_lock: bool

    def __init__(self, id: int, name: str, status: int, entry_device_id: DeviceID, open_duration: int, open_timeout: int, open_once: bool, door_group_id: DoorGroupID, relay_output_id: RelayOutputID, sensor_input_id: InputID, exit_button_input_id: InputID, unconditional_lock: bool) -> None:
        self.id = id
        self.name = name
        self.status = status
        self.entry_device_id = entry_device_id
        self.open_duration = open_duration
        self.open_timeout = open_timeout
        self.open_once = open_once
        self.door_group_id = door_group_id
        self.relay_output_id = relay_output_id
        self.sensor_input_id = sensor_input_id
        self.exit_button_input_id = exit_button_input_id
        self.unconditional_lock = unconditional_lock

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        status = to_int(from_str(obj.get("status")))
        entry_device_id = DeviceID.from_dict(obj.get("entry_device_id"))
        open_duration = to_int(from_str(obj.get("open_duration")))
        open_timeout = to_int(from_str(obj.get("open_timeout")))
        open_once = from_stringified_bool(from_str(obj.get("open_once")))
        door_group_id = DoorGroupID.from_dict(obj.get("door_group_id"))
        relay_output_id = RelayOutputID.from_dict(obj.get("relay_output_id"))
        sensor_input_id = InputID.from_dict(obj.get("sensor_input_id"))
        exit_button_input_id = InputID.from_dict(obj.get("exit_button_input_id"))
        unconditional_lock = from_stringified_bool(from_str(obj.get("unconditional_lock")))
        return Row(id, name, status, entry_device_id, open_duration, open_timeout, open_once, door_group_id, relay_output_id, sensor_input_id, exit_button_input_id, unconditional_lock)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["status"] = from_str(str(self.status))
        result["entry_device_id"] = to_class(DeviceID, self.entry_device_id)
        result["open_duration"] = from_str(str(self.open_duration))
        result["open_timeout"] = from_str(str(self.open_timeout))
        result["open_once"] = from_str(str(self.open_once).lower())
        result["door_group_id"] = to_class(DoorGroupID, self.door_group_id)
        result["relay_output_id"] = to_class(RelayOutputID, self.relay_output_id)
        result["sensor_input_id"] = to_class(InputID, self.sensor_input_id)
        result["exit_button_input_id"] = to_class(InputID, self.exit_button_input_id)
        result["unconditional_lock"] = from_str(str(self.unconditional_lock).lower())
        return result


class DoorCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'DoorCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return DoorCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class ViewDoorWithRS485Response:
    door_collection: DoorCollection
    response: Response

    def __init__(self, door_collection: DoorCollection, response: Response) -> None:
        self.door_collection = door_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'ViewDoorWithRS485Response':
        if not obj:
            return None
        door_collection = DoorCollection.from_dict(obj.get("DoorCollection"))
        response = Response.from_dict(obj.get("Response"))
        return ViewDoorWithRS485Response(door_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["DoorCollection"] = to_class(DoorCollection, self.door_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def view_door_with_rs485_response_from_dict(s: Any) -> ViewDoorWithRS485Response:
    return ViewDoorWithRS485Response.from_dict(s)


def view_door_with_rs485_response_to_dict(x: ViewDoorWithRS485Response) -> Any:
    return to_class(ViewDoorWithRS485Response, x)
