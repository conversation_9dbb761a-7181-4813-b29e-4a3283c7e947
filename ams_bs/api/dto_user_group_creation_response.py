from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any



class UserGroupCreation :
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupCreation':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return UserGroupCreation(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class UserGroupCreationResponse(BaseResponse):
    user_group: UserGroupCreation
    response: Response

    def __init__(self, user_group: UserGroupCreation, response: Response) -> None:
        self.user_group = user_group
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupCreationResponse':
        if not obj:
            return None
        user_group = UserGroupCreation.from_dict(obj.get("UserGroup"))
        response = Response.from_dict(obj.get("Response"))
        return UserGroupCreationResponse(user_group, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserGroup"] = to_class(UserGroupCreation, self.user_group)
        result["Response"] = to_class(Response, self.response)
        return result


def user_group_creation_response_from_dict(s: Any) -> UserGroupCreationResponse:
    return UserGroupCreationResponse.from_dict(s)


def user_group_creation_response_to_dict(x: UserGroupCreationResponse) -> Any:
    return to_class(UserGroupCreationResponse, x)
