from .utils import *
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast

@dataclass
class ModeTemplateDTO:
    """Convert all data contract to field match python name convention
    all date or date time map as string in format
    DATETIME_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss"
    DATE_DISPLAY_FORMAT = "dd/MM/yyyy"
    selection field or enum will be integer field
    """

    response_code: str
    response_message: str
    date_time_string: str  # complete date and time format yyyy/MM/dd HH:mm:ss , if empty replace time now
    type: int  # -- 0 action taken from user, 1 = automated action (enum)


    @staticmethod
    def from_dict(obj: Any) -> 'ModeTemplateDTO':
        """used as expected input from api and when need to convert it to Model DTO"""
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        date_time_string = from_str(obj.get("DateTimeString"))  # DATE_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss";
        type = from_int(obj.get("Type"))
        return ModeTemplateDTO(response_code, response_message, date_time_string, type)

    @staticmethod
    def to_dict(self) -> dict:
        """used in api when need to convert Model DTO to dict to return json data in request library"""
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["DateTimeString"] = from_str(self.date_time_string)
        result["Type"] = from_int(self.type)
        return result