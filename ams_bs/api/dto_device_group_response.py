from .utils import *
from .dto_response import Response, BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast


class ParentID:
    id: int

    def __init__(self, id: int) -> None:
        self.id = id

    @staticmethod
    def from_dict(obj: Any) -> 'ParentID':
        if not obj:
            return None
        id = from_int(obj.get("id"))
        return ParentID(id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_int(self.id)
        return result


class Row:
    id: int
    name: str
    depth: int
    parent_id: Optional[ParentID]

    def __init__(self, id: int, name: str, depth: int, parent_id: Optional[ParentID]) -> None:
        self.id = id
        self.name = name
        self.depth = depth
        self.parent_id = parent_id

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = from_int(obj.get("id"))
        name = from_str(obj.get("name"))
        depth = from_int(obj.get("depth"))
        parent_id = from_union([ParentID.from_dict, from_none], obj.get("parent_id"))
        return Row(id, name, depth, parent_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_int(self.id)
        result["name"] = from_str(self.name)
        result["depth"] = from_int(self.depth)
        if self.parent_id is not None:
            result["parent_id"] = from_union([lambda x: to_class(ParentID, x), from_none], self.parent_id)
        return result


class DeviceGroupCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceGroupCollection':
        if not obj:
            return None
        total = from_int(obj.get("total"))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return DeviceGroupCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_int(self.total)
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result

class DeviceGroupsResponse(BaseResponse):
    device_group_collection: DeviceGroupCollection
    response: Response

    def __init__(self, device_group_collection: DeviceGroupCollection, response: Response) -> None:
        self.device_group_collection = device_group_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceGroupsResponse':
        if not obj:
            return None
        device_group_collection = DeviceGroupCollection.from_dict(obj.get("DeviceGroupCollection"))
        response = Response.from_dict(obj.get("Response"))
        return DeviceGroupsResponse(device_group_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["DeviceGroupCollection"] = to_class(DeviceGroupCollection, self.device_group_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def device_groups_response_from_dict(s: Any) -> DeviceGroupsResponse:
    return DeviceGroupsResponse.from_dict(s)


def device_groups_response_to_dict(x: DeviceGroupsResponse) -> Any:
    return to_class(DeviceGroupsResponse, x)
