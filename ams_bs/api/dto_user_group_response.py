from .utils import *
from .dto_response import Response , BaseResponse
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast


class ParentID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'ParentID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return ParentID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class Row:
    id: int
    name: str
    description: str
    depth: int
    inherited: bool
    face_count: int
    user_count: int
    user_groups: Optional[List[ParentID]]
    parent_id: Optional[ParentID]

    def __init__(self, id: int, name: str, description: str, depth: int, inherited: bool, face_count: int, user_count: int, user_groups: Optional[List[ParentID]], parent_id: Optional[ParentID]) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.depth = depth
        self.inherited = inherited
        self.face_count = face_count
        self.user_count = user_count
        self.user_groups = user_groups
        self.parent_id = parent_id

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        depth = to_int(from_str(obj.get("depth")))
        inherited = from_stringified_bool(from_str(obj.get("inherited")))
        face_count = to_int(from_str(obj.get("face_count")))
        user_count = to_int(from_str(obj.get("user_count")))
        user_groups = from_union([lambda x: from_list(ParentID.from_dict, x), from_none], obj.get("user_groups"))
        parent_id = from_union([ParentID.from_dict, from_none], obj.get("parent_id"))
        return Row(id, name, description, depth, inherited, face_count, user_count, user_groups, parent_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["depth"] = from_str(str(self.depth))
        result["inherited"] = from_str(str(self.inherited).lower())
        result["face_count"] = from_str(str(self.face_count))
        result["user_count"] = from_str(str(self.user_count))
        if self.user_groups is not None:
            result["user_groups"] = from_union([lambda x: from_list(lambda x: to_class(ParentID, x), x), from_none], self.user_groups)
        if self.parent_id is not None:
            result["parent_id"] = from_union([lambda x: to_class(ParentID, x), from_none], self.parent_id)
        return result


class UserGroupCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return UserGroupCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class UserGroupsResponse(BaseResponse):
    user_group_collection: UserGroupCollection
    response: Response

    def __init__(self, user_group_collection: UserGroupCollection, response: Response) -> None:
        self.user_group_collection = user_group_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserGroupsResponse':
        if not obj:
            return None
        user_group_collection = UserGroupCollection.from_dict(obj.get("UserGroupCollection"))
        response = Response.from_dict(obj.get("Response"))
        return UserGroupsResponse(user_group_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserGroupCollection"] = to_class(UserGroupCollection, self.user_group_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def user_groups_response_from_dict(s: Any) -> UserGroupsResponse:
    return UserGroupsResponse.from_dict(s)


def user_groups_response_to_dict(x: UserGroupsResponse) -> Any:
    return to_class(UserGroupsResponse, x)
