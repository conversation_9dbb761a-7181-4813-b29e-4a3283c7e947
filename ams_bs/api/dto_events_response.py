from .utils import *
from .dto_response import *
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast

class ID:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'ID':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return ID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


class EventTypeID:
    code: int

    def __init__(self, code: int) -> None:
        self.code = code

    @staticmethod
    def from_dict(obj: Any) -> 'EventTypeID':
        if not obj:
            return None
        code = to_int(from_str(obj.get("code")))
        return EventTypeID(code)

    def to_dict(self) -> dict:
        result: dict = {}
        result["code"] = from_str(str(self.code))
        return result


class Timezone:
    half: int
    hour: int
    negative: int

    def __init__(self, half: int, hour: int, negative: int) -> None:
        self.half = half
        self.hour = hour
        self.negative = negative

    @staticmethod
    def from_dict(obj: Any) -> 'Timezone':
        if not obj:
            return None
        half = to_int(from_str(obj.get("half")))
        hour = to_int(from_str(obj.get("hour")))
        negative = to_int(from_str(obj.get("negative")))
        return Timezone(half, hour, negative)

    def to_dict(self) -> dict:
        result: dict = {}
        result["half"] = from_str(str(self.half))
        result["hour"] = from_str(str(self.hour))
        result["negative"] = from_str(str(self.negative))
        return result


class UserID:
    user_id: Optional[str]
    name: Optional[str]
    photo_exists: bool

    def __init__(self, user_id: Optional[str] = None, name: Optional[str] = None, photo_exists: bool = False) -> None:
        self.user_id = user_id
        self.name = name
        self.photo_exists = photo_exists

    @staticmethod
    def from_dict(obj: Any) -> 'UserID':
        if not obj:
            return None
        user_id = obj.get("user_id")
        name = obj.get("name")
        photo_exists = from_stringified_bool(from_str(obj.get("photo_exists")))
        return UserID(user_id, name, photo_exists)

    def to_dict(self) -> dict:
        result: dict = {}
        if self.user_id:
            result["user_id"] = self.user_id
        if self.name:
            result["name"] = self.name
        result["photo_exists"] = from_str(str(self.photo_exists).lower())
        return result

class Row:
    id: int
    server_datetime: datetime
    row_datetime: datetime
    index: int
    user_group_id: ID
    device_id: ID
    event_type_id: EventTypeID
    is_dst: int
    timezone: Timezone
    user_update_by_device: bool
    hint: str
    user_id: UserID

    def __init__(self, id: int, server_datetime: datetime, row_datetime: datetime, index: int, user_group_id: ID, device_id: ID, event_type_id: EventTypeID, is_dst: int, timezone: Timezone, user_update_by_device: bool, hint: str, user_id: UserID) -> None:
        self.id = id
        self.server_datetime = server_datetime
        self.row_datetime = row_datetime
        self.index = index
        self.user_group_id = user_group_id
        self.device_id = device_id
        self.event_type_id = event_type_id
        self.is_dst = is_dst
        self.timezone = timezone
        self.user_update_by_device = user_update_by_device
        self.hint = hint
        self.user_id = user_id

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        server_datetime = from_datetime(obj.get("server_datetime"))
        row_datetime = from_datetime(obj.get("datetime"))
        index = to_int(from_str(obj.get("index")))
        user_group_id = ID.from_dict(obj.get("user_group_id"))
        device_id = ID.from_dict(obj.get("device_id"))
        event_type_id = EventTypeID.from_dict(obj.get("event_type_id"))
        is_dst = to_int(from_str(obj.get("is_dst")))
        timezone = Timezone.from_dict(obj.get("timezone"))
        user_update_by_device = from_stringified_bool(from_str(obj.get("user_update_by_device")))
        hint = from_str(obj.get("hint"))
        user_id = UserID.from_dict(obj.get("user_id"))
        return Row(id, server_datetime, row_datetime, index, user_group_id, device_id, event_type_id, is_dst, timezone, user_update_by_device, hint, user_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["server_datetime"] = self.server_datetime.isoformat()
        result["datetime"] = self.row_datetime.isoformat()
        result["index"] = from_str(str(self.index))
        result["user_group_id"] = to_class(ID, self.user_group_id)
        result["device_id"] = to_class(ID, self.device_id)
        result["event_type_id"] = to_class(EventTypeID, self.event_type_id)
        result["is_dst"] = from_str(str(self.is_dst))
        result["timezone"] = to_class(Timezone, self.timezone)
        result["user_update_by_device"] = from_str(str(self.user_update_by_device).lower())
        result["hint"] = from_str(self.hint)
        result["user_id"] = to_class(UserID, self.user_id)
        return result


class EventCollection:
    rows: List[Row]

    def __init__(self, rows: List[Row]) -> None:
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'EventCollection':
        if not obj:
            return None
        rows = from_list(Row.from_dict, obj.get("rows"))
        return EventCollection(rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result

class EventsResponse(BaseResponse):
    event_collection: EventCollection
    response: Response

    def __init__(self, event_collection: EventCollection, response: Response) -> None:
        self.event_collection = event_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'EventsResponse':
        if not obj:
            return None
        event_collection = EventCollection.from_dict(obj.get("EventCollection"))
        response = Response.from_dict(obj.get("Response"))
        return EventsResponse(event_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["EventCollection"] = to_class(EventCollection, self.event_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def events_response_from_dict(s: Any) -> EventsResponse:
    return EventsResponse.from_dict(s)


def events_response_to_dict(x: EventsResponse) -> Any:
    return to_class(EventsResponse, x)
