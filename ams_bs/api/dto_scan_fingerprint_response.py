from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any, List, TypeVar, Type, cast, Callable


class Row:
    id: int
    code: int

    def __init__(self, id: int, code: int) -> None:
        self.id = id
        self.code = code

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        code = to_int(from_str(obj.get("code")))
        return Row(id, code)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["code"] = from_str(str(self.code))
        return result


class DeviceResponse(BaseResponse):
    rows: List[Row]
    result: bool

    def __init__(self, rows: List[Row], result: bool) -> None:
        self.rows = rows
        self.result = result

    @staticmethod
    def from_dict(obj: Any) -> 'DeviceResponse':
        if not obj:
            return None
        rows = from_list(Row.from_dict, obj.get("rows"))
        result = from_stringified_bool(from_str(obj.get("result")))
        return DeviceResponse(rows, result)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        result["result"] = from_str(str(self.result).lower())
        return result


class FingerprintTemplate:
    enroll_quality: int
    template0: str
    template_image0: str

    def __init__(self, enroll_quality: int, template0: str, template_image0: str) -> None:
        self.enroll_quality = enroll_quality
        self.template0 = template0
        self.template_image0 = template_image0

    @staticmethod
    def from_dict(obj: Any) -> 'FingerprintTemplate':
        if not obj:
            return None
        enroll_quality = to_int(from_str(obj.get("enroll_quality")))
        template0 = from_str(obj.get("template0"))
        template_image0 = from_str(obj.get("template_image0"))
        return FingerprintTemplate(enroll_quality, template0, template_image0)

    def to_dict(self) -> dict:
        result: dict = {}
        result["enroll_quality"] = from_str(str(self.enroll_quality))
        result["template0"] = from_str(self.template0)
        result["template_image0"] = from_str(self.template_image0)
        return result



class ScanFingerprintResponse(BaseResponse):
    fingerprint_template: FingerprintTemplate
    device_response: DeviceResponse
    response: Response

    def __init__(self, fingerprint_template: FingerprintTemplate, device_response: DeviceResponse, response: Response) -> None:
        self.fingerprint_template = fingerprint_template
        self.device_response = device_response
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'ScanFingerprintResponse':
        if not obj:
            return None
        fingerprint_template = FingerprintTemplate.from_dict(obj.get("FingerprintTemplate"))
        device_response = DeviceResponse.from_dict(obj.get("DeviceResponse"))
        response = Response.from_dict(obj.get("Response"))
        return ScanFingerprintResponse(fingerprint_template, device_response, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["FingerprintTemplate"] = to_class(FingerprintTemplate, self.fingerprint_template)
        result["DeviceResponse"] = to_class(DeviceResponse, self.device_response)
        result["Response"] = to_class(Response, self.response)
        return result


def scan_fingerprint_response_from_dict(s: Any) -> ScanFingerprintResponse:
    return ScanFingerprintResponse.from_dict(s)


def scan_fingerprint_response_to_dict(x: ScanFingerprintResponse) -> Any:
    return to_class(ScanFingerprintResponse, x)