from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any, List, TypeVar, Type, cast, Callable


class CardType:
    id: int
    name: str
    type: int

    def __init__(self, id: int, name: str, type: int) -> None:
        self.id = id
        self.name = name
        self.type = type

    @staticmethod
    def from_dict(obj: Any) -> 'CardType':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        type = to_int(from_str(obj.get("type")))
        return CardType(id, name, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["type"] = from_str(str(self.type))
        return result


class Row:
    id: int
    card_id: int
    display_card_id: int
    card_type: CardType

    def __init__(self, id: int, card_id: int, display_card_id: int, card_type: CardType) -> None:
        self.id = id
        self.card_id = card_id
        self.display_card_id = display_card_id
        self.card_type = card_type

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        card_id = to_int(from_str(obj.get("card_id")))
        display_card_id = to_int(from_str(obj.get("display_card_id")))
        card_type = CardType.from_dict(obj.get("card_type"))
        return Row(id, card_id, display_card_id, card_type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["card_id"] = from_str(str(self.card_id))
        result["display_card_id"] = from_str(str(self.display_card_id))
        result["card_type"] = to_class(CardType, self.card_type)
        return result


class CardCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'CardCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return CardCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class CardResponse(BaseResponse):
    card_collection: CardCollection
    response: Response

    def __init__(self, card_collection: CardCollection, response: Response) -> None:
        self.card_collection = card_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'CardResponse':
        if not obj:
            return None
        card_collection = CardCollection.from_dict(obj.get("CardCollection"))
        response = Response.from_dict(obj.get("Response"))
        return CardResponse(card_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["CardCollection"] = to_class(CardCollection, self.card_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def card_response_from_dict(s: Any) -> CardResponse:
    return CardResponse.from_dict(s)


def card_response_to_dict(x: CardResponse) -> Any:
    return to_class(CardResponse, x)
