from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any

class Row:
    user_id: int
    name: str

    def __init__(self, user_id: int, name: str) -> None:
        self.user_id = user_id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        user_id = to_int(from_str(obj.get("user_id")))
        name = from_str(obj.get("name"))
        return Row(user_id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["user_id"] = from_str(str(self.user_id))
        result["name"] = from_str(self.name)
        return result


class UserCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'UserCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return UserCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class UserCreationResponse(BaseResponse):
    user_collection: UserCollection
    response: Response

    def __init__(self, user_collection: UserCollection, response: Response) -> None:
        self.user_collection = user_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'UserCreationResponse':
        if not obj:
            return None
        user_collection = UserCollection.from_dict(obj.get("UserCollection"))
        response = Response.from_dict(obj.get("Response"))
        return UserCreationResponse(user_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["UserCollection"] = to_class(UserCollection, self.user_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def user_creation_response_from_dict(s: Any) -> UserCreationResponse:
    return UserCreationResponse.from_dict(s)


def user_creation_response_to_dict(x: UserCreationResponse) -> Any:
    return to_class(UserCreationResponse, x)
