from .utils import *
from .dto_response import Response , BaseResponse
from typing import Any

class Row:
    id: int
    name: str
    description: str
    use_daily_iteration: bool
    start_date: datetime
    days_of_iteration: int

    def __init__(self, id: int, name: str, description: str, use_daily_iteration: bool, start_date: datetime, days_of_iteration: int) -> None:
        self.id = id
        self.name = name
        self.description = description
        self.use_daily_iteration = use_daily_iteration
        self.start_date = start_date
        self.days_of_iteration = days_of_iteration

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        if not obj:
            return None
        id = to_int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        description = from_str(obj.get("description"))
        use_daily_iteration = from_stringified_bool(from_str(obj.get("use_daily_iteration")))
        start_date = from_datetime(obj.get("start_date"))
        days_of_iteration = to_int(from_str(obj.get("days_of_iteration")))
        return Row(id, name, description, use_daily_iteration, start_date, days_of_iteration)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        result["description"] = from_str(self.description)
        result["use_daily_iteration"] = from_str(str(self.use_daily_iteration).lower())
        result["start_date"] = self.start_date.isoformat()
        result["days_of_iteration"] = from_str(str(self.days_of_iteration))
        return result


class ScheduleCollection:
    total: int
    rows: List[Row]

    def __init__(self, total: int, rows: List[Row]) -> None:
        self.total = total
        self.rows = rows

    @staticmethod
    def from_dict(obj: Any) -> 'ScheduleCollection':
        if not obj:
            return None
        total = to_int(from_str(obj.get("total")))
        rows = from_list(Row.from_dict, obj.get("rows"))
        return ScheduleCollection(total, rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["total"] = from_str(str(self.total))
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


class SchedulesResponse(BaseResponse):
    schedule_collection: ScheduleCollection
    response: Response

    def __init__(self, schedule_collection: ScheduleCollection, response: Response) -> None:
        self.schedule_collection = schedule_collection
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'SchedulesResponse':
        if not obj:
            return None
        schedule_collection = ScheduleCollection.from_dict(obj.get("ScheduleCollection"))
        response = Response.from_dict(obj.get("Response"))
        return SchedulesResponse(schedule_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ScheduleCollection"] = to_class(ScheduleCollection, self.schedule_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def schedules_response_from_dict(s: Any) -> SchedulesResponse:
    return SchedulesResponse.from_dict(s)


def schedules_response_to_dict(x: SchedulesResponse) -> Any:
    return to_class(SchedulesResponse, x)


# ------------------------------------------- ScheduleResponse ----------------------------------------------------------------


class ScheduleResponse(BaseResponse):
    schedule: Row
    response: Response

    def __init__(self, schedule: Row, response: Response) -> None:
        self.schedule = schedule
        self.response = response

    @staticmethod
    def from_dict(obj: Any) -> 'ScheduleResponse':
        if not obj:
            return None
        schedule = Row.from_dict(obj.get("Schedule"))
        response = Response.from_dict(obj.get("Response"))
        return ScheduleResponse(schedule, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["Schedule"] = to_class(Row, self.schedule)
        result["Response"] = to_class(Response, self.response)
        return result

def schedule_response_from_dict(s: Any) -> ScheduleResponse:
    return ScheduleResponse.from_dict(s)


def schedule_response_to_dict(x: ScheduleResponse) -> Any:
    return to_class(ScheduleResponse, x)