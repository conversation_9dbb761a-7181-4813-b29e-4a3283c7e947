# # -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from datetime import datetime
from dateutil.relativedelta import relativedelta
from psycopg2 import IntegrityError
import pytz
from odoo import api, fields, models
from odoo.tests.common import tagged
from pprint import pprint
from odoo.tools import mute_logger
# from odoo.addons.ta.tests.common import TestWorkEntryBase
# from addons_ta.ta.tests.common import TestWorkEntryBase
from ..tests.common import *


@tagged('ams_bs')
class TestDevice(TestBase):

    def setUp(self):
        super(TestDevice, self).setUp()

    def test_unit_test(self):
        x = 10
        # print(f"☻ ✓ {x} - passed ☑ 💯 ")  # ☑ 💯
        # print(f"☹ ✘ {x} - fail ⓧ")  # ❎ ⓧ ⒳ ✖  ❌ ✗ ✘ , ㋡ ㋛ ☺ ☹ ☻ 〠 シ ッ ツ ヅ
        # print(f"     ㋡ --> {x}")
        # print(f"🛈   Input Logs:")
        # print(f"⚠   Timesheet Info:")
        self.assertEqual(x, 10, "Just raise fail as testing result")

    def _test_get_events(self):
        payload = {
            "Query": {
                "limit": 20,
                "conditions": [
                    {
                        "column": "datetime",
                        "operator": 3,
                        "values": [
                            "2024-11-19T15:00:00.000Z",
                            "2025-03-02T16:59:59.000Z"
                        ]
                    }
                ],
                "orders": [
                    {
                        "column": "datetime",
                        "descending": True
                    }
                ]
            }
        }


    def test_device_cases(self):
        self._test_get_devices()
        self._test_get_device_groups()
        self._test_get_device_info()
        self._test_device_scan_card()
        self._test_device_scan_fingerprint()
        self._test_device_scan_face()

    def _test_get_devices(self):
        devices_response = self.bs_device_api_client.get_devices(self.base_url)
        self.handle_response('ams_test_case_devices_response', devices_response)

    def _test_get_device_groups(self):
        device_groups_response = self.bs_device_api_client.get_device_groups(self.base_url)
        self.handle_response('ams_test_case_device_groups_response', device_groups_response)

    def _test_get_device_info(self):
        device_response = self.bs_device_api_client.get_device(self.base_url , device_id=546832418)
        self.handle_response('ams_test_case_device_info_response', device_response)

    def _test_device_scan_card(self):
        scan_card_response = self.bs_device_api_client.scan_card(self.base_url, device_id=546832418)
        self.handle_response('ams_test_case_device_scan_card_response', scan_card_response)


    def _test_device_scan_fingerprint(self):
        scan_fingerprint_response = self.bs_device_api_client.scan_fingerprint(self.base_url, device_id=546832418 , enroll_quality="80")
        self.handle_response('ams_test_case_device_scan_fingerprint_response', scan_fingerprint_response)

    def _test_device_scan_face(self):
        scan_face_response = self.bs_device_api_client.scan_face(self.base_url, device_id=538203858,pose_sensitivity=4)
        self.handle_response('ams_test_case_device_scan_face_response', scan_face_response)

    # region helper-------

    # endregion
