# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import requests

from . import test_bs_login
from . import test_bs_ac
from . import test_bs_device
from . import test_bs_user






from odoo.addons.ams_bs.tests.common import TestBase
# Monkey-patch the `_request_handler` to allow all requests
def patch_request_handler():
    # Ensure patching happens only once, avoid recursion.
    if hasattr(TestBase, '_original_request_handler'):
        return
    _super_send = requests.Session.send
    # Save the original _request_handler to restore later
    TestBase._original_request_handler = TestBase._request_handler

    # Define the custom handler
    def custom_request_handler(s, r, /, **kw):
        # Allow all requests by bypassing the blocking logic
        return _super_send(s, r, **kw)

    # Apply the patch
    TestBase._request_handler = custom_request_handler


# Apply the patch when the module is initialized
patch_request_handler()


