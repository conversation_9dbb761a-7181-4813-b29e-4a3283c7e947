# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta, date, time

import requests
from dateutil.relativedelta import relativedelta
from requests import PreparedRequest, Session

from odoo.addons.ams_bs.api.dto_response import BaseResponse, Response
from odoo.fields import Date
from odoo.tests import tagged
from odoo.tests.common import TransactionCase



class TestBase(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super(TestBase, cls).setUpClass()


        cls.env.user.tz = 'Asia/Riyadh'
        cls.tz_hr_offset = 3
        cls.env.ref('resource.resource_calendar_std').tz = 'Asia/Riyadh'

        cls.dep_test = cls.env['hr.department'].create({
            'name': 'Testing & Development - Test',
        })

        cls.base_url = "https://example.com"
        cls.api_user = "example_user"
        cls.api_password = "example_password"
        cls.bs_session_id =""

        # Monkey-patch the `_request_handler` to allow all requests

        # Save the original _request_handler to restore later
        # cls._original_request_handler = cls._request_handler

        # Patch the _request_handler to allow external requests
        # def custom_request_handler(s, r, **kwargs):
        #     """
        #     A custom request handler that bypasses the blocking logic.
        #     This function will be used to replace the default `_request_handler`.
        #     """
        #     # Allow all requests by bypassing the blocking logic
        #     return s.send(r, **kwargs)
        #
        # # Apply the patch to the class
        # cls._request_handler = custom_request_handler

    @property
    def bs_api_client(self):
        return self.env['ams_base.biostar_api_client']

    @property
    def bs_user_api_client(self):
        return self.env['ams_bs.biostar_user_api_client']

    @property
    def bs_device_api_client(self):
        return self.env['ams_bs.biostar_device_api_client']

    @property
    def bs_ac_api_client(self):
        return self.env['ams_bs.biostar_ac_api_client']

# region Helper----------------------------------
    def handle_response_message(self,test_case,message ='',error=True):
        if error:
            print(f"❌ - {test_case} fail ,error: {message}")
        else:
            print(f"✅ 💯- {test_case} success {message}")

    def handle_response(self,test_case,result):
        code = -1
        if isinstance(result, BaseResponse):
            code = result.response.code
            if code != 0:
                self.handle_response_message(test_case,result.response.message)
        elif isinstance(result, Response):
            code = result.code
            if code != 0:
                self.handle_response_message(test_case,result.message)
                # self.assertIsNotNone(response, "Fail to get ac doors!")

        if code == 0:
            self.handle_response_message(test_case,error=False)

    # endregion



