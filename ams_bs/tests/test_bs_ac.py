# # -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from datetime import datetime
from dateutil.relativedelta import relativedelta
from psycopg2 import IntegrityError
import pytz
from odoo import api, fields, models
from odoo.addons.ams_bs.api.dto_response import Response, BaseResponse
from odoo.tests.common import tagged
from pprint import pprint
from odoo.tools import mute_logger
# from odoo.addons.ta.tests.common import TestWorkEntryBase
# from addons_ta.ta.tests.common import TestWorkEntryBase
from ..tests.common import *


@tagged('ams_bs')
class TestAccessControl(TestBase):

    def setUp(self):
        super(TestAccessControl, self).setUp()

    def _test_unit_test(self):
        x = 10
        # print(f"☻ ✓ {x} - passed ☑ 💯 ")  # ☑ 💯
        # print(f"☹ ✘ {x} - fail ⓧ")  # ❎ ⓧ ⒳ ✖  ❌ ✗ ✘ , ㋡ ㋛ ☺ ☹ ☻ 〠 シ ッ ツ ヅ
        # print(f"     ㋡ --> {x}")
        # print(f"🛈   Input Logs:")
        # print(f"⚠   Timesheet Info:")
        self.assertEqual(x, 10, "Just raise fail as testing result")

    def test_ac_cases(self):
        self._test_api_get_ac_doors()
        self._test_api_get_ac_door()
        self._test_api_get_ac_schedules()
        self._test_api_get_ac_schedule()
        self._test_api_get_ac_groups()
        self._test_api_get_ac_group()
        self._test_api_get_ac_levels()
        self._test_create_access_group()
        self._test_update_access_group()


    def _test_api_get_ac_doors(self):
        doors_response = self.bs_ac_api_client.get_ac_doors(self.base_url)
        self.handle_response('ams_test_case_doors_response', doors_response)

    def _test_api_get_ac_door(self):
        door_id = 1
        door_response = self.bs_ac_api_client.get_ac_door(self.base_url,door_id)
        self.handle_response('ams_test_case_door_response', door_response)

    def _test_api_get_ac_schedules(self):
        schedules_response = self.bs_ac_api_client.get_ac_schedules(self.base_url)
        self.handle_response('ams_test_case_schedules_response', schedules_response)


    def _test_api_get_ac_schedule(self):
        schedule_id = 1
        schedule_response = self.bs_ac_api_client.get_ac_schedule(self.base_url,schedule_id)
        self.handle_response('ams_test_case_schedule_response', schedule_response)


    def _test_api_get_ac_groups(self):
        ac_groups_response = self.bs_ac_api_client.get_ac_groups(self.base_url)
        self.handle_response('ams_test_case_ac_groups_response', ac_groups_response)


    def _test_api_get_ac_group(self):
        group_id = 1
        ac_group_response = self.bs_ac_api_client.get_ac_group(self.base_url,group_id)
        self.handle_response('ams_test_case_ac_group_response', ac_group_response)


    def _test_api_get_ac_levels(self):
        levels_response = self.bs_ac_api_client.get_ac_levels(self.base_url)
        self.handle_response('ams_test_case_levels_response', levels_response)

    def _test_create_access_group(self):
        payload= {
    "AccessGroup": {
        "name": "ac_grp2",
        "description": "",
        "users": [
            {
                "user_id": 10001
            }
        ],
        "user_groups": [
            {
                "id": 1
            }
        ],
        "access_levels": [
            {
                "id": 1
            }
        ]

    }
}
        ac_group_response = self.bs_ac_api_client.create_access_group(self.base_url,payload)
        self.handle_response('ams_test_case_create_ac_group_response', ac_group_response)

    def _test_update_access_group(self):
        payload = {
            "AccessGroup": {
                "name": "ac_grp4",
                "description": "",
                "users": [
                    {
                        "user_id": 2
                    }
                ],
                "user_groups": [
                    {
                        "id": 1
                    }
                ],
                "access_levels": [
                    {
                        "id": 1
                    }
                ]

            }
        }
        ac_group_response = self.bs_ac_api_client.update_access_group(self.base_url,13,payload)
        self.handle_response('ams_test_case_update_ac_group_response', ac_group_response)

    # region helper-------

    # endregion
