# # -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from datetime import datetime
from dateutil.relativedelta import relativedelta
from psycopg2 import IntegrityError
import pytz
from odoo import api, fields, models
from odoo.tests.common import tagged
from pprint import pprint
from odoo.tools import mute_logger
# from odoo.addons.ta.tests.common import TestWorkEntryBase
# from addons_ta.ta.tests.common import TestWorkEntryBase
from ..tests.common import *


@tagged('ams_bs')
class TestUser(TestBase):

    def setUp(self):
        super(TestUser, self).setUp()

    def _test_unit_test(self):
        x = 10
        # print(f"☻ ✓ {x} - passed ☑ 💯 ")  # ☑ 💯
        # print(f"☹ ✘ {x} - fail ⓧ")  # ❎ ⓧ ⒳ ✖  ❌ ✗ ✘ , ㋡ ㋛ ☺ ☹ ☻ 〠 シ ッ ツ ヅ
        # print(f"     ㋡ --> {x}")
        # print(f"🛈   Input Logs:")
        # print(f"⚠   Timesheet Info:")
        self.assertEqual(x, 10, "Just raise fail as testing result")

    def test_user_cases(self):
        self._test_get_users()
        self._test_get_user_details()
        self._test_get_user_groups()
        self._test_get_card_types()
        self._test_get_cards()
        self._test_create_card()
        self._test_assign_card_to_user()
        self._test_create_user()
        self._test_update_user()

    def _test_get_users(self):
        users_response = self.bs_user_api_client.get_users(self.base_url)
        self.handle_response('ams_test_case_users_response', users_response)

    def _test_get_user_details(self):
        user_response = self.bs_user_api_client.get_user(self.base_url, user_id=1)
        self.handle_response('ams_test_case_user_response', user_response)

    def _test_get_user_groups(self):
        user_groups_response = self.bs_user_api_client.get_user_groups(self.base_url)
        self.handle_response('ams_test_case_user_groups_response', user_groups_response)

    def _test_get_card_types(self):
        card_types_response = self.bs_user_api_client.get_card_types(self.base_url)
        self.handle_response('ams_test_case_card_types_response', card_types_response)

    def _test_get_cards(self):
        cards_response = self.bs_user_api_client.get_cards(self.base_url)
        self.handle_response('ams_test_case_cards_response', cards_response)

    def _test_create_card(self):
        payload = {
                "CardCollection": {
                    "rows": [
                        {
                            "card_id": "1000006",
                            "card_type": {
                                "id": "6",
                                "type" : "6"
                            }
                        }
                    ]
                }
            }
        cards_response = self.bs_user_api_client.create_card(self.base_url, payload)
        self.handle_response('ams_test_case_create_card_response', cards_response)

    def _test_assign_card_to_user(self):
        assign_card_to_user_response = self.bs_user_api_client.assign_card_to_user(self.base_url, user_id=5050, card_id=3)
        self.handle_response('ams_test_case_assign_card_to_user_response', assign_card_to_user_response)

    def _test_create_user(self):
        paylpad = {
    "User": {
        "user_id": "102",
        "user_group_id": {
            "id": "1"
        },
        "start_datetime": "2001-01-01T00:00:00.00Z",
        "expiry_datetime": "2030-12-31T23:59:00.00Z",
        "disabled": "false",
        "name": "Ahmed Reda",
        "email": "<EMAIL>",
        "department": "",
        "user_title": "",
        "photo": "",
        "phone": "",
        "permission": {
            "id": "1"
        },
        "access_groups": [
            {
                "id": "1"
            }
        ],
        "login_id": "102",
        "password": "",
        "user_ip": ""
    }
}
        user_response = self.bs_user_api_client.create_user(self.base_url , paylpad)
        self.handle_response('ams_test_case_create_user_response', user_response)

    def _test_update_user(self):
        paylpad = {
            "User": {
                "user_id": "1119",
                "user_group_id": {
                    "id": "1"
                },
                "start_datetime": "2001-01-01T00:00:00.00Z",
                "expiry_datetime": "2030-12-31T23:59:00.00Z",
                "disabled": "false",
                "name": "mohsen6",
                "email": "<EMAIL>",
                "department": "",
                "user_title": "",
                "photo": "",
                "phone": "",
                "permission": {
                    "id": "1"
                },
                "access_groups": [
                    {
                        "id": "1"
                    }
                ],
                "login_id": "1119",
                "password": "",
                "user_ip": ""
            }
        }
        user_response = self.bs_user_api_client.update_user(self.base_url, paylpad , 1119)
        self.handle_response('ams_test_case_update_user_response', user_response)

    # region helper-------

    # endregion
