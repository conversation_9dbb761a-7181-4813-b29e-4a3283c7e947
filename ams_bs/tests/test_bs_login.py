# # -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import json
from datetime import datetime
from unittest.mock import patch

import requests
from dateutil.relativedelta import relativedelta
from psycopg2 import IntegrityError
import pytz
from odoo import api, fields, models
from odoo.addons.ams_bs.models._base_biostar_api_client import BaseBiostarAPIClient
from odoo.tests.common import tagged
from pprint import pprint
from odoo.tools import mute_logger
# from odoo.addons.ta.tests.common import TestWorkEntryBase
# from addons_ta.ta.tests.common import TestWorkEntryBase
from ..tests.common import *
from unittest import mock

@tagged('ams_bs')
class TestUserLogin(TestBase):

    def setUp(self):
        super(TestUserLogin, self).setUp()

    def test_authentication_cases(self):
        self._test_api_login()

    def _test_api_login(self):
        response = self.bs_api_client.login(self.base_url, self.api_user, self.api_password)
        self.handle_response('ams_test_case_login_response', response)

    # region helper-------

    # endregion
