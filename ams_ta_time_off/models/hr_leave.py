from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timezone
from odoo.addons.ams_ta_time_off.api.dto_request import RequestDTO
from odoo.addons.ams_ta_time_off.api.dto_request_list import RequestListDTO
from odoo.addons.ams_ta_time_off.api.dto_request_log import RequestLogDTO
from odoo.addons.ams_ta.api.dto_employee import EmployeeDTO
import json
from pytz import timezone, UTC


class HolidaysRequest(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "hr.leave"
    _inherit = "hr.leave", "ams_base.abstract_model"
    _description = "Time off Request"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    record_id = fields.Char()

    # endregion

    # region  Special
    # endregion

    # region  Relational
    leave_manager_id = fields.Many2one("res.users")

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchange ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def write(self, vals):
        res = super(HolidaysRequest, self).write(vals)
        if 'state' in vals:
            self._onchange_state()  # Manually call onchange
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    @api.depends('state')
    def _onchange_state(self):
        for record in self:
            if record.state == 'validate':
                record._link_with_old_timesheets()
            # confirm = to approve
            elif record.state in ['refuse', 'cancel', 'confirm']:
                record._unlink_with_old_timesheets()

    def get_old_timesheets(self):
        employee = self.employee_id.id
        if employee:
            return self.env["ams_ta.timesheet"].search([('employee_id', '=', employee),
                                                        ('date', '>=', self.date_from.date()),
                                                        ('date', '<=', self.date_to.date()),
                                                        ('is_weekend', '=', False)
                                                        ])

    def _unlink_with_old_timesheets(self):
        old_timesheets = self.get_old_timesheets()
        if old_timesheets:
            old_timesheets.write({
                'time_off_hours': 0,
                'is_vacation': False,
                'time_off_id': None,
                'notes': '',
            })

    def _link_with_old_timesheets(self):
        old_timesheets = self.get_old_timesheets()
        if old_timesheets:
            leave_type = self.leave_type_request_unit
            if leave_type == 'day':
                old_timesheets.write({
                    'is_vacation': True,
                    'time_off_id': self.id,
                    'notes': self.holiday_status_id.name
                })
            else:
                old_timesheets.write({
                    'time_off_hours': self.number_of_hours,
                    'time_off_id': self.id,
                    'notes': f'{self.holiday_status_id.name} - {self.duration_display}'
                })

    # endregion

    # region ---------------------- TODO[IMP]: API Methods -------------------------------------
    def _convert_leave_state_to_status(self, state):
        """Map Odoo leave state to status code."""
        return {
            'confirm': 0,
            'validate1': 0,
            'validate': 1,
            'refuse': 2,
            'cancel': 3
        }.get(state, 0)

    def _convert_status_to_leave_state(self, status: int):
        """Map status code to Odoo leave state."""
        return {
            0: 'confirm',  # assuming 'confirm' as default wait state
            1: 'validate',
            2: 'refuse',
            3: 'cancel'
        }.get(status, 'confirm')

    def _prepare_request_vals(self, employee_id, request_dto: RequestLogDTO, is_vacation=False):
        """Prepare leave request values from DTO"""

        row_request_name = (request_dto.request_name or "").strip()
        leave_type = self.env['hr.leave.type'].search([('name', '=ilike', row_request_name)], limit=1)

        if not leave_type:
            leave_type_vals = {
                'name': row_request_name,
                'leave_validation_type': 'both',
                'requires_allocation': 'no',
                'request_unit': 'day' if is_vacation else 'hour',
            }
            leave_type = self.env['hr.leave.type'].sudo().create(leave_type_vals)

        return {
            'record_id': request_dto.request_no,
            'employee_id': employee_id,
            'holiday_status_id': leave_type.id,
            'notes': request_dto.reason or request_dto.reply_comment or "",
            'state': self._convert_status_to_leave_state(request_dto.status),
            'create_date': datetime.now(),
            'request_date_from': request_dto.start_date,
            'request_date_to': request_dto.end_date,
            'request_hour_from': request_dto.start_time,
            'request_hour_to': request_dto.end_time,
        }

    def prepare_request_log_dto(self, employee, is_vacation=False):
        """Prepares a unified RequestLogDTO for both permission and vacation types."""
        tz = timezone(employee.resource_id.tz or 'UTC')
        dto = RequestLogDTO(
            response_code="1",
            response_message="OK",
            response_message_ar="تمت العملية بنجاح",
            request_no=str(self.record_id),
            emp_no=employee.employee_number or '',
            user_name=employee.user_name or '',
            request_date_string=self.create_date.astimezone(tz).strftime('%d/%m/%Y %H:%M:%S') or "",
            start_date_string=self.date_from.astimezone(tz).strftime('%d/%m/%Y %H:%M:%S') or "",
            end_date_string=self.date_to.astimezone(tz).strftime('%d/%m/%Y %H:%M:%S') or "",
            request_name=self.holiday_status_id.name if self.holiday_status_id else "",
            reason=self.notes or "",
            reply_comment=self.notes or "",
            status=self._convert_leave_state_to_status(self.state),
            error_message=""
        )

        if not is_vacation:
            dto.transaction_type = 0  # or 1, depending on how you determine this
            dto.total_minutes = int((self.date_to - self.date_from).total_seconds() / 60) \
                if isinstance(self.date_to, datetime) and isinstance(self.date_from, datetime) else 0

        return dto

    def _prepare_request_dto(self, leaves, employee, is_vacation=False):
        """Processes leave records into DTO format for either vacation or permission requests."""
        emp_dto = employee._prepare_employee_dto(employee)
        logs = []
        for leave in leaves:
            # Call the prepare_request_log_dto method on each leave instance
            log_dto = leave.prepare_request_log_dto(employee, is_vacation=is_vacation)
            logs.append(log_dto)

        return RequestDTO(
            response_code="1",
            response_message="OK",
            response_message_ar="تمت العملية بنجاح",
            emp=emp_dto,
            logs=logs,
            error_message=""
        )

    def add_leave_request(self, request_dto: RequestLogDTO, is_vacation=False) -> RequestLogDTO:
        """Handle adding leave request"""
        try:
            # Step 1: Input validation
            if request_dto.error_message:
                return request_dto

            employee = self.env['hr.employee'].search([
                '|',
                ('employee_number', '=', request_dto.emp_no),
                ('user_name', '=', request_dto.user_name)
            ], limit=1)

            if not employee:
                request_dto.error_message = "Employee not found"
                request_dto.response_message_ar = "لم يتم العثور على الموظف"
                return request_dto

            if not request_dto.request_name:
                request_dto.error_message = "Request type not provided"
                request_dto.response_message_ar = "نوع الطلب غير محدد"
                return request_dto

            existing_request = self.env['hr.leave'].search([('record_id', '=', request_dto.request_no), ], limit=1)

            if existing_request:
                existing_request.write({'state': self._convert_status_to_leave_state(request_dto.status)})
            else:
                request_vals = self._prepare_request_vals(employee.id, request_dto, is_vacation)
                self.env['hr.leave'].create(request_vals)

            request_dto.response_message = "Leave request created or updated successfully"
            request_dto.response_code = "200"
            return request_dto

        except Exception as leave_ex:
            error_message = str(leave_ex)
            if "overlaps" in error_message.lower():
                request_dto.error_message = "There is already a leave request for this employee during the selected period"
                request_dto.response_message_ar = "يوجد بالفعل طلب إجازة لهذا الموظف خلال الفترة المحددة"
            else:
                request_dto.error_message = "Error: " + error_message
                request_dto.response_message_ar = "خطأ: " + error_message
            return request_dto

    def get_employee_requests(self, key: str, date_from, date_to, filter_type: str, is_vacation=False) -> RequestDTO:
        # Search for employee
        key = key.strip()
        employee = self.env['hr.employee'].search([(filter_type, '=ilike', key)], limit=1)
        if not employee:
            return RequestDTO(
                response_code="404",
                response_message="Employee not found",
                error_message=f"Error: Employee {key} doesn't exist!!",
                response_message_ar="الموظف غير موجود",
                emp=None,
                logs=[]
            )

        if date_to < date_from:
            return RequestDTO(
                response_code="400",
                response_message="The start date must be earlier than the end date",
                error_message=f"Error: The start date must be earlier than the end date",
                response_message_ar="يجب أن يكون تاريخ البداية قبل تاريخ النهاية",
                emp=employee._prepare_employee_dto(employee),
                logs=[]
            )

        # Get requests within the date range
        requests = self._get_leaves(employee, date_from, date_to, is_vacation)

        return self._prepare_request_dto(requests, employee, is_vacation)

    def _get_leaves(self, employee, date_from, date_to, is_vacation):
        """Return leaves for a given employee between date range."""
        domain = [
            ('employee_id', '=', employee.id),
            ('request_date_from', '>=', date_from.strftime('%Y-%m-%d')),
            ('request_date_to', '<=', date_to.strftime('%Y-%m-%d')),
            ('state', '!=', 'cancel')
        ]
        if is_vacation:
            domain.append(('holiday_status_id.request_unit', '=', 'day'))
        else:
            domain.append(('holiday_status_id.request_unit', '!=', 'day'))

        return self.env['hr.leave'].sudo().search(domain)

    def get_all_requests(self, date_from, date_to, key: str = None, filter_type: str = None,
                         is_vacation=False) -> RequestListDTO:
        """Get all permission or vacation requests within a date range."""
        if date_to < date_from:
            return RequestListDTO(
                response_code="400",
                response_message="The start date must be earlier than the end date",
                response_message_ar="يجب أن يكون تاريخ البداية قبل تاريخ النهاية",
                error_message="Error: The start date must be earlier than the end date",
                requests=[]
            )

        # Apply employee filtering if requested
        domain = []
        if filter_type == 'dept_name' and key:
            key = key.strip()
            domain.append(('department_id.name', '=ilike', key))
        employees = self.env['hr.employee'].sudo().search(domain)

        if not employees:
            return RequestListDTO(
                response_code="404",
                response_message="No employees found",
                response_message_ar="لا يوجد موظفين",
                error_message="Error: No employees found",
                requests=[]
            )

        # Initialize the result DTO
        request_list = RequestListDTO(
            response_code="1",
            response_message="OK",
            response_message_ar="تمت العملية بنجاح",
            error_message="",
            requests=[]
        )

        # Fetch requests for each employee
        for emp in employees:
            requests = self._get_leaves(emp, date_from, date_to, is_vacation)
            requests_dtos = self._prepare_request_dto(requests, emp, is_vacation)
            if requests_dtos:
                request_list.requests.append(requests_dtos)

        return request_list

    # endregion
