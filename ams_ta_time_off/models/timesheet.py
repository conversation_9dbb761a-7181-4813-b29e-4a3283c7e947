from odoo import api, fields, models
from datetime import datetime, time


class Timesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "ams_ta.timesheet"
    _description = "Timesheet for AMS TA Including Time Off Configuration"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    time_off_id = fields.Many2one("hr.leave", tracking=True)
    public_vacation_id = fields.Many2one('resource.calendar.leaves')

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchange ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model_create_multi
    def create(self, values_list):
        if not isinstance(values_list, list):
            values_list = [values_list]

        timesheets = super(Timesheet, self).create(values_list)
        for timesheet in timesheets:
            if not timesheet.is_weekend:
                date = datetime.combine(timesheet.date, time(hour=0, minute=0))
                public_vacation_record = self.env["resource.calendar.leaves"].search([
                    ('is_active', '=', True),
                    ('date_from', '<=', date),
                    ('date_to', '>=', date)
                ], limit=1)

                if public_vacation_record:
                    timesheet.write({
                        'is_public_vacation': True,
                        'public_vacation_id': public_vacation_record.id,
                        'notes': public_vacation_record.name
                    })

                else:

                    time_off_record = self.env['hr.leave'].search([
                        ('employee_id', '=', timesheet.employee_id.id),
                        ('request_date_from', '<=', timesheet.date),
                        ('request_date_to', '>=', timesheet.date),
                        ('state', '=', 'validate')
                    ], limit=1)

                    if time_off_record:

                        if time_off_record.holiday_status_id.request_unit == 'day':
                            timesheet.write({
                                'is_vacation': True,
                                'time_off_id': time_off_record.id,
                                'notes': time_off_record.holiday_status_id.name
                            })
                        else:
                            timesheet.write({
                                'time_off_hours': time_off_record.number_of_hours,
                                'time_off_id': time_off_record.id,
                                'notes': time_off_record.holiday_status_id.name
                            })

        return timesheets

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    # endregion
