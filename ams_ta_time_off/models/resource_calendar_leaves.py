from odoo import fields, models


class ResourceCalendarLeaves(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "resource.calendar.leaves"
    _description = "Public Holidays"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    is_active = fields.Boolean()

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchange ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_activate(self):
        self.is_active = True
        # TODO Review  date with user context
        old_timesheets = self.env["ams_ta.timesheet"].search([('date', '>=', self.date_from.date()),
                                                              ('date', '<=', self.date_to.date()),
                                                              ('is_weekend', '=', False),
                                                              ])
        if old_timesheets:
            old_timesheets.write({
                'is_public_vacation': True,
                'public_vacation_id': self.id,
                'notes': self.name
            })

    def action_deactivate(self):
        self.is_active = False

        domain = [('public_vacation_id', '=', self.id)]

        timeoff_timesheets = self.env['ams_ta.timesheet'].search(domain)
        if timeoff_timesheets:
            for timesheet in timeoff_timesheets:
                timesheet.write({'public_vacation_id': False,
                                 'is_public_vacation': False,
                                 'notes': '',
                                 })
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
