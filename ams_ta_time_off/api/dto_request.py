from odoo.addons.ams_ta.api.dto_employee import *
from odoo.addons.ams_ta_time_off.api.dto_request_log import *
from odoo.addons.ams_ta.api.utils import *


class RequestDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    emp: EmployeeDTO
    logs: List[RequestLogDTO]
    error_message: str

    def __init__(self, response_code: str, response_message: str, response_message_ar: str, emp: EmployeeDTO,
                 logs: List[RequestLogDTO], error_message: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.emp = emp
        self.logs = logs
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'RequestDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        emp = EmployeeDTO.from_dict(obj.get("Emp"))
        logs = from_list(RequestLogDTO.from_dict, obj.get("Logs"))
        error_message = from_str(obj.get("ErrorMessage"))
        return RequestDTO(response_code, response_message, response_message_ar, emp, logs, error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["Emp"] = self.emp.to_dict() if self.emp else {}
        result["Logs"] = [log.to_dict() for log in self.logs]
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def request_dto_from_dict(s: Any) -> RequestDTO:
    return RequestDTO.from_dict(s)


def request_dto_to_dict(x: RequestDTO) -> Any:
    return to_class(RequestDTO, x)
