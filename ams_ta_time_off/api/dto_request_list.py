from odoo.addons.ams_ta_time_off.api.dto_request import *
from odoo.addons.ams_ta.api.utils import *


class RequestListDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    requests: List[RequestDTO]
    error_message: str

    def __init__(self, response_code: str, response_message: str, response_message_ar: str, requests: List[RequestDTO],
                 error_message: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.requests = requests
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'RequestListDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        requests = from_list(RequestDTO.from_dict, obj.get("Requests"))
        error_message = from_str(obj.get("ErrorMessage"))
        return RequestListDTO(response_code, response_message, response_message_ar, requests, error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["Requests"] = [request.to_dict() for request in self.requests]
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def request_list_dto_from_dict(s: Any) -> RequestListDTO:
    return RequestListDTO.from_dict(s)


def request_list_dto_to_dict(x: RequestListDTO) -> Any:
    return to_class(RequestListDTO, x)
