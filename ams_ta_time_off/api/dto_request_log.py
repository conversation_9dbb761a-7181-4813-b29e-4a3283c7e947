from odoo.addons.ams_ta.api.utils import *

DATE_FORMAT = '%d/%m/%Y %H:%M:%S'


@dataclass
class RequestLogDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    emp_no: str
    user_name: str
    request_no: str
    request_date_string: str
    start_date_string: str
    end_date_string: str
    request_name: str
    reason: str
    reply_comment: str
    status: int
    error_message: str
    # Permission-specific fields (optional)
    transaction_type: Optional[int] = None
    total_minutes: Optional[int] = None
    emp_no: str

    def __init__(
            self,
            response_code: str,
            response_message: str,
            response_message_ar: str,
            emp_no: str,
            user_name: str,
            request_no: str,
            request_date_string: str,
            start_date_string: str,
            end_date_string: str,
            request_name: str,
            reason: str,
            reply_comment: str,
            status: int,
            error_message: str,
            transaction_type: Optional[int] = None,
            total_minutes: Optional[int] = None
    ) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.emp_no = emp_no
        self.user_name = user_name
        self.request_no = request_no
        self.request_date_string = request_date_string
        self.start_date_string = start_date_string
        self.end_date_string = end_date_string
        self.request_name = request_name
        self.reason = reason
        self.reply_comment = reply_comment
        self.status = status
        self.error_message = error_message
        self.transaction_type = transaction_type
        self.total_minutes = total_minutes

        # ✅ Automatic date validation if no other error message
        start = self.start_date
        end = self.end_date
        if not self.error_message:
            if not start or not end:
                self.error_message = "Error: Invalid date format. Expected dd/MM/yyyy HH:mm:ss"
            elif end < start:
                self.error_message = "Error: End date must be greater than or equal to start date"
            elif self.emp_no == "":
                self.error_message = "Error: Employee number is required"

        # Set response_message to be the same as error_message if error_message is not empty
        if self.error_message:
            self.response_message = self.error_message

    @staticmethod
    def from_dict(obj: Any) -> 'RequestLogDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode", "0"))
        response_message = from_str(obj.get("ResponseMessage", ""))
        response_message_ar = from_str(obj.get("ResponseMessageAR", ""))
        emp_no = from_str(obj.get("EmpNo", ""))
        user_name = from_str(obj.get("UserName", ""))
        request_no = from_str(obj.get("RequestNo", ""))
        request_date_string = from_str(obj.get("RequestDateString", ""))
        start_date_string = from_str(obj.get("StartDateString", ""))
        end_date_string = from_str(obj.get("EndDateString", ""))
        request_name = from_str(obj.get("RequestName", ""))
        reason = from_str(obj.get("Reason", ""))
        reply_comment = from_str(obj.get("ReplyComment", ""))
        status = from_int(obj.get("Status", 0))
        error_message = from_str(obj.get("ErrorMessage", ""))
        # Optional fields for permission requests
        transaction_type = from_int(obj.get("TransactionType")) if obj.get("TransactionType") is not None else None
        total_minutes = from_int(obj.get("TotalMinutes")) if obj.get("TotalMinutes") is not None else None

        return RequestLogDTO(
            response_code, response_message, response_message_ar, emp_no, user_name,
            request_no, request_date_string, start_date_string,
            end_date_string, request_name, reason, reply_comment, status,
            error_message, transaction_type, total_minutes
        )

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["EmpNo"] = from_str(self.emp_no)
        result["UserName"] = from_str(self.user_name)
        result["RequestNo"] = from_str(str(self.request_no))
        result["RequestDateString"] = from_str(self.request_date_string)
        result["StartDateString"] = from_str(self.start_date_string)
        result["EndDateString"] = from_str(self.end_date_string)
        result["RequestName"] = from_str(self.request_name)
        result["Reason"] = from_str(self.reason)
        result["ReplyComment"] = from_str(self.reply_comment)
        result["Status"] = from_int(self.status)
        result["ErrorMessage"] = from_str(self.error_message)
        # Only include permission-specific fields if they exist
        if self.transaction_type is not None:
            result["TransactionType"] = from_int(self.transaction_type)
        if self.total_minutes is not None:
            result["TotalMinutes"] = from_int(self.total_minutes)
        return result

    @property
    def request_date(self) -> Optional[date]:
        return self._parse_date(self.request_date_string).date()

    @property
    def start_date(self) -> Optional[date]:
        return self._parse_date(self.start_date_string).date()

    @property
    def end_date(self) -> Optional[date]:
        return self._parse_date(self.end_date_string).date()

    @property
    def start_time(self) -> Optional[float]:
        start_dt = self._parse_date(self.start_date_string)
        return float(start_dt.hour) + float(start_dt.minute) / 60

    @property
    def end_time(self) -> Optional[float]:
        end_dt = self._parse_date(self.end_date_string)
        return float(end_dt.hour) + float(end_dt.minute) / 60

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        try:
            return datetime.strptime(date_str, DATE_FORMAT)
        except Exception:
            return None


def request_log_dto_from_dict(s: Any) -> RequestLogDTO:
    return RequestLogDTO.from_dict(s)


def request_log_dto_to_dict(x: RequestLogDTO) -> Any:
    return to_class(RequestLogDTO, x)
