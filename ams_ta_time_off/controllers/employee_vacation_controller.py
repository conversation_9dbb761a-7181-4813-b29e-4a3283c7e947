from odoo import http
from odoo.addons.ams_ta.controllers.base_controller import BaseController
from odoo.addons.ams_ta.helper.helper import _parse_date_range
from odoo.addons.ams_ta_time_off.api.dto_request_log import RequestLogDTO
from odoo.http import request
from odoo.exceptions import ValidationError
import json


class EmployeeVacationController(BaseController):

    @property
    def hr_leave_model(self):
        return self.ams_ta_request.env['hr.leave'].sudo()

    @http.route('/AddVacationRequest', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_vacation_request(self):
        """Add vacation request - handles exceptions here"""
        try:
            self.logger.info(f"AddVacationRequest called at {request.httprequest.url}")
            data = request.get_json_data()
            vacation_dto = RequestLogDTO.from_dict(data)
            self.logger.info(f"AddVacationRequest data: {data}")
            admin_user = request.env.ref('base.user_admin')
            response = self.hr_leave_model.with_user(admin_user).add_leave_request(vacation_dto , is_vacation=True)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeeVacationsByEmpNo', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_vacations_by_empno(self, empNo: str, dateFrom: str, dateTo: str):
        """Get vacations by emp no - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeeVacationsByEmpNo called at {request.httprequest.url}")
            dt_from, dt_to =_parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_employee_requests(empNo, dt_from, dt_to, filter_type="employee_number" , is_vacation=True)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeeVacationsByUserName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_vacations_by_username(self, userName: str, dateFrom: str, dateTo: str):
        """Get vacations by username - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeeVacationsByUserName called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_employee_requests(userName, dt_from, dt_to, filter_type="user_name" , is_vacation=True)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesVacations', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_vacations(self, dateFrom: str, dateTo: str):
        """Get all vacations - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeesVacations called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_all_requests(dt_from, dt_to, is_vacation=True)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesVacationsByDeptName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_vacations_by_dept_name(self, deptName: str,dateFrom: str,dateTo: str):
        """Get vacations by dept - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeesVacationsByDeptName called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_all_requests(dt_from, dt_to, deptName,filter_type="dept_name", is_vacation=True)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

