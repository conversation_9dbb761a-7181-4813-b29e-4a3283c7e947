from datetime import datetime

from odoo import http
from odoo.addons.ams_ta.controllers.base_controller import BaseController
from odoo.addons.ams_ta.helper.helper import _parse_date_range
from odoo.addons.ams_ta_time_off.api.dto_request_log import RequestLogDTO

from odoo.http import request
from odoo.exceptions import ValidationError
import json


class EmployeePermissionController(BaseController):

    @property
    def hr_leave_model(self):
        return self.ams_ta_request.env['hr.leave'].sudo()

    @http.route('/AddPermissionRequest', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_permission_request(self):
        """Add permission request - handles exceptions here"""
        try:
            self.logger.info(f"AddPermissionRequest called at {request.httprequest.url}")
            data = request.get_json_data()
            permission_dto = RequestLogDTO.from_dict(data)
            self.logger.info(f"AddPermissionRequest data: {data}")
            admin_user = request.env.ref('base.user_admin')
            response = self.hr_leave_model.with_user(admin_user).add_leave_request(permission_dto)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeePermissionsByEmpNo', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_permissions_by_empno(self,empNo: str,dateFrom: str,dateTo: str):
        """Get permissions by emp no - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeePermissionsByEmpNo called at {request.httprequest.url}")
            dt_from, dt_to =_parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_employee_requests(empNo, dt_from, dt_to,filter_type="employee_number")
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeePermissionsByUserName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employee_permissions_by_username(self, userName: str,dateFrom: str,dateTo: str):
        """Get permissions by username - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeePermissionsByUserName called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_employee_requests(userName, dt_from, dt_to, filter_type="user_name")
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesPermissions', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_permissions(self, dateFrom: str, dateTo: str):
        """Get all permissions - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeesPermissions called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)
            response = self.hr_leave_model.get_all_requests(dt_from, dt_to)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/GetEmployeesPermissionsByDeptName', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_employees_permissions_by_dept_name(self,deptName: str,dateFrom: str,dateTo: str):
        """Get permissions by dept - handles exceptions here"""
        try:
            self.logger.info(f"GetEmployeesPermissionsByDeptName called at {request.httprequest.url}")
            dt_from, dt_to = _parse_date_range(dateFrom, dateTo)

            response = self.hr_leave_model.get_all_requests(dt_from, dt_to, deptName,filter_type="dept_name", is_vacation=False)
            res_dict = response.to_dict()
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

