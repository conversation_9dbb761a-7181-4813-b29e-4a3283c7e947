from odoo import http, fields
from odoo.http import request
from datetime import datetime
from werkzeug.wrappers import Response
import json

class QueueController(http.Controller):

    @http.route('/trigger_counter', type='http', auth='public', methods=['GET'], csrf=False)
    def trigger_counter(self, category_name=None, **kwargs):
        """
        Endpoint to create a new ams_counter.queue record.
        Takes 'category_name' as input.
        """
        # Validation: Check if the category_name is provided
        if not category_name:
            return "Error: 'category_name' parameter is required."

        # Create a new record in 'ams_counter.queue'
        queue_record = request.env['ams_counter.queue'].sudo().create({
            'timestamp': fields.Datetime.now(),
            'category_name': category_name
        })

        # Prepare a success response with formatted timestamp
        response_data = {
            'success': True,
            'message': 'Record created successfully!',
            'record_id': queue_record.id,
            'name': queue_record.name
        }

        # Return the response as JSON
        return Response(
            json.dumps(response_data),
            status=200,
            content_type='application/json'
        )