from odoo import models, fields, api
import random
from datetime import datetime, timedelta


class CounterQueue(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_counter.queue"
    _description = "Counter Queue"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Name",default="New")
    timestamp = fields.Datetime(string="Timestamp", required=True)
    category_name = fields.Char(string="Category Name",default="New")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    date = fields.Date(string="Date", compute="_compute_datetime_fields", store=True)
    year = fields.Integer(string="Year", compute="_compute_datetime_fields", store=True)
    month = fields.Integer(string="Month", compute="_compute_datetime_fields", store=True)
    day = fields.Integer(string="Day", compute="_compute_datetime_fields", store=True)
    hour = fields.Integer(string="Hour", compute="_compute_datetime_fields", store=True)
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('timestamp')
    def _compute_datetime_fields(self):
        for record in self:
            if record.timestamp:
                timestamp_dt = fields.Datetime.context_timestamp(record, record.timestamp)
                record.date = timestamp_dt.date()
                record.year = timestamp_dt.year
                record.month = timestamp_dt.month
                record.day = timestamp_dt.day
                record.hour = timestamp_dt.hour
            else:
                record.date = False
                record.year = 0
                record.month = 0
                record.day = 0
                record.hour = 0
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self,vals):
        if isinstance(vals, dict):
            vals = [vals]  # convert dict to list of dicts
        for val in vals:
            if val.get('name', 'New') == 'New':
                val['name'] = self.env['ir.sequence'].next_by_code('ams.counter.queue') or 'New'

        res=super(CounterQueue, self).create(vals)
        self.send_auto_refresh()
        return res

    def write(self, vals):
        res = super(CounterQueue, self).write(vals)
        self.send_auto_refresh()
        return res
    # endregion]

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate_random_data(self):
        """Generate 100 random records with timestamp values for today and random category_name."""
        current_date = datetime.now().date()  # Get today's date
        for _ in range(100):
            # Generate a random time within today
            random_hour = random.randint(0, 23)  # Hours: 0-23
            random_minute = random.randint(0, 59)  # Minutes: 0-59
            random_second = random.randint(0, 59)  # Seconds: 0-59

            random_timestamp = datetime.combine(
                current_date,
                datetime.min.time()
            ) + timedelta(hours=random_hour, minutes=random_minute, seconds=random_second)

            random_category = random.choice(['enter', 'exit'])  # Random category name

            # Create the record
            self.create({
                'timestamp': random_timestamp,
                'category_name': random_category,
            })
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def send_auto_refresh(self):
        """auto refresh for treeview send trigger to bus channel 'auto_refresh' exist in module lp_auto_refresh
         so to work properly need install module lp_auto_refresh manually"""
        self.env['bus.bus']._sendone('auto_refresh', 'auto_refresh',
                                     {'model': self._name, 'id': self.id})
    # endregion
