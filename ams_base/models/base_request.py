# -*- coding: utf-8 -*-
from datetime import timed<PERSON>ta
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError



class BaseRequest(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.request'
    _description = 'Base Request'
    _inherit = ['ams_base.abstract_model', 'ams_base.follow_up_model']
    _sql_constraints = [
        ('unique_name', 'unique(name)', 'The name must be unique!')
    ]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    def _domain_approval_uid(self):

        if not self.env.user.department_id:
            return []

        # Get the current department and its parent departments
        current_department = self.env.user.department_id.id
        parent_departments = self.env['hr.department'].search([
            ('id', 'parent_of', current_department)
        ]).ids

        # Combine the current department and parent departments for managers
        manager_departments = parent_departments + [current_department]

        # Return the domain for supervisors and managers
        return [
            '|',
            '&',
            ('department_id', '=', current_department),  # Supervisors only in the current department
            ('level', '=', 'supervisor'),
            '&',
            ('department_id', 'in', manager_departments),  # Managers in current or higher departments
            ('level', '=', 'manager')
        ]

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Name", required=True, readonly=True, index=True, default='New', copy=False)
    subject = fields.Char(string='Subject', required=True)
    start_date = fields.Datetime(string='Start Date', required=True)
    duration = fields.Float(string='Duration', help="Duration in hours", default=1)
    request_date = fields.Datetime(string='Request Date', readonly=True, default=fields.Datetime.now, copy=False)
    response_date = fields.Datetime(string='Response Date', readonly=True)
    commit = fields.Char(string='Comments')
    state = fields.Selection(
        [('pending', 'Pending'), ('need_approval', 'Need Approval'), ('approved', 'Approved'),
         ('confirmed', 'Confirmed'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')],
        string='State', default='pending', tracking=True)
    status = fields.Selection(
        [('pending', 'Pending'), ('running', 'Running'), ('finished', 'Finished')],
        string='Status', default='pending', readonly=True, copy=False)
    email_state = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed')
    ], string="Email State", default='pending', readonly=True)

    schedule_type = fields.Selection([('single', 'Single Day'), ('multiple', 'Multiple Days')], string='Schedule Type',
                                     default='single', required=True)
    is_event = fields.Boolean(string='Is Event', default=False)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    request_uid = fields.Many2one('res.users', string='Requested By', readonly=True, default=lambda self: self.env.uid,
                                  copy=False)
    response_uid = fields.Many2one('res.users', string='Responded By', readonly=True, copy=False)
    approval_uid = fields.Many2one('res.users', string="Approval Manager", domain=_domain_approval_uid, copy=False)
    # endregion

    # region  Computed
    date = fields.Date(string='Date', compute='_compute_date', store=True)
    end_date = fields.Datetime(string="End Date", compute="_compute_end_date", store=True, readonly=False,
                               required=True)
    need_approval = fields.Boolean(string='Need Approval', default=False, compute='_compute_need_approval', copy=False)
    company_id = fields.Many2one('res.company', default=lambda self: self.env.user.company_id, string="Company")

    # endregion
    # region  Computed
    # endregion
    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('start_date')
    def _compute_date(self):
        for record in self:
            if record.start_date:
                record.date = fields.Datetime.context_timestamp(record, record.start_date).date()
            else:
                record.date = False

    @api.depends('start_date', 'duration')
    def _compute_end_date(self):
        for record in self:
            if record.start_date and record.duration:
                hours = int(record.duration)
                minutes = int((record.duration - hours) * 60)
                record.end_date = record.start_date + timedelta(hours=hours, minutes=minutes)
            else:
                record.end_date = False

    def _compute_need_approval(self):
        for rec in self:
            rec.need_approval = False

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for record in self:
            if record.end_date <= fields.Datetime.now():
                raise ValidationError(_("End Date must be in the future."))
            if record.start_date and record.end_date and record.end_date < record.start_date:
                raise ValidationError(_("End Date must be greater than Start Date."))

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def unlink(self):
        """
        Override unlink to prevent deletion of records with state 'pending'.
        """
        for record in self:
            if record.state != 'pending':
                raise UserError(f"You cannot delete a request in the {record.state} state.")
            record.send_auto_refresh()
        return super(BaseRequest, self).unlink()

    def create(self, vals):
        res = super(BaseRequest, self).create(vals)
        res.send_auto_refresh()
        return res

    def write(self, vals):
        res = super(BaseRequest, self).write(vals)
        for record in self:
            record.send_auto_refresh()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    # Your action method
    def action_open_request(self):
        """
        Generic method to open records created by the current user

        :return: Action dictionary for opening records
        :rtype: dict
        """

        # Prepare the domain to filter records created by the current user
        domain = []
        if self.env.user.level != 'user':
            domain = []
        else:
            domain = [('create_uid', '=', self.env.user.id)]

        # Prepare the action dictionary
        action = {
            'name': 'My Requests',
            'type': 'ir.actions.act_window',
            'res_model': 'ams_vm.invitation',
            'view_mode': 'list,form',
            'domain': domain,
            'context': {'create': False},  # Optional: prevent creating new records from this view
        }

        return action

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
