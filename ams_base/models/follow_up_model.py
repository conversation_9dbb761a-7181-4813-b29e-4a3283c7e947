# -*- coding: utf-8 -*-

from odoo import api, fields, models
from ast import literal_eval

import logging

_logger = logging.getLogger(__name__)


class BaseFollowUpModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_base.follow_up_model"
    _description = "Follow Up Model"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Properties ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------

    # region  Basic
    dept_depth = fields.Char(string='Department Depth', index=True,
                             help="Contain hierarchy of department depth when create record")
    last_dept_depth = fields.Char(string='Last Department Depth', index=True,
                                  related='last_department_id.dept_depth', store=True,
                                  help="Contain hierarchy  depth of last department assigned to employee")

    # endregion

    # region  Computed
    current_employee_level = fields.Selection([
        ('user', 'User'),
        ('supervisor', 'Supervisor'),
        ('manager', 'Manager'),
        ('administrator', 'Administrator'),
    ], string="Current Employee Level", compute="_compute_current_employee_level", store=False)
    # endregion

    # region  relation
    employee_id = fields.Many2one('hr.employee', help="this record owned to this employee as follow up")
    create_employee_id = fields.Many2one('hr.employee', string="Create By Employee")
    department_id = fields.Many2one('hr.department', string="Department",
                                    help="Employee department on record creation")
    last_department_id = fields.Many2one('hr.department', string="Last Department",
                                         related='employee_id.department_id', store=True,
                                         help="Last department assign to employee")
    manager_id = fields.Many2one('hr.employee', string="Employee Manager", related='employee_id.parent_id', store=True)
    coach_id = fields.Many2one('hr.employee', string="Employee Coach", related='employee_id.coach_id', store=True)
    employee_number = fields.Char(string="Employee Number", related='employee_id.employee_number', store=True)
    enroll_number = fields.Char(string="Enroll Number", related='employee_id.enroll_number', store=True)

    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # def _compute_current_employee_level(self):
    #     """
    #     Compute the current user's employee level.
    #     """
    #     user_level = self.env.user.level
    #     for record in self:
    #         record.current_employee_level = user_level

    def _compute_current_employee_level(self):
        valid_levels = ['user', 'supervisor', 'manager', 'administrator']
        for record in self:
            try:
                user_level = self.env.user.level or 'user'
                if user_level not in valid_levels:
                    _logger.warning("Invalid user level: %s, defaulting to 'user'", user_level)
                    record.current_employee_level = 'user'
                else:
                    record.current_employee_level = user_level
            except Exception as e:
                _logger.error("Error computing current_employee_level for model %s, record %s: %s", self._name,
                              record.id, e)
                record.current_employee_level = 'user'  # Fallback

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # Override read to force recomputation of current_employee_level
    def read(self, fields=None, load='_classic_read'):
        # Ensure current_employee_level is recomputed when the record is read
        if fields and 'current_employee_level' in fields:
            self._compute_current_employee_level()
        return super(BaseFollowUpModel, self).read(fields=fields, load=load)

    @api.model
    def _search(self, domain, offset=0, limit=None, order=None):
        # if self.browse().has_access('read'):
        #     return super()._search(domain, offset, limit, order)
        # and not self.env.context.get('from_emp',False)==True:
        if self.env.context.get('apply_followup_domain', 0) == 1:
            followup_domain = self._domain_follow_up()
            domain += followup_domain  # Add the follow-up domain to the existing domain

        return super(BaseFollowUpModel, self)._search(domain, offset=offset, limit=limit, order=order)

    def create(self, vals_list):
        _logger.info("Received vals_list: %s", vals_list)

        # Check if vals_list is a dictionary; if so, convert it into a list
        # if isinstance(vals_list, dict):
        #     vals_list = [vals_list]
        #
        # # Ensure vals_list is a list of dictionaries
        # if not isinstance(vals_list, list) or not all(isinstance(vals, dict) for vals in vals_list):
        #     raise ValueError(f"Expected a list of dictionaries for 'vals_list', but got {type(vals_list).__name__}.")

        current_employee = self.get_current_user_employee()
        res_records = super(BaseFollowUpModel, self).create(vals_list)
        for rec in res_records:
            if rec._name == 'hr.employee':
                rec.employee_id = rec
                if rec.department_id:
                    rec.dept_depth = rec.department_id.dept_depth

            elif not rec.employee_id:
                rec.employee_id = current_employee
                if rec.employee_id and rec.employee_id.department_id:
                    rec.department_id = current_employee.department_id.id
                    rec.dept_depth = current_employee.department_id.dept_depth
            if rec.employee_id:
                if rec.employee_id.department_id and not rec.department_id:
                    rec.department_id = rec.employee_id.department_id.id
        return res_records

        # for vals in vals_list:
        #     if self._name == 'hr.employee':
        #         emp_record = super(BaseFollowUpModel, self).create(vals_list)
        #         emp_record.employee_id = emp_record.id
        #         return emp_record
        #
        #     employee_id = vals.get('employee_id',False)
        #     if employee_id:
        #         current_employee = self.env['hr.employee'].browse(employee_id) # TODO: enhance performance
        #
        #     if current_employee and current_employee.department_id:
        #         # assign current employee department which record is created
        #         vals['employee_id'] = current_employee.id
        #         vals['department_id'] = current_employee.department_id.id
        #         vals['dept_depth'] = current_employee.department_id.dept_depth
        #
        # return super(BaseFollowUpModel, self).create(vals_list)

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    def action_open_views(self, action_info: dict):
        """
        Return action for opening views with a custom domain based on user access level.
        """
        # Determine user access level
        view_mode = action_info.get('view_mode', False) or self.follow_up_view_mode
        view_context = action_info.get('context', {})
        action_ref = action_info.get('action_ref', f'')  # f'{self._name}_action'
        use_domain_follow_up_visibility = action_info.get('use_domain_follow_up_visibility', False)

        # Fetch the action if it exists
        action_xml = self.env.ref(action_ref, raise_if_not_found=False)
        if use_domain_follow_up_visibility:
            domain = self.sudo()._domain_follow_up_visibility()
        else:
            domain = self._domain_follow_up()

        action = {
            'type': 'ir.actions.act_window',
            'name': self._description,
            'res_model': self._name,
            'view_mode': view_mode,
            'context': view_context,
            'domain': domain,
            'target': 'current',  # Ensure the top menu remains visible
            # 'flags': {'action_buttons': True, 'sidebar': False},  # Proper UI rendering
        }
        if action_xml:
            action.update(action_xml.read(['name', 'res_model', 'view_mode', 'context', 'help'])[0])

            if action_xml.domain:
                domain = (literal_eval(action_xml.domain) or []) + domain
                action['domain'] = domain
            # Update the domain of the existing action
            # action_xml.sudo().write({'domain': domain})
            # Ensure context is merged properly
            # action['context'] = {**eval(action.get('context', '{}')), **view_context}  # Merge dicts safely

        # TODO : handel open custom views based on external reference passed from context
        # form_view=self.env.ref(self.env.context.get('form_view_ref'), raise_if_not_found=False)
        # list_view = self.env.ref(self.env.context.get('list_view_ref'), raise_if_not_found=False)
        # kanban_view = self.env.ref(self.env.context.get('kanban_view_ref'), raise_if_not_found=False)
        #
        # views = []
        # if list_view:
        #     views.append((list_view.id, 'list'))
        #
        # if len(views) > 0:
        #     action['views'] = views

        return action

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    @property
    def follow_up_view_mode(self):
        """return basic view mode , we can override to change view mode in child classes"""
        return 'list,form'

    def _domain_follow_up(self):
        """return basic follow up domain based on custom user level , we can override to change domain
        user only access his own records
        supervisor only access his own department records
        manager only access his own department records and sub-departments
        admin access all records"""

        # Determine user access level
        user = self.env.user

        current_user_department = user.department_id
        # dept_field = "dept_depth" if self.env.company.follow_up_option == 'create_dept' else "last_dept_depth"
        dept_field = "department_id" if self.env.company.follow_up_option == 'create_dept' else "last_department_id"

        if user._is_admin() or user.level == 'administrator':
            return []

        if user.level == 'user' or not current_user_department:
            return [('create_uid', '=', user.id)]

        elif user.level == 'supervisor':
            return ['|', (dept_field, '=', current_user_department.id), (dept_field, '=', False)]

        elif user.level == 'manager':
            return ['|', '|', (dept_field, 'child_of', current_user_department.id),
                    (dept_field, '=', current_user_department.id), (dept_field, '=', False)]

        # return user_domain

    def _domain_follow_up_visibility(self):
        """return basic follow up domain based visitor visibility ,
        if create_user can only access his own visitor records
        if create_department Users in the same department as the creator can see the record
        if all , all users can access all visitor records
      """

        user = self.env.user
        visitor_visibility = self.env.company.visitor_visibility  # Get the visitor visibility setting

        # Admin can see all records
        if user._is_admin():
            return []

        # Handle different visibility scenarios based on the 'visitor_visibility' field
        if visitor_visibility == 'create_user':
            # Only the user who created the record can see it
            return [('create_uid', '=', user.id)]

        elif visitor_visibility == 'create_department':
            # Users in the same department as the creator can see the record
            return [('last_department_id', '=', user.department_id.id)]

        elif visitor_visibility == 'all':
            # All users can see the record
            return []

        return []

    def get_current_user_employee(self):
        """return an employee record for current user"""
        employee = self.env['hr.employee'].sudo().with_context(from_emp=True).search([('user_id', '=', self.env.uid)],
                                                                                     limit=1)
        return employee

    def get_current_user_department(self):
        """return a department record for current employee"""
        employee = self.sudo().get_current_user_employee()
        department = employee.department_id if employee else False
        return department

    # endregion
