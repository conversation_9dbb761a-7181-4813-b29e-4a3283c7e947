# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Employee(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = 'hr.employee'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    employee_number = fields.Char(string="Employee No", tracking=True)
    enroll_number = fields.Char(string="Enroll No", readonly=False, help="Device Enroll Number", tracking=True)
    follow_up_level = fields.Selection(related='user_id.level', store=True, readonly=False)
    user_name = fields.Char(related='user_id.login')

    # mobile app region

    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # device_group_id = fields.Many2one('ams.device_group', help="Default Device Group")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # @api.model
    # def _search(self, domain, offset=0, limit=None, order=None):
    #     if self.env.context.get('apply_followup_domain', 0) == 1:
    #         followup_domain = self._domain_follow_up()
    #         domain += followup_domain  # Add the follow-up domain to the existing domain
    #     return super(Employee, self)._search(domain, offset=offset, limit=limit, order=order)

    def _domain_follow_up(self):
        """return basic follow up domain based on custom user level , we can override to change domain
        user only access his own records
        supervisor only access his own department records
        manager only access his own department records and sub-departments
        admin access all records"""

        # Determine user access level
        user = self.env.user

        current_user_department = user.department_id
        # dept_field = "dept_depth" if self.env.company.follow_up_option == 'create_dept' else "last_dept_depth"
        # dept_field = "department_id" if self.env.company.follow_up_option == 'create_dept' else "last_department_id"

        if user._is_admin() or user.level == 'administrator':
            return []

        if user.level == 'user' or not current_user_department:
            return [('user_id', '=', user.id)]

        elif user.level == 'supervisor':
            return [('department_id', '=', current_user_department.id)]

        elif user.level == 'manager':
            return ['|', ('department_id', 'child_of', current_user_department.id),
                    ('department_id', '=', current_user_department.id)]

        # return user_domain
    # endregion
