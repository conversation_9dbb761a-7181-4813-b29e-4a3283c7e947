# -*- coding: utf-8 -*-
import inspect

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

import logging

_logger = logging.getLogger("APIClient")


class BaseAPIModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.api_model'
    _inherit = ['ams_base.abstract_model']
    _description = 'Base API Model'
    _sql_constraints = [
        ('unique_name', 'UNIQUE(name)', 'The name must be unique.')
    ]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string='Name', required=True)
    description = fields.Char(string='Description')
    record_id = fields.Integer(string='Record ID', readonly=True, index=True, copy=False)
    origin_id = fields.Integer(string='Origin ID', readonly=True, index=True, copy=False)
    synced = fields.Boolean(string='Synced', readonly=True, copy=False)
    need_to_sync = fields.Boolean(string='Need to Sync', readonly=True, copy=False , default=True)
    last_sync_date = fields.Datetime(string='Last Sync Date', readonly=True, copy=False)
    is_predefined = fields.Boolean(string='Is Predefined', readonly=True, copy=False)

    error = fields.Boolean(string='Error', copy=False)
    error_msg = fields.Char(string='Error Message', copy=False)
    response_code = fields.Char(string='Response Code', copy=False)
    

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def write(self, vals):
        vals_list = vals if isinstance(vals, list) else [vals]
        for val in vals_list:
            if any(field in self.sync_fields for field in val):
                val['need_to_sync'] = True
        res = super(BaseAPIModel, self).write(vals)
        return res

    def unlink(self):
        """
        Override the unlink method to prevent deletion of synced records.
        Raise a UserError if any of the records are synced.
        """
        if any(record.synced for record in self):
            raise UserError(_("You cannot delete records that are synced."))
        return super(BaseAPIModel, self).unlink()

    def action_unsync(self):
        self.write({'synced': False})

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_reset_error(self):
        self.reset_error()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def copy(self, default=None):
        """
        override the copy method to add some character to the unique fields in order to avoid the redundancy
        which obstruct the duplication action.
        """
        default = dict(default or {})
        added_char = "_Copy(%s)" % self.id
        default.update({
            'name': self.name + added_char,
        })
        return super(BaseAPIModel, self).copy(default)

    @property
    def api_client(self):
        return self.env['ams_base.api_client'].sudo()  # to be override in child model

    @property
    def logger(self):
        return _logger

    @property
    def origin_model(self):
        """ override this method in target api model (Biostar)
         to return mapping for origin  model in access management module
          example ams_bs.user_group -> ams.user_group """
        return self.env[self._name].sudo()  # this just for initialization should be override in target model

    @property
    def sync_fields(self):
        """
        Property to return array of field names that should be checked for sync.
        Override this property in inherited models.
        """
        return ['name', 'description']

    def handle_api_sync_response(self, response):
        """
        Handle the synchronous API response and update the values if the response code is "1" (indicating success).
        The function takes the response as a parameter and does not return any values.
        """
        # if self.api_client.is_success_response(response):
        #     vals = self.get_response_dict(response)
        #     if vals:
        #         self.create_or_update(vals)
        # else:
        #     self.assign_error(response)
        ...

    @property
    def record_ids(self):
        return [rec.record_id for rec in self]

    def get_res_id(self, record_id):
        """return odoo model record id (id) based on API model record id (record_id)"""
        record = self.env[self._name].search([('record_id', '=', record_id)])
        return record.id if record else False

    def get_res_ids(self, record_ids):
        """return odoo model record id (id) based on API model record id (record_id)"""
        if not isinstance(record_ids, list):
            record_ids = [record_ids]
        records = self.env[self._name].search([('record_id', 'in', record_ids)])
        return records.ids if records else []

    def get_record(self, record_id):
        """return odoo model record  based on API model record id (record_id)"""
        if record_id != 0:
            return self.env[self._name].search([('record_id', '=', record_id)], limit=1)
        else:
            return self.env[self._name]

    def _prepare_record_vals(self, response_record, origin=False, **kwargs):
        """override this method in target model to mapping api response to odoo record values"""
        return {}

    def _sync_record(self, response_record, **kwargs):
        """sync record from api to odoo and create copy in origin model if sync_option is auto
        example when sync  ams_bs.user_group -> copy record to origin model ams.user_group """
        vals = self._prepare_record_vals(response_record, **kwargs)
        record = self.create_or_update(vals)

        if self.env.company.sync_option == 'auto':
            vals_origin = self._prepare_record_vals(response_record, origin=True, **kwargs)
            record_origin = self.origin_model.create_or_update(vals_origin)
            self.origin_id = record_origin.id
        return record

    def create_or_update(self, vals, synced=True):
        """
        Create or update a record based on the provided record values.

        :param vals: A dictionary containing the values for the zone
        :type vals: dict
        :return: The updated or newly created  record
        :rtype:  Recordset
        """
        api_record_id = vals.get("record_id")
        api_record_name = vals.get("name")

        record_obj = self.env[self._name].search(
            ['|', ("record_id", "=", api_record_id), ("name", "=", api_record_name)], limit=1)
        if synced:
            vals.update({"synced": True, "last_sync_date": fields.Datetime.now(), 'company_id': self.env.company.id , 'need_to_sync': False})

        if record_obj:
            record_obj.update(vals)
            return self.browse(record_obj.id)
        else:
            new_record = self.create(vals)
            return new_record

    def reset_error(self):
        self.write({"error_msg": "", "error": False})

    def get_call_hierarchy(self):
        """Returns a string representing the call hierarchy."""
        stack = inspect.stack()
        # Skip the first few frames to avoid including the logging code itself
        stack_hierarchy = [
            frame.function for frame in stack[2:]  # Start from the caller's frame
        ]
        # Return the hierarchy joined by dots
        return ".".join(reversed(stack_hierarchy))

    def assign_error(self, response, raise_error=False):
        error_code = response.get("response_code", "-1")
        error_msg = response.get("response_message", "Unknown Error")
        error = f"Error Code:{error_code}, Message:{error_msg}, Details: {self.get_call_hierarchy()}"
        self.logger.error(error)
        self.write({"error_msg": error, "error": True})
        if raise_error:
            raise UserError(error)

    def get_api_model_prefix(self, api_type):
        if api_type == 'suprema':
            return "ams_bs"
        elif api_type == 'zk':
            return "ams_bs"  # TODO Update
        else:
            return "ams"

    def get_api_model(self, api_type):
        # replace prefix of current model _name and update prefix based on api_type
        model_prefix = self.get_api_model_prefix(api_type)
        model_name = self._name.split('.')[1]
        api_model_name = f"{model_prefix}.{model_name}"
        return self.env[api_model_name].sudo()

    def get_mapping_api_ids(self, api_type):
        """
        return odoo models ids corresponding to api models
        """
        record_ids = self.record_id  # if len(self)>1 else [self.record_id]
        api_model = self.get_api_model(api_type)
        return api_model.get_res_ids(record_ids)

    def prepare_vals(self, source_record, field_mapping=None):
        """
        Prepare values for non-relational fields dynamically.

        :param source_record: Recordset of the source model.
        :param field_mapping: Optional dictionary for mapping field names between models.
        :return: Dictionary of prepared values for the target model.
        """
        field_mapping = field_mapping or {}  # Default to empty mapping
        prepared_vals = {}

        for field_name, field_obj in source_record._fields.items():
            # Skip relational fields and technical fields
            if isinstance(field_obj, (fields.Many2one, fields.Many2many, fields.One2many)) or field_name in ['id',
                                                                                                             '__last_update']:
                continue

            # Determine the target field name (use mapping if provided)
            target_field_name = field_mapping.get(field_name, field_name)

            # Extract the value from the source record
            prepared_vals[target_field_name] = source_record.__getitem__(field_name)

        return prepared_vals
    # endregion

    # region ---------------------- TODO[IMP]: Property Methods -------------------------------------

    # endregion


class BaseAPIModelChatter(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = 'ams_base.api_model_chatter'
    _inherit = ['ams_base.api_model', 'mail.thread', 'mail.activity.mixin']
    _description = 'Api Model Chatter'
    company_id = fields.Many2one('res.company', default=lambda self: self.env.user.company_id, string="Company")
