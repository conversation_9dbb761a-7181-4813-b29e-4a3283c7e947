from odoo.addons.ams_ta_mobile.api.dto_timeoff import *
from odoo import http
from odoo.addons.ams_ta_mobile.api.dto_emp_att_log import EmpAttendanceLogDTO
from odoo.addons.ams_ta_mobile.api.dto_mobile_att import AttendanceParameterDTO, MobileAttendanceDTO
from odoo.http import request, Response
import json
from odoo.addons.ams_ta_mobile.api.dto_employee import *
from odoo.addons.ams_ta.controllers.base_controller import BaseController


class MobileAppController(BaseController):

    @http.route('/MRegisterApp', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def register_app(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            self.logger.info(f"MRegisterApp TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            self.logger.info(f"MRegisterApp auth_data: {auth_data} ")
            auth_dto = AuthDTO.from_dict(auth_data)
            self.logger.info("MRegisterApp AuthDTO.from_dict(auth_data)")
        except ValueError as ex:
            self.logger.error(f"MRegisterApp return response error: {ex} ")
            return request.make_response('Invalid JSON data', status=200)
            # json.dumps({'error': 'Invalid JSON data'})

        # Access the 'authData' field from the JSON object
        if auth_dto:
            # auth_data = auth_data['authData']
            # Do something with the 'authData'
            res = self.employee_model.register_app(auth_dto)
            # Return a response as JSON
            # response = {'message': res}
            self.logger.info(f"MRegisterApp return response string: {res} ")
            return request.make_response(res, status=200)
            # json.dumps(response)
            # http.Response(json.dumps(response), content_type='application/json',
            #                  status=response.get("response_code"))

        return request.make_response('Invalid request', status=200)  # json.dumps({'error': 'Invalid request'})

    @http.route('/MAuthenticate', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def authenticate(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        res_dict = {}

        try:
            self.logger.info(f"MAuthenticate TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            self.logger.info(f"MAuthenticate auth_data {auth_data} ")

            auth_dto = AuthDTO.from_dict(auth_data)
            self.logger.info(f"MAuthenticate auth_dto {auth_dto} ")

            db_name = request.session.db

            request.session.clear()

            # **Force logout any existing session before authenticating**
            old_session = request.session.sid
            user = request.session.uid

            # Prepare credentials dictionary
            credentials = {
                "login": auth_dto.username,
                "password": auth_dto.password,
                "type": 'password'
            }

            # Authenticate using dictionary-based credentials
            uid = None
            try:
                uid = request.session.authenticate(db_name, credentials)
            except Exception as auth_ex:
                self.logger.error(f"MAuthenticate authentication error: {auth_ex}")

            if not uid:
                res_dict = {
                    'response_code': "104",  # Custom error code for authentication failure
                    'response_message': "Invalid username or password",
                    'response_message_ar': "اسم المستخدم أو كلمة المرور غير صحيحة"
                }
                self.logger.error(f"MAuthenticate failed: {res_dict}")
                return request.make_json_response(res_dict, status=200)

            auth_dto.session_id = request.session.sid
            self.logger.info(f"MAuthenticate new session_id {auth_dto.session_id}")

            # Proceed with mobile authentication
            if auth_dto:
                res = self.employee_model.mobile_authenticate(auth_dto, True)
                res_dict = res.to_dict()
                self.logger.info(f"MAuthenticate success {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

        # Default error response
        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot access login information',
            'response_message_ar': 'خطأ: لا يمكن الوصول إلى معلومات تسجيل الدخول'

        }
        return request.make_json_response(res_dict, status=400)

    @http.route('/MGetEmployee', type='http', auth='user', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def get_employee(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            self.logger.info(f"MGetEmployee TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            self.logger.info(f"MGetEmployee auth_data: {auth_data} ")
            auth_dto = AuthDTO.from_dict(auth_data)
            if auth_dto:
                res = self.employee_model.get_employee(auth_dto)
                res_dict = res.to_dict()
                self.logger.info(f"MGetEmployee return response EmployeeDTO: {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot get employee data',
            'response_message_ar': 'خطأ: لا يمكن الحصول على بيانات الموظف'
        }

        return request.make_json_response(res_dict, status=200)

    @http.route('/MGetEmployeeAttendance', type='http', auth='user', methods=['POST', 'OPTIONS'], csrf=False,
                cors="*")
    def get_employee_attendance(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            self.logger.info(f"MGetEmployeeAttendance TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            self.logger.info(f"MGetEmployeeAttendance auth_data: {auth_data} ")
            auth_dto = AttendanceParameterDTO.from_dict(auth_data)
            if auth_dto:
                res = self.timesheet_model.get_employee_attendance(auth_dto)
                self.logger.info(f"MGetEmployeeAttendance return response: {res} ")
                res_dict = res.to_dict()
                self.logger.info(f"MGetEmployeeAttendance return response MobileAttendanceDTO: {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot get employee attendance',
            'response_message_ar': 'خطأ: لا يمكن الحصول على حضور الموظف'

        }
        self.logger.error(f"MGetEmployeeAttendance return response error: {res_dict} ")
        return request.make_json_response(res_dict, status=200)

    @http.route('/MAddAttendance', type='http', auth='user', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_attendance_log(self, **kwargs):
        try:
            self.logger.info(f"MAddAttendance TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            self.logger.info(f"MAddAttendance auth_data: {auth_data} ")
            auth_dto = EmpAttendanceLogDTO.from_dict(auth_data)
            res = self.timesheet_model.add_attendance_log(auth_dto)
            res_dict = res.to_dict()
            self.logger.info(f"MAddAttendance return response EmpAttendanceLogDTO: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/MGetTimeOffTypes', type='http', auth='user', methods=['get'], csrf=False, cors="*")
    def get_timeoff_types(self, time_off_category: str):
        try:
            result = request.env['hr.leave.type'].sudo().get_timeoff_types_by_category(time_off_category)
            res_dict = result.to_dict()
            self.logger.info(f"MGetTimeOffTypes return response Successfully")
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/MGetTime_off', type='http', auth='user', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def get_timeoff(self):
        try:
            payload = request.get_json_data()
            auth_dto = AuthDTO.from_dict(payload)
            result = self.hr_leave_model.get_time_off(auth_dto)
            res_dict = result.to_dict()
            self.logger.info(f"MGetTimeOff return response Successfully")
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)

    @http.route('/MAddTimeOffLog', type='http', auth='user', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_timeoff_log(self):
        try:
            payload = request.get_json_data()
            emp_timeoff_dto = EmpTimeOffDTO.from_dict(payload)
            result = request.env['hr.leave'].sudo().add_timeoff_log(emp_timeoff_dto)
            res_dict = result.to_dict()
            self.logger.info(f"MAddTimeOffLog return response : {res_dict} ")
            return request.make_json_response(res_dict, status=200)
        except Exception as ex:
            return self.handle_api_error(ex)
