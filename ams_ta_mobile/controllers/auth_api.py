import requests
from odoo import http
from odoo.http import request
from odoo.addons.ams_ta.controllers.base_controller import BaseController


class AuthAPIController(BaseController):

    @http.route('/api/user/info', type='http', auth="user", methods=['GET'], csrf=False)
    def user_info(self):
        """ Get user details using session authentication """
        user = request.env.user
        result = {
            'user_id': user.id,
            'name': user.name,
            'email': user.email,
            'login': user.login,
            'session_id': request.session.sid
        }
        return request.make_json_response(result, status=200)

    # @http.route('/api/logout', type='http', auth="user", methods=['POST'], csrf=False)
    # def logout(self):
    #     request.session.logout()
    #     return {'success': True, 'message': 'Logged out successfully'}

    # add get isalive method with auth
    @http.route('/api/isalive', type='http', auth="public", methods=['GET'], csrf=False)
    def isalive(self):
        # Your logic to process the request and get the result
        self.logger.info(f"Service is alive {request.httprequest.url}")
        result = {
            "version": "********",
            "live": "Ok",
        }
        # _logger.info(f"Test API result {result} ")
        # data = request.get_json_data()

        return request.make_json_response(result, status=200)

    @http.route('/api/auth-rpc', type='http', auth="public", methods=['POST'], csrf=False)
    def auth_rpc(self, **kwargs):
        try:
            auth_data = request.get_json_data()
            old_session_id = request.session.sid  # Store old session ID

            odoo_url = request.httprequest.host_url.rstrip('/')
            session_url = f"{odoo_url}/web/session/authenticate"

            data = {
                'jsonrpc': '2.0',
                'method': 'call',
                'params': {
                    'db': request.session.db,  # Use the current database
                    'login': auth_data.get('login'),
                    'password': auth_data.get('password'),
                }
            }

            # Call Odoo authentication endpoint
            session_response = requests.post(session_url, json=data)

            if session_response.status_code == 200:
                session_data = session_response.json()

                if session_data.get('result') and session_response.cookies.get('session_id'):
                    new_session_id = session_response.cookies['session_id']

                    user = request.env['res.users'].sudo().search([('login', '=', auth_data.get('login'))], limit=1)

                    result = {
                        'success': True,
                        'old_session_id': old_session_id,
                        'new_session_id': new_session_id,
                        'user_id': user.id,
                        'name': user.name,
                        'login': user.login
                    }

                    response = request.make_json_response(result, status=200)
                    response.set_cookie('session_id', new_session_id)  # Set session_id in cookies
                    return response

            return request.make_json_response({'success': False, 'error': 'Authentication failed'}, status=401)

        except Exception as e:
            return request.make_json_response({'success': False, 'error': str(e)}, status=500)
