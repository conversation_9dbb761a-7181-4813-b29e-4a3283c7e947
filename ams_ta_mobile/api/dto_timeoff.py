from odoo.addons.ams_ta.api.utils import *


class TimeOffType:
    id: int
    name: str

    def __init__(self, id: int, name: str) -> None:
        self.id = id
        self.name = name

    @staticmethod
    def from_dict(obj: Any) -> 'TimeOffType':
        assert isinstance(obj, dict)
        id = from_int(obj.get("id"))
        name = from_str(obj.get("name"))
        return TimeOffType(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_int(self.id)
        result["name"] = from_str(self.name)
        return result


class TimeoffTypesDTO:
    time_off_type: List[TimeOffType]
    response_code: int
    response_message: str
    response_message_ar: str = ''

    def __init__(self, time_off_type: List[TimeOffType], response_code: int, response_message: str,
                 response_message_ar: str = '') -> None:
        self.time_off_type = time_off_type
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar

    @staticmethod
    def from_dict(obj: Any) -> 'TimeoffTypesDTO':
        assert isinstance(obj, dict)
        time_off_type = from_list(TimeOffType.from_dict, obj.get("TimeOffType"))
        response_code = int(from_str(obj.get("ResponseCode")))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAr"))
        return TimeoffTypesDTO(time_off_type, response_code, response_message, response_message_ar)

    def to_dict(self) -> dict:
        result: dict = {}
        result["TimeOffType"] = from_list(lambda x: to_class(TimeOffType, x), self.time_off_type)
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAr"] = from_str(self.response_message_ar)
        return result


def timeoff_types_dto_from_dict(s: Any) -> TimeoffTypesDTO:
    return TimeoffTypesDTO.from_dict(s)


def timeoff_types_dto_to_dict(x: TimeoffTypesDTO) -> Any:
    return to_class(TimeoffTypesDTO, x)


# _______________________________________________________________________________


class TimeOff:
    holiday_status_id: int
    holiday_status_name: str
    description: str
    date_from: datetime
    date_to: datetime
    hour_from: str
    hour_to: str
    duration_display: str
    state: str
    state_display: str

    def __init__(self, holiday_status_id: int, holiday_status_name: str, description: str, date_from: datetime,
                 date_to: datetime, hour_from: str, hour_to: str, duration_display: str, state: str,
                 state_display: str) -> None:
        self.holiday_status_id = holiday_status_id
        self.holiday_status_name = holiday_status_name
        self.description = description
        self.date_from = date_from
        self.date_to = date_to
        self.hour_from = hour_from
        self.hour_to = hour_to

        self.duration_display = duration_display
        self.state = state
        self.state_display = state_display

    @staticmethod
    def from_dict(obj: Any) -> 'TimeOff':
        assert isinstance(obj, dict)
        holiday_status_id = from_int(obj.get("holiday_status_id"))
        holiday_status_name = from_str(obj.get("holiday_status_name"))
        description = from_str(obj.get("description"))
        date_from = from_datetime(obj.get("date_from"))
        date_to = from_datetime(obj.get("date_to"))
        hour_from = from_str(obj.get("hour_from"))
        hour_to = from_str(obj.get("hour_to"))
        duration_display = from_str(obj.get("duration_display"))
        state = from_str(obj.get("state"))
        state_display = from_str(obj.get("state_display"))
        return TimeOff(holiday_status_id, holiday_status_name, description, date_from, date_to, hour_from, hour_to,
                       duration_display, state, state_display)

    def to_dict(self) -> dict:
        result: dict = {}
        result["holiday_status_id"] = from_int(self.holiday_status_id)
        result["holiday_status_name"] = from_str(self.holiday_status_name)
        result["description"] = from_str(self.description)
        result["date_from"] = self.date_from.strftime("%Y-%m-%d")
        result["date_to"] = self.date_to.strftime("%Y-%m-%d")
        result["hour_from"] = self.hour_from
        result["hour_to"] = self.hour_to
        result["duration_display"] = from_str(self.duration_display)
        result["state"] = from_str(self.state)
        result["state_display"] = from_str(self.state_display)
        return result


class TimeOffDTO:
    time_off: List[TimeOff]
    response_code: int
    response_message: str
    response_message_ar: str = ''

    def __init__(self, time_off: List[TimeOff], response_code: int, response_message: str,
                 response_message_ar: str = '') -> None:
        self.time_off = time_off
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar

    @staticmethod
    def from_dict(obj: Any) -> 'TimeOffDTO':
        assert isinstance(obj, dict)
        time_off = from_list(TimeOff.from_dict, obj.get("TimeOff"))
        response_code = int(from_str(obj.get("ResponseCode")))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAr"))
        return TimeOffDTO(time_off, response_code, response_message, response_message_ar)

    def to_dict(self) -> dict:
        result: dict = {}
        result["TimeOff"] = from_list(lambda x: to_class(TimeOff, x), self.time_off)
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAr"] = from_str(self.response_message_ar)
        return result


def time_off_dto_from_dict(s: Any) -> TimeOffDTO:
    return TimeOffDTO.from_dict(s)


def time_off_dto_to_dict(x: TimeOffDTO) -> Any:
    return to_class(TimeOffDTO, x)


# _______________________________________________________________________________

class EmpTimeOffDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    app_id: int
    app_version: str
    device_info: str
    emp_no: str
    hr_code: str
    token: str
    username: str
    timeoff_type_id: int
    date_time_from: Optional[datetime]
    date_time_to: Optional[datetime]
    timeoff_category: int
    date_from: Optional[datetime]
    date_to: Optional[datetime]
    request_unit_hour: bool
    hour_from: float
    hour_to: float
    hour_from_display: str
    hour_to_display: str

    def __init__(self, response_code: str, response_message: str, response_message_ar: str, app_id: int,
                 app_version: str, device_info: str,
                 emp_no: str, hr_code: str, token: str, username: str, timeoff_type_id: int,
                 date_time_from: Optional[datetime], date_time_to: Optional[datetime], timeoff_category: int,
                 date_from: Optional[datetime], date_to: Optional[datetime], request_unit_hour: bool,
                 hour_from: float, hour_to: float, hour_from_display: str, hour_to_display: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.app_id = app_id
        self.app_version = app_version
        self.device_info = device_info
        self.emp_no = emp_no
        self.hr_code = hr_code
        self.token = token
        self.username = username
        self.timeoff_type_id = timeoff_type_id
        self.date_time_from = date_time_from
        self.date_time_to = date_time_to
        self.timeoff_category = timeoff_category
        self.date_from = date_from
        self.date_to = date_to
        self.request_unit_hour = request_unit_hour
        self.hour_from = hour_from
        self.hour_to = hour_to
        self.hour_from_display = hour_from_display
        self.hour_to_display = hour_to_display

    @staticmethod
    def from_dict(obj: Any) -> 'EmpTimeOffDTO':
        assert isinstance(obj, dict)

        def parse_datetime(date_str):
            if not date_str:
                return None
            formats = ('%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S',
                       '%d-%m-%Y %H:%M:%S', '%d/%m/%Y %H:%M:%S',
                       '%Y-%m-%d %H:%M', '%Y-%m-%d',)
            for fmt in formats:
                try:
                    return datetime.strptime(date_str.strip(), fmt)
                except ValueError:
                    continue
            raise ValueError(f"Invalid datetime format: {date_str}")

        response_code = str(obj.get("ResponseCode", ""))
        response_message = str(obj.get("ResponseMessage", ""))
        response_message_ar = str(obj.get("ResponseMessageAr", ""))
        app_id = int(obj.get("AppId", 0))
        app_version = str(obj.get("AppVersion", ""))
        device_info = str(obj.get("DeviceInfo", ""))
        emp_no = str(obj.get("EmpNo", ""))
        hr_code = str(obj.get("HRCode", ""))
        token = str(obj.get("Token", ""))
        username = str(obj.get("Username", ""))
        timeoff_type_id = int(obj.get("TimeOffTypeID", 0))
        date_time_from = parse_datetime(obj.get("DateTimeFrom"))
        date_time_to = parse_datetime(obj.get("DateTimeTo"))
        timeoff_category = int(obj.get("TimeOffCategory", 0))
        date_from = parse_datetime(obj.get("DateFrom") or "")
        date_to = parse_datetime(obj.get("DateTo") or "")
        request_unit_hour = bool(obj.get("RequestUnitHour", False))
        hour_from = (obj.get("HourFrom", ""))
        hour_to = (obj.get("HourTo", ""))
        hour_from_display = str(obj.get("HourFromDisplay", ""))
        hour_to_display = str(obj.get("HourToDisplay", ""))

        return EmpTimeOffDTO(response_code, response_message, response_message_ar, app_id, app_version, device_info,
                             emp_no,
                             hr_code, token, username, timeoff_type_id, date_time_from, date_time_to,
                             timeoff_category, date_from, date_to, request_unit_hour, hour_from, hour_to,
                             hour_from_display, hour_to_display)

    def to_dict(self) -> dict:
        result: dict = {
            "ResponseCode": self.response_code,
            "ResponseMessage": self.response_message,
            "ResponseMessageAr": self.response_message_ar,
            "AppId": str(self.app_id),
            "AppVersion": self.app_version,
            "DeviceInfo": self.device_info,
            "EmpNo": self.emp_no,
            "HRCode": self.hr_code,
            "Token": self.token,
            "Username": self.username,
            "TimeOffTypeID": self.timeoff_type_id,
            "DateTimeFrom": self.date_time_from.strftime('%Y-%m-%d') if self.date_time_from else "",
            "DateTimeTo": self.date_time_to.strftime('%Y-%m-%d') if self.date_time_to else "",
            "TimeOffCategory": self.timeoff_category,
            "DateFrom": self.date_from.strftime('%Y-%m-%d') if self.date_from else "",
            "DateTo": self.date_to.strftime('%Y-%m-%d') if self.date_to else "",
            "HourFrom": self.hour_from,
            "HourTo": self.hour_to,
            "HourFromDisplay": self.hour_from_display,
            "HourToDisplay": self.hour_to_display

        }
        return result
