from odoo.addons.ams_ta.api.utils import *
from dataclasses import dataclass


@dataclass
class EmpAttendanceLogDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    app_id: str
    app_version: str
    device_info: str
    emp_no: str
    hr_code: str  # enroll_no
    token: str
    username: str
    date_time_string: str  # DATE_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss"; , if empty replace time now
    latitude: float
    longitude: float
    time: str
    type: int  # -- 0 action taken from user, 1 = automated action

    @staticmethod
    def from_dict(obj: Any) -> 'EmpAttendanceLogDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAr"))
        app_id = from_str(obj.get("AppId"))
        app_version = from_str(obj.get("AppVersion"))
        device_info = from_str(obj.get("DeviceInfo"))
        emp_no = from_str(obj.get("EmpNo"))
        hr_code = from_str(obj.get("HRCode"))
        token = from_str(obj.get("Token"))
        username = from_str(obj.get("Username"))
        date_time_string = from_str(obj.get("DateTimeString"))  # DATE_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss";
        latitude = from_float(obj.get("Latitude"))
        longitude = from_float(obj.get("Longitude"))
        time = from_str(obj.get("Time"))
        type = from_int(obj.get("Type"))
        return EmpAttendanceLogDTO(response_code, response_message, response_message_ar, app_id, app_version,
                                   device_info, emp_no, hr_code,
                                   token, username, date_time_string, latitude, longitude, time, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAr"] = from_str(self.response_message_ar)
        result["AppId"] = from_str(self.app_id)
        result["AppVersion"] = from_str(self.app_version)
        result["DeviceInfo"] = from_str(self.device_info)
        result["EmpNo"] = from_str(self.emp_no)
        result["HRCode"] = from_str(self.hr_code)
        result["Token"] = from_str(self.token)
        result["Username"] = from_str(self.username)
        result["DateTimeString"] = from_str(self.date_time_string)
        result["Latitude"] = to_float(self.latitude)
        result["Longitude"] = to_float(self.longitude)
        result["Time"] = from_str(self.time)
        result["Type"] = from_int(self.type)
        return result


def emp_attendance_log_dto_from_dict(s: Any) -> EmpAttendanceLogDTO:
    return EmpAttendanceLogDTO.from_dict(s)


def emp_attendance_log_dto_to_dict(x: EmpAttendanceLogDTO) -> Any:
    return to_class(EmpAttendanceLogDTO, x)


"""
The concept of a DTO is to encapsulate data into an object 
that can be easily passed between layers of an application.
 In other words, it is an object that serves as a container 
 for data that needs to be transferred from one place to another. 
 This can be especially useful in situations 
 where data needs to be sent over a network or stored in a database.
 
 By using a DTO, the data can be structured in a way that is easy to work with and understand.
  It also helps to keep the code organized and maintainable 
  by separating the data from the logic of the application.

In summary, a DTO is a simple container for data that helps 
to transfer data between different parts of an application.
 It can make the code more organized and maintainable, 
 and it can be especially useful in situations 
 where data needs to be transferred over a network or stored in a database.
"""
