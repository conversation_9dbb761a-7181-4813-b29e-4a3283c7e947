from odoo.addons.ams_ta.api.utils import *
from dataclasses import dataclass


@dataclass
class AuthDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    app_id: str = ''
    app_version: str = ''
    device_info: str = ''
    emp_no: str = ''
    hr_code: str = ''  # enroll_no
    token: str = ''
    username: str = ''
    auth_type: int = 1  # 0= Active directory, 1= user&pass, 2= user name only, 3= external
    domain: str = ''
    external_auth: bool = False  # example: Google auth
    is_mobile_device: bool = True  # authenticate
    password: str = ''  # Ensure this password is hashed and salted!
    # New fields for filtering time off records
    timeoff_type: int = 0
    timeoff_category: int = 0
    date_from: str = ''
    date_to: str = ''

    @staticmethod
    def from_dict(obj: Any) -> 'AuthDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        app_id = from_str(obj.get("AppId"))
        app_version = from_str(obj.get("AppVersion"))
        device_info = from_str(obj.get("DeviceInfo"))
        emp_no = from_str(obj.get("EmpNo"))
        hr_code = from_str(obj.get("HRCode"))
        token = from_str(obj.get("Token"))
        username = from_str(obj.get("Username"))
        auth_type = from_int(obj.get("AuthType") or 1)
        domain = from_str(obj.get("Domain"))
        external_auth = from_bool(obj.get("ExternalAuth") or False)
        is_mobile_device = from_bool(obj.get("IsMobileDevice") or True)
        password = from_str(obj.get("Password"))
        # Extract new fields with default values if not provided
        # ✅ تأكد من استخراج القيم الصحيحة من JSON
        timeoff_category = int(obj.get("TimeOffCategory", 0))
        timeoff_type = int(obj.get("TimeOffTypeID", 0))

        logging.info(f"Parsed TimeOffCategory: {timeoff_category}, Parsed TimeOffTypeID: {timeoff_type}")

        date_from = from_str(obj.get("DateFrom") or "")
        date_to = from_str(obj.get("DateTo") or "")
        return AuthDTO(
            response_code,
            response_message,
            response_message_ar,
            app_id,
            app_version,
            device_info,
            emp_no,
            hr_code,
            token,
            username,
            auth_type,
            domain,
            external_auth,
            is_mobile_device,
            password,
            timeoff_category,
            timeoff_type,
            date_from,
            date_to
        )

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["ResponseMessage"] = from_str(self.response_message)
        result["AppId"] = from_str(self.app_id)
        result["AppVersion"] = from_str(self.app_version)
        result["DeviceInfo"] = from_str(self.device_info)
        result["EmpNo"] = from_str(self.emp_no)
        result["HRCode"] = from_str(self.hr_code)
        result["Token"] = from_str(self.token)
        result["Username"] = from_str(self.username)
        result["AuthType"] = from_int(self.auth_type)
        result["Domain"] = from_str(self.domain)
        result["ExternalAuth"] = from_bool(self.external_auth)
        result["IsMobileDevice"] = from_bool(self.is_mobile_device)
        result["Password"] = from_str(self.password)
        result["TimeOffCategory"] = from_int(self.timeoff_category)
        result["TimeOffTypeID"] = from_int(self.timeoff_type)

        logging.info(f"TimeOffCategory: {self.timeoff_category}, TimeOffTypeID: {self.timeoff_type}")

        result["DateFrom"] = from_str(self.date_from)
        result["DateTo"] = from_str(self.date_to)
        return result


@dataclass
class EmployeeDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    app_id: str = ''
    app_version: str = ''
    device_info: str = ''
    emp_no: str = ''
    hr_code: str = ''  # enroll_number
    token: str = ''
    username: str = ''
    arabic_name: str = ''
    area_arabic_name: str = ''
    area_english_name: str = ''
    bg_arabic_name: str = ''  # user_group
    bg_english_name: str = ''  # user_group
    branch_arabic_name: str = ''
    branch_english_name: str = ''
    dept_arabic_name: str = ''
    dept_english_name: str = ''
    email: str = ''
    english_name: str = ''
    error_message: str = ''
    phone_no: str = ''
    user_name: str = ''

    def __init__(self, app_id, app_version, device_info, emp_no,
                 hr_code, token, username):
        # self.response_code = response_code
        # self.response_message = response_message
        self.app_id = app_id
        self.app_version = app_version
        self.device_info = device_info
        self.emp_no = emp_no
        self.hr_code = hr_code
        self.token = token
        self.username = username

    @staticmethod
    def from_dict(obj: Any) -> 'EmployeeDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        app_id = from_str(obj.get("AppId"))
        app_version = from_str(obj.get("AppVersion"))
        device_info = from_str(obj.get("DeviceInfo"))
        emp_no = from_str(obj.get("EmpNo"))
        hr_code = from_str(obj.get("HRCode"))
        token = from_str(obj.get("Token"))
        username = from_str(obj.get("Username"))
        arabic_name = from_str(obj.get("ArabicName"))
        area_arabic_name = from_str(obj.get("AreaArabicName"))
        area_english_name = from_str(obj.get("AreaEnglishName"))
        bg_arabic_name = from_str(obj.get("BGArabicName"))
        bg_english_name = from_str(obj.get("BGEnglishName"))
        branch_arabic_name = from_str(obj.get("BranchArabicName"))
        branch_english_name = from_str(obj.get("BranchEnglishName"))
        dept_arabic_name = from_str(obj.get("DeptArabicName"))
        dept_english_name = from_str(obj.get("DeptEnglishName"))
        email = from_str(obj.get("Email"))
        english_name = from_str(obj.get("EnglishName"))
        error_message = from_str(obj.get("ErrorMessage"))
        phone_no = from_str(obj.get("PhoneNo"))
        user_name = from_str(obj.get("UserName"))
        return EmployeeDTO(response_code, response_message, response_message_ar, app_id, app_version, device_info,
                           emp_no, hr_code, token,
                           username, arabic_name, area_arabic_name, area_english_name, bg_arabic_name, bg_english_name,
                           branch_arabic_name, branch_english_name, dept_arabic_name, dept_english_name, email,
                           english_name, error_message, phone_no, user_name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["AppId"] = from_str(self.app_id)
        result["AppVersion"] = from_str(self.app_version)
        result["DeviceInfo"] = from_str(self.device_info)
        result["EmpNo"] = from_str(self.emp_no)
        result["HRCode"] = from_str(self.hr_code)
        result["Token"] = from_str(self.token)
        result["Username"] = from_str(self.username)
        result["ArabicName"] = from_str(self.arabic_name)
        result["AreaArabicName"] = from_str(self.area_arabic_name)
        result["AreaEnglishName"] = from_str(self.area_english_name)
        result["BGArabicName"] = from_str(self.bg_arabic_name)
        result["BGEnglishName"] = from_str(self.bg_english_name)
        result["BranchArabicName"] = from_str(self.branch_arabic_name)
        result["BranchEnglishName"] = from_str(self.branch_english_name)
        result["DeptArabicName"] = from_str(self.dept_arabic_name)
        result["DeptEnglishName"] = from_str(self.dept_english_name)
        result["Email"] = from_str(self.email)
        result["EnglishName"] = from_str(self.english_name)
        result["ErrorMessage"] = from_str(self.error_message)
        result["PhoneNo"] = from_str(self.phone_no)
        result["UserName"] = from_str(self.user_name)
        return result


def employee_dto_from_dict(s: Any) -> EmployeeDTO:
    return EmployeeDTO.from_dict(s)


def employee_dto_to_dict(x: EmployeeDTO) -> Any:
    return to_class(EmployeeDTO, x)
