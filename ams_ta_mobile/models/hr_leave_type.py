# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.addons.ams_ta_mobile.api.dto_timeoff import *


class HolidaysType(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    # _name = "hr.leave.type"
    _inherit = ["hr.leave.type"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    time_off_category = fields.Integer(string='Timeoff Category', readonly=True)

    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.model
    def create(self, vals_list):
        # Ensure vals_list is a list and process each item
        if not isinstance(vals_list, list):
            vals_list = [vals_list]

        for vals in vals_list:
            if vals['request_unit'] == 'day':
                vals['time_off_category'] = 1
            else:
                vals['time_off_category'] = 2
        res = super(HolidaysType, self).create(vals_list)
        return res

    def write(self, vals_list):
        if 'request_unit' in vals_list:
            if vals_list['request_unit'] == 'day':
                vals_list['time_off_category'] = 1
            else:
                vals_list['time_off_category'] = 2
        res = super(HolidaysType, self).write(vals_list)
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def get_timeoff_types_by_category(self, category: str) -> TimeoffTypesDTO:
        if category == "1": # vacation
            domain = [('request_unit', '=', 'day')]
        else: # permission
            domain = [('request_unit', '!=', 'day')]
        time_off_types_records = self.env['hr.leave.type'].sudo().search(domain)
        if time_off_types_records:
            return TimeoffTypesDTO(
                time_off_type=[TimeOffType(id=record.id, name=record.name) for record in time_off_types_records],
                response_code=200,
                response_message="Success",
                response_message_ar="تمت العملية بنجاح"
            )
        else:
            return TimeoffTypesDTO(
                time_off_type=[],
                response_code=404,
                response_message="No time-off types found for this category",
                response_message_ar="لا يوجد أنواع أجازات لهذه الفئة"
            )
    # endregion
