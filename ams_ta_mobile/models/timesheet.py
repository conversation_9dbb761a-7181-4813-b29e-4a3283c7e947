from odoo import api, fields, models
from odoo.addons.ams_bs.api.utils import to_int
from odoo.addons.ams_ta_mobile.api.dto_mobile_att import *
from odoo.addons.ams_ta_mobile.api.dto_emp_att_log import *
from odoo.addons.ams_ta.helper.helper import *
import logging

from pytz import timezone, UTC

_logger = logging.getLogger(__name__)


class Timesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.timesheet"
    _inherit = ["ams_ta.timesheet"]

    # endregion

    # region ---------------------- TODO[IMP]: Mobile API Methods ---------------------------------
    def action_test_get_att(self):
        # create object AttendanceParameterDTO with static parameters

        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '-1',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)
        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '0',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)

        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '1',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)
        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '2',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)

    def action_test_attend_log(self):  # '%Y/%m/%d H:M:S'
        data = EmpAttendanceLogDTO('', '', '', '', '23231', 'abigail/13031', '', 'u0z3rt0k3n', '',
                                   '2023/06/07 01:00:00',
                                   23.0, 34.7, '01:00', 1)
        result = self.add_attendance_log(data)
        print("done")
        # data2 = EmpAttendanceLogDTO('', '', '', '', '23231', 'abigail/13031', '', 'u0z3rt0k3n', '',
        #                            '2023/06/07 01:00:00',
        #                            23.0, 34.7, '01:00', 1)
        # self.add_attendance_log(data2)

    def add_attendance_log(self, data: EmpAttendanceLogDTO) -> EmpAttendanceLogDTO:
        """
        create a new punch log for employee based on specific time or time now and assign map location

        Args:
            data (EmpAttendanceLogDTO): A data transfer object containing the employee's attendance information.

        Returns:
            EmpAttendanceLogDTO: A data transfer object containing the updated attendance information for the employee.
        """
        # TODO : Validate
        # "100": "Input data not found",  if not data.emp_no and not data.hr_code and not data.username
        # "113": "AppId not valid", if employee.mobile_register_id in [False, '', ' ']
        # "114": "Attendance permission is denied", if not employee.mobile_app_allow or not employee.mobile_attendance_allow
        # "111": This user not registered in time attendance system
        # TODO [IMP]: create ams_ta.punch_log time with Geo - location  info

        if not data.emp_no and not data.hr_code and not data.username:
            data.response_code = "100"
            data.response_message = "Input data not found"
            data.response_message_ar = "البيانات المدخلة غير صالحة"
            return data

        employee_domain = ('employee_number', '=', data.emp_no) if data.emp_no \
            else ('user_name', '=', data.username) if data.username \
            else ('enroll_number', '=', data.hr_code)

        employee = self.env['hr.employee'].search([employee_domain], limit=1)

        if not employee.mobile_register_id:
            data.response_code = "113"
            data.response_message = "AppId not valid"
            data.response_message_ar = "معرف التطبيق غير صالح"
            return data
        if employee.mobile_register_id and employee.mobile_register_id != data.app_id:
            data.response_code = "109"
            data.response_message = "You have already registered device,please contact administrator!!"
            data.response_message_ar = "لديك جهاز مسجل بالفعل ، يرجى الاتصال بالمسؤول!!"
            return data

        if not employee.mobile_app_allow or not employee.mobile_attendance_allow:
            data.response_code = "114"
            data.response_message = "Attendance permission is denied"
            data.response_message_ar = "غير مسموح للمستخدم بالحضور والانصراف"
            return data

        if employee:
            punch_log = self.env['ams_ta.punch_log'].create({
                'name': f"Punch Log for {employee.name}",
                'employee_id': employee.id,
                'date_time': datetime.now(),
                'employee_number': data.emp_no,
                'enroll_number': data.hr_code,
                'device_serial': data.device_info,
                'longitude': data.longitude,
                'latitude': data.latitude,
                'notes': f"Logged from mobile, type: {'User action' if data.type == 0 else 'Automated action'}",
                'from_mobile': True,
                'state': 'pending'
            })

            action = punch_log.geo_fence_action_taken
            if action == 'none':
                data.response_code = "1"
                data.response_message = "Success"
                data.response_message_ar = "تم التسجيل بنجاح"
                data.error_message = "None"
            elif action in ['flag', 'warn']:
                data.response_code = "1.1"
                data.response_message = "Warning: Outside the allowed geographic area"
                data.response_message_ar = "تحذير: خارج المنطقة الجغرافية المسموح بها"
            elif action == 'reject':
                data.response_code = "1.1.1"
                data.response_message = "Rejected: Outside the allowed geographic area"
                data.response_message_ar = "مرفوض: خارج المنطقة الجغرافية المسموح بها"
        else:
            data.response_code = "111"
            data.response_message = "This user not registered in time attendance system"
            data.response_message_ar = "المستخدم غير مسجل في نظام الحضور والانصراف"

        return data

    def get_employee_attendance(self, att_param: AttendanceParameterDTO) -> MobileAttendanceDTO:
        """
         Retrieve the attendance record of an employee based on the given attendance parameters.
         Args:
             att_param (AttendanceParameterDTO): An object containing the attendance parameters.
         Returns:
             MobileAttendanceDTO: An object containing the attendance record of the employee.
         """
        attendance, vacations, permissions = [], [], []
        summary = False
        employee_domain = self.env['hr.employee']._get_employee_domain(att_param)
        # ('employee_number', '=', att_param.emp_no) if att_param.emp_no \
        # else ('user_name', '=', att_param.username)

        if att_param.action in ['-1', '0']:
            domain = [('employee_id', 'in', self.env['hr.employee'].search([employee_domain]).ids)]
            if att_param.date_from:
                domain.append(('date', '>=', att_param.date_from))
            if att_param.date_to:
                domain.append(('date', '<=', att_param.date_to))

            timesheets = self.env['ams_ta.timesheet'].search(domain)

            summary = self._prepare_attendance(timesheets, attendance)

        else:
            self._prepare_time_off(employee_domain, att_param, permissions, vacations)
            summary = self._prepare_attendance([], attendance)
        mobile_attendance_dto = MobileAttendanceDTO(attendance=attendance, device_info=att_param.device_info,
                                                    permissions=permissions, response_code=att_param.response_code,
                                                    response_message=att_param.response_message, summary=summary,
                                                    vacations=vacations)
        mobile_attendance_dto.response_code = '1'
        mobile_attendance_dto.response_message = 'OK'
        mobile_attendance_dto.response_message_ar = 'تم العملية بنجاح'
        return mobile_attendance_dto

    def _prepare_attendance(self, timesheets, attendance: []):
        total_absent = 0  # : int
        total_delay_and_shortage_minutes = 0  # : int
        total_delay_minutes = 0  # : int
        total_overtime_minutes = 0  # : int
        total_permission_minutes = 0  # : int
        total_shortage_minutes = 0  # : int
        total_vacation = 0  # : int
        total_working_minutes = 0  # : int

        user_tz = self.env.context.get('tz') or self.env.user.tz or 'UTC'
        tz = timezone(user_tz)

        for ts in timesheets:
            if ts.is_absent:
                total_absent += 1
                status = 2
            total_working_minutes += ts.working_time_min or 0
            total_delay_minutes += ts.delay_time_min or 0
            total_shortage_minutes += ts.shortage_time_min or 0
            total_overtime_minutes += ts.overtime_min or 0
            total_permission_minutes += ts.permission_count * 60
            if ts.is_vacation:
                total_vacation += 1
                status = 3
            if ts.is_dayoff:
                status = 1
            else:
                status = 0

            attendance_record = Attendance(
                check_in_time_string=convert_to_str_time_2digit_format(ts.ts_time_units_ids.first_checkin_time),
                check_out_time_string=convert_to_str_time_2digit_format(ts.ts_time_units_ids.last_checkout_time),
                # date_string=ts.date.strftime('%Y-%m-%d') if isinstance(ts.date, datetime.date) else '',
                date_string=ts.date.strftime('%Y-%m-%d'),
                day_length=ts.required_time_min,
                delay_minutes=ts.delay_time_min,
                display_delay_minutes=minutes_to_str_time(ts.delay_time_min),
                department_name_ar=ts.department_id.name,
                department_name_en=ts.department_id.name,
                difference_minutes=0,
                employee_id=str(ts.employee_id.id),
                note_ar=ts.notes,
                note_en=ts.notes,
                overtime_minutes=ts.overtime_min,
                permission_minutes=ts.permission_count,
                shift_name_ar=ts.shift_id.name,
                shift_name_en=ts.shift_id.name,
                shortage_minutes=ts.shortage_time_min,
                working_minutes=to_int(ts.working_time_min),
                display_working_minutes=minutes_to_str_time(ts.working_time_min),
                status=status

            )
            attendance.append(attendance_record)

        summary = Summary(
            total_absent=total_absent,
            total_delay_and_shortage_minutes=total_delay_minutes + total_shortage_minutes,
            total_delay_minutes=total_delay_minutes,
            total_overtime_minutes=total_overtime_minutes,
            total_permission_minutes=total_permission_minutes,
            total_shortage_minutes=total_shortage_minutes,
            total_vacation=total_vacation,
            total_working_minutes=total_working_minutes,
            total_delay_and_shortage_minutes_string=str(total_delay_minutes + total_shortage_minutes),
            total_delay_minutes_string=str(total_delay_minutes),
            total_overtime_minutes_string=str(total_overtime_minutes),
            total_permission_minutes_string=str(total_permission_minutes),
            total_shortage_minutes_string=str(total_shortage_minutes),
            total_working_minutes_string=str(total_working_minutes)
        )
        return summary

    def _prepare_time_off(self, employee_domain, att_param, permissions, vacations):
        # override method in time off & return dto
        pass

    # endregion

    # region ---------------------- TODO[IMP]: Helper Methods -------------------------------------
    # endregion
