# -*- coding: utf-8 -*-
from odoo.addons.ams_ta.helper.helper import *
from odoo import api, fields, models, _
from odoo.addons.ams_ta_mobile.api.dto_employee import EmployeeDTO, AuthDTO
from odoo.addons.ams_ta_mobile.api.dto_timeoff import *


class HolidaysRequest(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    # _name = "hr.leave.type"
    _inherit = ["hr.leave"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    name = fields.Char(store=True)

    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model_create_multi
    def create(self, vals_list):
        """Ensure request_unit_hours is set correctly during batch creation."""
        for vals in vals_list:
            if "holiday_status_id" in vals:
                holiday_status = self.env["hr.leave.type"].browse(vals["holiday_status_id"])
                vals["request_unit_hours"] = not (holiday_status.time_off_category == 1)
        return super(HolidaysRequest, self).create(vals_list)

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------

    def get_time_off(self, auth: AuthDTO) -> TimeOffDTO:
        """
        Input:timeoff_category=1 its a day off and timeoff_category=2 its a permission
        Retrieve the time off (leave) records for the authenticated employee.
        """
        employee = self.env['hr.employee'].search([self._employee_domain(auth)], limit=1)
        # logging.info(f"Checking timeoff_category in get_time_off: {auth.timeoff_category}")

        if not employee:
            return TimeOffDTO([], 111, "This user is not registered in the time attendance system",
                              "المستخدم غير مسجل في نظام الحضور والانصراف")

        # Build the base domain filters
        leave_domain = [
            ('employee_id', '=', employee.id),
        ]

        if auth.date_from and auth.date_to:
            leave_domain.append(('date_from', '>=', auth.date_from))
            leave_domain.append(('date_to', '<=', auth.date_to))
        elif auth.date_from and not auth.date_to:
            leave_domain.append(('date_from', '>=', auth.date_from))
        elif not auth.date_from and auth.date_to:
            leave_domain.append(('date_to', '<=', auth.date_to))

        if auth.timeoff_category != 0:
            leave_domain.append(('holiday_status_id.time_off_category', '=', auth.timeoff_category))
        leaves = self.env['hr.leave'].search(leave_domain)
        # Convert leave records into a list of TimeOff objects
        time_off_list = [
            TimeOff(
                holiday_status_id=leave.holiday_status_id.id,
                holiday_status_name=leave.holiday_status_id.name,
                description=leave.name or "",
                date_from=leave.date_from,
                date_to=leave.date_to,
                hour_from=convert_to_str_time_2digit_format(leave.request_hour_from),  # Convert to str
                hour_to=convert_to_str_time_2digit_format(leave.request_hour_to),  # Convert to str
                duration_display=f"{leave.duration_display}",
                state=leave.state,
                state_display=dict(leave._fields['state'].selection).get(leave.state, leave.state)
            )
            for leave in leaves
        ]

        return TimeOffDTO(time_off_list, 1, "OK", "تمت العملية بنجاح")


    def add_timeoff_log(self, data: EmpTimeOffDTO) -> EmpTimeOffDTO:
        try:
            if not data.emp_no and not data.hr_code and not data.username:
                data.response_code = "100"
                data.response_message = "Input data not found"
                data.response_message_ar = "البيانات المدخلة غير صالحة"
                return data

            employee_domain = self._employee_domain(data)
            employee = self.env['hr.employee'].search([employee_domain], limit=1)

            if not employee:
                data.response_code = "111"
                data.response_message = "User not registered in system"
                data.response_message_ar = "المستخدم غير مسجل في نظام الحضور والانصراف"
                return data

            # Validate AppId (Mobile Register ID)
            if not employee.mobile_register_id:
                data.response_code = "113"
                data.response_message = "AppId not valid"
                data.response_message_ar = "رقم التسجيل غير صالح"
                return data

            # Validate Attendance Permission
            if not employee.mobile_app_allow or not employee.mobile_attendance_allow:
                data.response_code = "114"
                data.response_message = "Attendance permission denied"
                data.response_message_ar = "تم رفض الصلاحية للحضور والانصراف"
                return data

            # Ensure dates exist in DTO
            if not data.date_from or not data.date_to:
                data.response_code = "115"
                data.response_message = "Date fields are missing or invalid"
                data.response_message_ar = "الحقول التاريخية غير موجودة أو غير صالحة"
                return data

            if data.date_from > data.date_to or data.hour_from > data.hour_to:
                data.response_code = "116"
                data.response_message = "Date From cannot be after Date To"
                data.response_message_ar = "لا يمكن أن يكون تاريخ البدء بعد تاريخ الانتهاء"
                return data

            # Check for overlapping leaves
            overlapping_leave = self.env['hr.leave'].search([
                ('employee_id', '=', employee.id),
                ('request_date_from', '<=', data.date_to.date()),
                ('request_date_to', '>=', data.date_from.date()),
                ('state', 'in', ['confirm', 'validate'])  # Pending or approved
            ], limit=1)

            if overlapping_leave:
                data.response_code = "117"
                data.response_message = (f"You already booked time off which overlaps with this period:"
                                         f" {overlapping_leave.name}"
                                         f" - from {overlapping_leave.request_date_from} "
                                         f"to {overlapping_leave.request_date_to}")
                data.response_message_ar = "لقد حجزت إجازة مسبقًا تتداخل مع هذه الفترة "


                return data

            leave_vals = {
                'name': f"Mobile Time Off Request: {employee.name}",
                'employee_id': employee.id,
                'holiday_status_id': data.timeoff_type_id,  # Adjust as per your leave type ID
                'request_date_from': data.date_from,
                'request_date_to': data.date_to,
                'request_hour_from': (data.hour_from),
                'request_hour_to': (data.hour_to),
                'notes': f"Mobile Request - Category: {'Vacation' if data.timeoff_category == 1 else 'Permission'}",
            }

            try:
                self.env['hr.leave'].create(leave_vals)
                data.response_code = "1"
                data.response_message = "OK"
                data.response_message_ar = "تمت العملية بنجاح"
                return data

            except Exception as ex:
                data.response_code = "112"
                data.response_message = f"Error creating leave request: {str(ex)}"
                data.response_message_ar = f"خطأ في انشاء طلب الاجازة: {str(ex)}"
                return data

        except Exception as ex:
            data.response_code = "111"
            data.response_message = f"System Error: {str(ex)}"
            data.response_message_ar = f"خطأ في النظام: {str(ex)}"
            return data

    def _employee_domain(self, data):
        if data.username:
            return ('user_name', '=', data.username)
        elif data.emp_no:
            return ('employee_number', '=', data.emp_no)
        elif data.hr_code:
            return ('enroll_number', '=', data.hr_code)
        return []

    # endregion
