# -*- coding: utf-8 -*-
import threading
from odoo import api, fields, models, _
from odoo.addons.ams_ta_mobile.api.dto_employee import EmployeeDTO, AuthDTO


class Employee(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "hr.employee"
    _inherit = ["hr.employee"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    mobile_app_allow = fields.Boolean(tracking=True, help="Allow using mobile app to review the attendance")
    mobile_attendance_allow = fields.Boolean(string="Attendance Allow", tracking=True,
                                             help="Allow attendance with mobile app", default=True)
    mobile_app_version = fields.Char()
    mobile_register_id = fields.Char(help="Mobile application ID")
    mobile_token = fields.Char(help="When user (employee) success login save token")
    mobile_last_login_date = fields.Datetime()
    mobile_register_date = fields.Datetime(tracking=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def action_for_test_get_employee_api_method(self):
        pass

    def get_employee(self, auth: AuthDTO) -> EmployeeDTO:
        """
         Retrieve the employee record of  the given authentication parameters.

         Args:
             auth (AuthDTO): An object containing the authentication parameters.

         Returns:
             EmployeeDTO: An object containing the Employee record.
         """
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        emp_dto = EmployeeDTO(app_id=auth.app_id, app_version=auth.app_version, device_info=auth.device_info,
                              emp_no=auth.emp_no, hr_code=auth.hr_code, token=auth.token, username=auth.username)
        if employee:
            # TODO: complete assign employee data

            emp_dto.token = employee.mobile_token
            emp_dto.app_version = employee.mobile_app_version
            emp_dto.app_id = employee.mobile_register_id
            emp_dto.response_code = '1'
            emp_dto.response_message = 'OK'
            emp_dto.response_message_ar = 'تمت العملية بنجاح'
            emp_dto.english_name = employee.name
            emp_dto.arabic_name = employee.name
            emp_dto.user_name = employee.user_name
            emp_dto.dept_english_name = employee.department_id.name
            emp_dto.dept_arabic_name = employee.department_id.name
            emp_dto.hr_code = employee.enroll_number
            emp_dto.emp_no = employee.employee_number
            emp_dto.bg_english_name = employee.user_group_id.name
            emp_dto.bg_arabic_name = employee.user_group_id.name
            emp_dto.area_english_name = employee.private_city
            emp_dto.area_arabic_name = employee.private_city
            emp_dto.email = employee.work_email
            emp_dto.phone_no = employee.work_phone
        else:
            emp_dto.response_code = "111"
            emp_dto.response_message = "This user not registered in time attendance system"
            emp_dto.response_message_ar = "هذا المستخدم غير مسجل في نظام الحضور والانصراف"

        return emp_dto

    def register_app(self, auth: AuthDTO) -> str:
        """
         register mobile app of the employee based on given authentication parameters.
         Args:
             auth (AuthDTO): An object containing the authentication parameters.
         Returns:
             str: return '1' if success or return error message
        """
        """register unique app id for employee"""
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        response_message = "0"
        # TODO validate:
        # "Error:You don't have mobile app using permission"
        # "Error:Not allowed for multiple registration"
        # "Error:this app already register!! please contact administrator"

        return response_message

    def authenticate(self, auth: AuthDTO, validate_app_id=False, database='') -> str:
        """ call odoo user authentication from related user"""
        # self.env.cr.dbname
        action = "01"  # login or register
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        # TODO validate
        # "Error:Employee doesn't exist"
        # "Error:Employee doesn't have user"
        # "Error:You don't have registration for mobile app"
        # "Error:Not allowed for multiple registration"
        # self.env.user.authenticate(database, auth.username, auth.password, {}) # if auth.auth_type == 1 and response_message == '1':
        response_message = ""
        return response_message

    def mobile_authenticate(self, auth: AuthDTO, validate_app_id=False) -> EmployeeDTO:
        """ call odoo user authentication from related user"""
        # self.env.cr.dbname
        emp_dto = EmployeeDTO(app_id=auth.app_id, app_version=auth.app_version, device_info=auth.device_info,
                              emp_no=auth.emp_no, hr_code=auth.hr_code, token=auth.token, username=auth.username)
        emp_dto.response_code = "101"  # login fail
        emp_dto.response_message = ""  # login fail
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])

        # TODO validate:
        # " 101, Error:Employee doesn't exist or unhandled error"
        if not employee:
            emp_dto.response_code = "101"
            emp_dto.response_message = "Error:Employee doesn't exist or unhandled error"
            emp_dto.response_message_ar = "خطأ: الموظف غير موجود أو خطأ غير متوقع"
            return emp_dto
        # " 102, Error:Employee doesn't have user"
        if not employee.user_id:
            emp_dto.response_code = "102"
            emp_dto.response_message = "Error:Employee doesn't have user"
            emp_dto.response_message_ar = "خطأ: الموظف ليس لديه مستخدم"
            return emp_dto
        # " 103, This user does not have permission to access mobile app!!"
        if not employee.mobile_app_allow:
            emp_dto.response_code = "103"
            emp_dto.response_message = "This user does not have permission to access mobile app!!"
            emp_dto.response_message_ar = "هذا المستخدم لا يملك صلاحية الوصول إلى تطبيق الهاتف المحمول!!"
            return emp_dto
        # " 109,You have already registered device,please contact administrator!! "
        if validate_app_id and employee.mobile_register_id and employee.mobile_register_id != auth.app_id:
            emp_dto.response_code = "109"
            emp_dto.response_message = "You have already registered device,please contact administrator!! "
            emp_dto.response_message_ar = "لديك جهاز مسجل بالفعل ، يرجى الاتصال بالمسؤول!!"
            return emp_dto
        # " 111, This user not registered in time attendance system"
        if not employee.mobile_attendance_allow:
            emp_dto.response_code = "111"
            emp_dto.response_message = "This user not registered in time attendance system"
            emp_dto.response_message_ar = "هذا المستخدم غير مسجل في نظام الحضور والانصراف"
            return emp_dto

        if auth.auth_type == 1 and emp_dto.response_message == '':
            uid = self.env.user.authenticate(self.env.cr.dbname, {
                'login': auth.username,
                'password': auth.password,
                'type': 'password'
            }, {})

            if uid:
                if not employee.mobile_register_id and auth.app_id:
                    employee.mobile_register_id = auth.app_id  # register

                emp_dto.response_code = "1"
                emp_dto.response_message = "OK"
                emp_dto.response_message_ar = "تم تسجيل الدخول بنجاح"
                emp_dto.token = employee.mobile_token
                emp_dto.app_version = employee.mobile_app_version
                emp_dto.app_id = employee.mobile_register_id
                emp_dto.english_name = employee.name
                emp_dto.arabic_name = employee.name
                emp_dto.user_name = employee.user_name
                emp_dto.dept_english_name = employee.department_id.name
                emp_dto.dept_arabic_name = employee.department_id.name
                emp_dto.hr_code = employee.enroll_number
                emp_dto.emp_no = employee.employee_number
                emp_dto.bg_english_name = employee.user_group_id.name
                emp_dto.bg_arabic_name = employee.user_group_id.name
                emp_dto.area_english_name = employee.private_city
                emp_dto.area_arabic_name = employee.private_city
                emp_dto.email = employee.work_email
                emp_dto.phone_no = employee.work_phone

        return emp_dto

    def validate_ldap_user(self, username, password, domain) -> bool:
        ...

    def get_db_name(self):
        db = odoo.tools.config['db_name']
        # If the database name is not provided on the command-line,
        # use the one on the thread (which means if it is provided on
        # the command-line, this will break when installing another
        # database from XML-RPC).
        if not db and hasattr(threading.current_thread(), 'dbname'):
            return threading.current_thread().dbname
        return db

    def _get_employee_domain(self, data):
        employee_domain = []
        if data.username:
            return ('user_name', '=', data.username)
        elif data.emp_no:
            return ('employee_number', '=', data.emp_no)
        elif data.hr_code:
            return ('enroll_number', '=', data.hr_code)

        return employee_domain

    # endregion
