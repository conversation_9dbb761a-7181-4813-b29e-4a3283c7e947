#!/usr/bin/env python3
"""
Flexible Markdown to PDF converter with Mermaid diagram support
Usage: python3 convert_md_to_pdf_flexible.py <input_file.md> [output_file.pdf]
"""

import os
import re
import subprocess
import tempfile
import sys
from pathlib import Path

def extract_mermaid_diagrams(content):
    """
    Extract mermaid diagrams from markdown content and replace with image references
    """
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    diagrams = []
    
    def replace_mermaid(match):
        diagram_content = match.group(1)
        diagram_id = len(diagrams)
        diagrams.append(diagram_content)
        # Use relative path for better compatibility
        return f'![Diagram {diagram_id + 1}](./diagram_{diagram_id + 1}.png)\n\n'
    
    # Replace mermaid blocks with image references
    modified_content = re.sub(mermaid_pattern, replace_mermaid, content, flags=re.DOTALL)
    
    # Clean up problematic Unicode characters in code blocks
    unicode_replacements = {
        '├──': '|--',
        '└──': '`--',
        '│': '|',
        '├': '|',
        '└': '`'
    }
    
    for unicode_char, ascii_replacement in unicode_replacements.items():
        modified_content = modified_content.replace(unicode_char, ascii_replacement)
    
    return modified_content, diagrams

def create_mermaid_images(diagrams, output_dir):
    """
    Create PNG images from mermaid diagram content
    """
    created_images = []
    for i, diagram in enumerate(diagrams):
        # Create temporary mermaid file
        mermaid_file = os.path.join(output_dir, f'temp_diagram_{i + 1}.mmd')
        with open(mermaid_file, 'w', encoding='utf-8') as f:
            f.write(diagram)
        
        # Convert to PNG
        output_image = os.path.join(output_dir, f'diagram_{i + 1}.png')
        cmd = ['mmdc', '-i', mermaid_file, '-o', output_image, '-w', '1200', '-H', '800', '--backgroundColor', 'white']
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            if os.path.exists(output_image):
                print(f"Created diagram_{i + 1}.png successfully")
                created_images.append(f'diagram_{i + 1}.png')
            else:
                print(f"Warning: diagram_{i + 1}.png was not created")
        except subprocess.CalledProcessError as e:
            print(f"Error creating diagram {i + 1}: {e}")
            print(f"Error output: {e.stderr}")
            print(f"Skipping diagram {i + 1}")
        except FileNotFoundError:
            print(f"Error: mmdc (mermaid-cli) not found. Please install it with: npm install -g @mermaid-js/mermaid-cli")
            return []
        
        # Clean up temporary mermaid file
        if os.path.exists(mermaid_file):
            os.remove(mermaid_file)
    
    return created_images

def convert_to_pdf(markdown_file, output_pdf, temp_dir):
    """
    Convert markdown to PDF using pandoc via HTML
    """
    # First convert to HTML
    html_file = os.path.join(temp_dir, 'document.html')
    
    cmd_html = [
        'pandoc',
        markdown_file,
        '-o', html_file,
        '--css', os.path.join(temp_dir, 'style.css'),
        '--standalone',
        '--toc',
        '--toc-depth=3',
        '--number-sections',
        '--highlight-style=tango',
        '--from=markdown',
        '--to=html5'
    ]
    
    try:
        # Change to temp directory so relative image paths work
        result = subprocess.run(cmd_html, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"HTML created successfully: {html_file}")
        
        # Check if HTML file was created and contains image references
        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
                img_count = html_content.count('<img')
                print(f"HTML contains {img_count} image references")
        
        # Then convert HTML to PDF using wkhtmltopdf directly
        cmd_pdf = [
            'wkhtmltopdf',
            '--page-size', 'A4',
            '--margin-top', '20mm',
            '--margin-bottom', '20mm',
            '--margin-left', '20mm',
            '--margin-right', '20mm',
            '--enable-local-file-access',
            '--print-media-type',
            html_file,
            output_pdf
        ]
        
        result_pdf = subprocess.run(cmd_pdf, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"PDF created successfully: {output_pdf}")
        
        # Verify PDF was created
        if os.path.exists(output_pdf):
            pdf_size = os.path.getsize(output_pdf)
            print(f"PDF file size: {pdf_size} bytes")
        
    except subprocess.CalledProcessError as e:
        print(f"Error converting to PDF: {e}")
        print(f"Error output: {e.stderr}")
        
        # Fallback: try pandoc direct to PDF with different settings
        print("Trying fallback method with pandoc direct to PDF...")
        cmd_fallback = [
            'pandoc',
            markdown_file,
            '-o', output_pdf,
            '--standalone',
            '--from=markdown',
            '--to=pdf',
            '--variable=geometry:margin=2cm',
            '--highlight-style=tango',
            '--resource-path=' + temp_dir
        ]
        
        try:
            result_fallback = subprocess.run(cmd_fallback, check=True, capture_output=True, text=True, cwd=temp_dir)
            print(f"PDF created successfully with fallback method: {output_pdf}")
            if os.path.exists(output_pdf):
                pdf_size = os.path.getsize(output_pdf)
                print(f"PDF file size: {pdf_size} bytes")
        except subprocess.CalledProcessError as e2:
            print(f"Error with fallback method: {e2}")
            print(f"Error output: {e2.stderr}")
    except FileNotFoundError as e:
        print(f"Error: Required tool not found: {e}")
        print("Please ensure pandoc and wkhtmltopdf are installed")

def create_css_style(temp_dir):
    """
    Create CSS for better PDF formatting
    """
    css_content = """
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        margin: 40px;
        color: #333;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #2c3e50;
        margin-top: 30px;
        margin-bottom: 15px;
    }
    
    h1 {
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
    }
    
    h2 {
        border-bottom: 2px solid #95a5a6;
        padding-bottom: 8px;
    }
    
    table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
    }
    
    th, td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    
    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    code {
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }
    
    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        background-color: white;
    }
    
    ul, ol {
        margin: 15px 0;
        padding-left: 30px;
    }
    
    li {
        margin: 5px 0;
    }
    
    blockquote {
        border-left: 4px solid #3498db;
        margin: 20px 0;
        padding: 10px 20px;
        background-color: #f8f9fa;
    }
    
    .page-break {
        page-break-before: always;
    }
    """
    
    css_file = os.path.join(temp_dir, 'style.css')
    with open(css_file, 'w') as f:
        f.write(css_content)

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 convert_md_to_pdf_flexible.py <input_file.md> [output_file.pdf]")
        print("Example: python3 convert_md_to_pdf_flexible.py document.md")
        print("Example: python3 convert_md_to_pdf_flexible.py document.md output.pdf")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    # Generate output filename if not provided
    if len(sys.argv) >= 3:
        output_file = sys.argv[2]
    else:
        # Use input filename with .pdf extension
        input_path = Path(input_file)
        output_file = str(input_path.with_suffix('.pdf'))
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    
    print(f"Converting '{input_file}' to '{output_file}'")
    
    # Read the input markdown file
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading input file: {e}")
        sys.exit(1)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Working in temporary directory: {temp_dir}")
        
        # Extract mermaid diagrams and replace with image references
        modified_content, diagrams = extract_mermaid_diagrams(content)
        
        # Create mermaid images
        if diagrams:
            print(f"Found {len(diagrams)} mermaid diagrams")
            created_images = create_mermaid_images(diagrams, temp_dir)
            print(f"Successfully created {len(created_images)} diagram images")
            
            # List all files in temp directory for debugging
            print("Files in temp directory:")
            for file in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  {file} ({size} bytes)")
        else:
            created_images = []
            print("No mermaid diagrams found")
        
        # Create CSS file
        create_css_style(temp_dir)
        
        # Write modified markdown to temporary file
        temp_md_file = os.path.join(temp_dir, 'document.md')
        with open(temp_md_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Convert to PDF
        convert_to_pdf(temp_md_file, output_file, temp_dir)
    
    print(f"Conversion complete! Output: {output_file}")

if __name__ == '__main__':
    main()