# -*- coding: utf-8 -*-
from odoo import api
from itertools import groupby
from operator import attrgetter


class ReportUtilsMixin:
    """
    Utility mixin for attendance reports.

    This mixin provides shared logic to build a dynamic search domain
    based on user input in the report wizard and to group timesheet records
    by employee or department as required by different reports.
    """

    @api.model
    def _build_timesheet_domain(self, wizard):
        """
        Constructs a search domain for timesheet records based on wizard inputs.

        :param wizard: Recordset of 'ta.report_generator_wizard'
        :return: List representing the domain
        """
        domain = []

        employee_ids = wizard.employee_ids.ids
        department_ids = wizard.department_ids.ids
        coach_ids = wizard.coach_ids.ids
        manager_ids = wizard.manager_ids.ids

        if not employee_ids and not department_ids:
            domain = self.env['ams_base.follow_up_model']._domain_follow_up()

        if not employee_ids and department_ids:
            domain.append(('last_department_id', 'in', department_ids))

        if employee_ids:
            domain.append(('employee_id', 'in', employee_ids))

        if coach_ids:
            domain.append(('coach_id', 'in', coach_ids))

        if manager_ids:
            domain.append(('manager_id', 'in', manager_ids))

        if wizard.from_date:
            domain.append(('date', '>=', wizard.from_date))

        if wizard.to_date:
            domain.append(('date', '<=', wizard.to_date))

        return domain

    def _group_by_employee(self, timesheets):
        """
        Groups timesheets by employee.

        :param timesheets: Recordset of 'ams_ta.timesheet'
        :return: Dictionary with employee_id as key and grouped data as value
        """
        # TODO [IMP]: Add summation of timesheets for each employee to use in xml directly
        return {
            emp.id: {
                'employee': emp,
                'timesheets': list(ts_group)
            }
            for emp, ts_group in groupby(timesheets, key=attrgetter('employee_id'))
        }

    def _group_by_department_and_employee(self, timesheets):
        """
        Groups timesheets first by department, then by employee.

        :param timesheets: Recordset of 'ams_ta.timesheet'
        :return: Dictionary with department_id as key, each containing employees with their timesheets
        """
        # TODO [IMP]: Add summation of timesheets for each Department to use in xml directly

        grouped_data = {}
        dept_key = attrgetter('employee_id.department_id')
        emp_key = attrgetter('employee_id')

        for department, dept_group in groupby(timesheets, key=dept_key):
            employees = {}
            for employee, emp_group in groupby(dept_group, key=emp_key):
                employees[employee.id] = {
                    'employee': employee,
                    'timesheets': list(emp_group)
                }
            grouped_data[department.id] = {
                'department': department,
                'employees': employees
            }

        return grouped_data
