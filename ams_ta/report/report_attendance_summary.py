from odoo import api, models
from .report_utils import ReportUtilsMixin


class ReportAttendanceSummary(models.AbstractModel,ReportUtilsMixin):
    """
    Abstract report model for rendering the 'Attendance Summary Report'.

    This model is linked to the QWeb report defined in XML with
    report_name='ams_ta.report_attendance_summary'.

    It gathers and organizes timesheet data based on selected employees,
    departments, and date range to be displayed in the report.
    """
    _name = 'report.ams_ta.report_attendance_summary'
    _description = 'Attendance Summary Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        """
        Prepares the data required by the QWeb report engine.

        :param docids: IDs of wizard records (ta.report_generator_wizard)
        :param data: Optional data dictionary passed from the report action
        :return: Dictionary to be used in the QWeb report template
        """
        docs = self.env['ta.report_generator_wizard'].browse(docids)
        data = data or {}

        domain = self._build_timesheet_domain(docs)
        timesheets = self.env['ams_ta.timesheet'].search(domain, order="employee_id, date")
        grouped_data = self._group_by_department_and_employee(timesheets)

        return {
            'doc_ids': docids,
            'doc_model': 'ta.report_generator_wizard',
            'docs': docs,
            'grouped_data': grouped_data,
            'data': data,
        }