# # -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from datetime import datetime
from dateutil.relativedelta import relativedelta
from psycopg2 import IntegrityError
import pytz
from odoo import api, fields, models
from odoo.tests.common import tagged
from pprint import pprint
from odoo.tools import mute_logger
# from odoo.addons.ta.tests.common import TestWorkEntryBase
# from addons_ta.ta.tests.common import TestWorkEntryBase
from ..tests.common import *


@tagged('ta_timesheet')
class TestTimesheet(TATestBase):

    def setUp(self):
        super(TestTimesheet, self).setUp()

    def _test_unit_test(self):
        x = 10
        self.assertEqual(x, 10, "Just raise fail as testing result")

    def test_normal_timesheet(self):
        # self.env['ta.punch_log'].search([]).unlink()
        # self.env['ta.timesheet'].search([]).unlink()
        print("---TODO Run Timesheet Tests Cases (08:00 - 16:00)------------")
        print(f"Shift Start Date:{self.shift_8_16.start_date},{self.test_emp.name}")

        msg = "1- Test normal attendance"
        logs = self.to_datetime_array(["08:09", "12:00", "16:09","10:00"], day_index=0)  # sunday
        self._assert_ts([logs], msg, ex_wt=8)

        # msg = "2- Test working,delay,overtime , time off  attendance"
        # logs = self.to_datetime_array(["8:11"], day_index=1)  # monday
        # # assign vacation
        # # self.assign_emp_vacation(self.emp_8_16, logs[0])
        # self._assert_ts([logs], msg, ex_wt=0, ex_delay=0, ex_dayoff=True, ex_is_vac=True,
        #                 ex_shortage=0, ex_overtime=0)

        # msg = "2- Test working,delay,overtime , time off  attendance"
        # logs = self.to_datetime_array(["8:00","16:30"], day_index=1)  # monday
        # # assign vacation
        # # self.assign_emp_vacation(self.emp_8_16, logs[0])
        # self._assert_ts([logs], msg, ex_wt=0, ex_delay=0, ex_dayoff=True, ex_is_vac=True,
        #                 ex_shortage=0, ex_overtime=0)

        msg = "3- Test working,delay,shortage  attendance"
        logs = self.to_datetime_array(["08:11", "13:20", "15:40"], day_index=2)  # Tue
        self._assert_ts([logs], msg, ex_wt=date_diff(logs[0], logs[-1]), ex_delay=min_to_float("00:11"),
                        ex_shortage=min_to_float("00:20"))

        msg = "4- Test one punch log,delay,working time =0  (No checkout) "
        logs = self.to_datetime_array(["11:00"], day_index=3)  # Wed
        ts = self._assert_ts([logs], msg, ex_delay=3)

        msg = "5- Test one punch log,delay , half working time (No checkout)"
        self.time_unit_8_16.rule_id.write({'calc_half_no_checkout_time':True,'apply_half_work': True})
        logs = self.to_datetime_array(["14:00"], day_index=4)  # Thu
        ts = self._assert_ts([logs], msg, ex_wt=4, ex_delay=6)
        self.time_unit_8_16.rule_id.write({'calc_half_no_checkout_time': False, 'apply_half_work': False}) # reset


        msg = "6- Test day is day-off ,no calculation"
        logs = self.to_datetime_array(["09:00", "14:00"], day_index=5)  # Fri
        ts = self._assert_ts([logs], msg, ex_weekend=True, ex_dayoff=True)

        msg = "7- Test day is weekend,no calculation"
        logs = self.to_datetime_array(["09:00", "14:00"], day_index=6)  # SAT
        ts = self._assert_ts([logs], msg, ex_weekend=True, ex_dayoff=True)

        msg = "8- Test applying min , max calculation in shift unit"
        self.time_unit_8_16.apply_min_max = True
        self.time_unit_8_16.min_checkin_time = 7
        self.time_unit_8_16.max_checkout_time = 17
        logs = self.to_datetime_array(["06:00", "18:00"], day_index=7)  # SUN
        ts = self._assert_ts([logs], msg, ex_wt=10, ex_overtime=2)

        msg = "9- Test absent if working time less than 2 hours"
        self.time_unit_8_16.absent_time_criteria = 2
        logs = self.to_datetime_array(["08:00", "9:00"], day_index=8)  # Mon
        ts = self._assert_ts([logs], msg, ex_wt=1, ex_is_absent=True, ex_shortage=7)

        # self.print_ts(ts)

    def test_overnight_timesheet(self):
        print("=================================================================")
        print("---TODO Run Overnight Timesheet Tests Cases (23:00 - 07:00)-----")
        self.test_emp = self.emp_23_7  # switch to overnight employee
        print(f"Shift Start Date:{self.shift_23_7.start_date},{self.test_emp.name}")
        #
        msg = "1- Test overnight multiple logs per day ,working time"
        logs1 = self.to_datetime_array(["23:10", "23:50", "23:30"], day_index=0)  # sunday 1/1/2023 -in
        logs2 = self.to_datetime_array(["06:00", "07:10"], day_index=1,)  # monday 1/2/2023 -out
        ts = self._assert_ts([logs1, logs2], msg, ex_wt=8,ex_delay=min_to_float("00:10"))

        msg = "2- Test overnight working,delay,shortage"
        logs1 = self.to_datetime_array(["23:11", "23:30"], day_index=1)  # monday 1/2/2023 - in
        logs2 = self.to_datetime_array(["06:00", "06:49"], day_index=2)  # tue 1/3/2023 -out
        ts = self._assert_ts([logs1, logs2], msg, ex_wt=min_to_float("07:38"), ex_delay=min_to_float("00:11"),
                             ex_shortage=min_to_float("00:11"), ex_overtime=0)

        msg = "3- Test overnight working,delay, overtime"
        logs1 = self.to_datetime_array(["23:30", "23:11"], day_index=2)  # tue 1/3/2023 - in
        logs2 = self.to_datetime_array(["08:00", "06:00"], day_index=3)  # wed 1/4/2023 - out
        ts = self._assert_ts([logs1, logs2], msg, ex_wt=min_to_float("08:49"), ex_delay=min_to_float("00:11"), ex_overtime=min_to_float("00:49")) #  'max_checkout_time': 8
        #
        msg = "4- Test overnight check-in,out in second day"
        logs1 = self.to_datetime_array(["2:00", "5:00"], day_index=4)  # thu 1/5/2023 - in ,out
        ts = self._assert_ts([logs1], msg, ex_wt=3, ex_shortage=2, ex_delay=3)
        # print(f"    ⚠ {self.ts_to_dict(ts)}")
        # pprint(self.ts_to_dict(ts), indent=4)
        #
        msg = "5- Test day off"
        logs1 = self.to_datetime_array(["22:00", "23:00"], day_index=5)  # fri 1/6/2023 ,why checkout not checkin
        ts = self._assert_ts([logs1], msg, ex_weekend=True, ex_dayoff=True)
        # pprint(self.ts_to_dict(ts), sort_dicts=False)

        msg = "6- Test one punch log,day off"
        logs1 = self.to_datetime_array(["23:00"], day_index=6)  # sat 1/7/2023 ,why checkout not checkin
        ts = self._assert_ts([logs1], msg, ex_weekend=True, ex_dayoff=True)
        # pprint(self.ts_to_dict(ts), sort_dicts=False)

        msg = "7- Test overnight attendance out of range shift unit"
        logs1 = self.to_datetime_array(["10:00", "12:00"], day_index=7)  # sun  1/8/2023 - in ,out
        ts = self._assert_ts([logs1], msg, ex_wt=0)#, ex_delay=8)  # TODO discuss shortage , delay
        # working + delay + shortage >= required_time (greater if there is overtime )

        # print('To be discuss, how calc delay,shortage employee if attendance out of range ')
        # pprint(self.ts_to_dict(ts), sort_dicts=False)

        msg = "8- Test overnight some punches out of range shift unit"
        logs1 = self.to_datetime_array(["1:00"], day_index=9)  # mon  1/9/2023 - in ,out
        logs2 = self.to_datetime_array(["6:00"], day_index=9)  # tue  1/10/2023 - in
        ts = self._assert_ts([logs1, logs2], msg, ex_wt=5, ex_shortage=1, ex_delay=2)
        # pprint(self.ts_to_dict(ts), sort_dicts=False)
        # print(f"☹ ✘ {msg} - fail ⓧ")

        # msg = "4- Test one punch log,delay,working time =0  (No checkout) "
        # logs = self.to_datetime_array(["11:00"], day_index=3)  # Wed
        # ts = self._assert_ts(logs, msg, ex_delay=3)
        #
        # msg = "5- Test one punch log,delay , half working time (No checkout)"
        # self.time_unit_8_16.rule_id.calc_half_no_checkout_time = True  # update setting
        # logs = self.to_datetime_array(["14:00"], day_index=4)  # Thu
        # ts = self._assert_ts(logs, msg, ex_wt=4, ex_delay=6)
        #
        # msg = "6- Test day is day-off ,no calculation"
        # logs = self.to_datetime_array(["09:00", "14:00"], day_index=5)  # Fri
        # ts = self._assert_ts(logs, msg, ex_weekend=True, ex_dayoff=True)
        #
        # msg = "7- Test day is weekend,no calculation"
        # logs = self.to_datetime_array(["09:00", "14:00"], day_index=6)  # SAT
        # ts = self._assert_ts(logs, msg, ex_weekend=True, ex_dayoff=True)
        #
        # msg = "8- Test applying min , max calculation in shift unit"
        # self.time_unit_8_16.apply_min_max = True
        # self.time_unit_8_16.min_checkin_time = 7
        # self.time_unit_8_16.max_checkout_time = 17
        # logs = self.to_datetime_array(["06:00", "18:00"], day_index=7)  # SUN
        # ts = self._assert_ts(logs, msg, ex_wt=10)
        #
        # msg = "9- Test absent if working time less than 2 hours"
        # self.time_unit_8_16.absent_time_criteria = 2
        # logs = self.to_datetime_array(["08:00", "9:00"], day_index=8)  # Mon
        # ts = self._assert_ts(logs, msg, ex_wt=1, ex_is_absent=True, ex_shortage=7)

        print(self.ts_to_dict(ts))

    # region helper-------
    def _assert_ts(self, logs, msg, **ex_kwargs):
        """validate expected values in timesheet with tolerance for small time differences"""
        ts = self.create_ts(self.test_emp.id, logs)
        
        def float_compare_with_tolerance(value1, value2, precision_digits=4):
            """Compare float values with tolerance (29 seconds = ~0.008 hours)"""
            tolerance = 0.016666666666666666  # 29 seconds converted to hours
            diff = abs(value1 - value2)
            return diff <= tolerance

        try:
            self.assertTrue(ts, "Timesheet must be created ")
            
            # Compare working time with tolerance
            self.assertTrue(
                float_compare_with_tolerance(round(ts.working_time, 2), ex_kwargs.get('ex_wt', 0)),
                f"Wrong working_time calculation. Expected: {ex_kwargs.get('ex_wt', 0)}, Got: {round(ts.working_time, 2)}"
            )

            if ts.is_dayoff:
                self.assertEqual(round(ts.delay_time, 2), 0, "Wrong delay_time calculation,day off")
                self.assertEqual(round(ts.shortage_time, 2), 0, "Wrong shortage_time calculation,day off")
                self.assertEqual(round(ts.overtime, 2), 0, "Wrong overtime calculation,day off")
            else:
                # Compare delay time with tolerance
                self.assertTrue(
                    float_compare_with_tolerance(round(ts.delay_time, 2), ex_kwargs.get('ex_delay', 0)),
                    f"Wrong delay_time calculation. Expected: {ex_kwargs.get('ex_delay', 0)}, Got: {round(ts.delay_time, 2)}"
                )
                
                # Compare shortage time with tolerance
                self.assertTrue(
                    float_compare_with_tolerance(round(ts.shortage_time, 2), ex_kwargs.get('ex_shortage', 0)),
                    f"Wrong shortage_time calculation. Expected: {ex_kwargs.get('ex_shortage', 0)}, Got: {round(ts.shortage_time, 2)}"
                )
                
                # Compare overtime with tolerance
                self.assertTrue(
                    float_compare_with_tolerance(round(ts.overtime, 2), ex_kwargs.get('ex_overtime', 0)),
                    f"Wrong overtime calculation. Expected: {ex_kwargs.get('ex_overtime', 0)}, Got: {round(ts.overtime, 2)}"
                )

            # Boolean comparisons remain exact
            self.assertEqual(ts.is_weekend, ex_kwargs.get('ex_weekend', False),
                            "Wrong value  'is_weekend' must be true")
            self.assertEqual(ts.is_dayoff, ex_kwargs.get('ex_dayoff', False),
                            "Wrong value 'is_dayoff' must be true")
            self.assertEqual(ts.is_absent, ex_kwargs.get('ex_is_absent', False),
                            "Wrong value 'is_absent' must be true")
            self.assertEqual(ts.is_vacation, ex_kwargs.get('ex_is_vac', False),
                            "Wrong value 'is_vacation' must be true")

            print(f"☻ ✓ {msg} - passed ☑ 💯 ")
        except Exception as ex:
            print(f"☹ ✘ {msg} - fail ⓧ")
            print(f"     ㋡ --> {ex}")
            print(f"🛈   Input Logs:")
            pprint(self.flatten_datetime_logs(logs, self.tz_hr_offset))
            print(f"⚠   Timesheet Info:")
            pprint(self.ts_to_dict(ts), sort_dicts=False)

        return ts

    def create_ts(self, employee_id, logs=[]):
        """execute punch log for checkin & checkout and return timesheet created"""
        last_punch_log = self.env['ams_ta.punch_log']
        for logs_arr in logs:
            for log_time in logs_arr:
                last_punch_log=self.env['ams_ta.punch_log'].create({'employee_id': employee_id, 'date_time': log_time})
                # punch_log.action_execute_punch_log()

        # ts_date = logs[0][0].date() if len(logs) > 0 else False
        return last_punch_log.timesheet_id

    def ts_to_dict(self, ts):
        """convert timesheet to dictionary"""
        if ts:
            ts = self.env['ams_ta.timesheet'].browse(ts.id)
            # fields.Datetime.tots.first_checkin_datetime.t
            checkin = fields.Datetime.context_timestamp(ts, ts.first_checkin_datetime) \
                if ts.first_checkin_datetime else '__:__'
            checkout = fields.Datetime.context_timestamp(ts, ts.last_checkout_datetime) \
                if ts.last_checkout_datetime else '__:__'

            ts_dict = {
                'date': str(ts.date),
                'check-in': str(checkin),
                'check-out': str(checkout),
                'working': ts.working_time,
                'delay': ts.delay_time,
                'shortage': ts.shortage_time,
                'overtime': ts.overtime,
                'is_absent': ts.is_absent,
                'is_dayoff': ts.is_dayoff,
                'is_weekend': ts.is_weekend,
                'time_untit_name': ts.ts_time_units_ids[0].name if len(
                    ts.ts_time_units_ids) > 0 else 'None',
                'time_unit_start': ts.ts_time_units_ids[0].start_time if len(
                    ts.ts_time_units_ids) > 0 else '__:__',
                'time_unit_end': ts.ts_time_units_ids[0].end_time if len(
                    ts.ts_time_units_ids) > 0 else '__:__',

                'time_unit_min': ts.ts_time_units_ids[0].min_checkin_time if len(
                    ts.ts_time_units_ids) > 0 else '__:__',
                'time_unit_max': ts.ts_time_units_ids[0].max_checkout_time if len(
                    ts.ts_time_units_ids) > 0 else '__:__',
                'time_unit_working_time': ts.ts_time_units_ids[0].working_time if len(
                    ts.ts_time_units_ids) > 0 else '__:__',
                'notes': ts.notes

            }
            # print(ts_dict)
            return ts_dict
            # print(
            #     f"in:{str(checkin)},"
            #     f"out:{str(checkout)},"
            #     f"wt:{ts.working_time},"
            #     f"dt:{ts.delay_time},"
            #     f"st:{ts.shortage_time},"
            #     f"ot:{ts.overtime},")
    # endregion

    # def setUp(self):
    #     super(TestWorkEntry, self).setUp()
    #     self.tz = pytz.timezone(self.richard_emp.tz)
    #     self.start = datetime(2015, 11, 1, 1, 0, 0)
    #     self.end = datetime(2015, 11, 30, 23, 59, 59)
    #     self.resource_calendar_id = self.env['resource.calendar'].create({'name': 'My Calendar'})
    #     contract = self.env['hr.contract'].create({
    #         'date_start': self.start.date() - relativedelta(days=5),
    #         'name': 'dodo',
    #         'resource_calendar_id': self.resource_calendar_id.id,
    #         'wage': 1000,
    #         'employee_id': self.richard_emp.id,
    #         'state': 'open',
    #         'date_generated_from': self.end.date() + relativedelta(days=5),
    #     })
    #     self.richard_emp.resource_calendar_id = self.resource_calendar_id
    #     self.richard_emp.contract_id = contract
    #
    # def test_no_duplicate(self):
    #     self.richard_emp.generate_work_entries(self.start, self.end)
    #     pou1 = self.env['hr.work.entry'].search_count([])
    #     self.richard_emp.generate_work_entries(self.start, self.end)
    #     pou2 = self.env['hr.work.entry'].search_count([])
    #     self.assertEqual(pou1, pou2, "Work entries should not be duplicated")
