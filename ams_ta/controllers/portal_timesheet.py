from odoo import http
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

class CustomPortal(CustomerPortal):
    _items_per_page = 5
    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)
        if 'portal_timesheet' in counters:
            timesheet_count = request.env['ams_ta.timesheet'].sudo().search_count([
                ('employee_id.user_id', '=', request.env.uid)  # Ensure correct mapping to user
            ])
            values['portal_timesheet'] = timesheet_count if timesheet_count > 0 else 1  # Prevent d-none
        return values

    @http.route(['/timesheet', '/timesheet/page/<int:page>'], type='http', auth="user", website=True)
    def portal_timesheet(self, search=None, search_in='All', filterby='All', groupby='none', page=1):
        """To search the timesheets data in the portal based on the current user"""
        searchbar_inputs = {
            'All': {'label': 'All', 'input': 'All', 'domain': []},
            'Name': {'label': 'Name', 'input': 'Name', 'domain': [('name', 'like', search)]},
            'Date': {'label': 'Date', 'input': 'Date', 'domain': [('date', 'like', search)]},
        }

        # Define the search bar filters
        searchbar_filters = {
            'All': {'label': 'All', 'domain': []},
            'Today': {
                'label': 'Today',
                'domain': [
                    ('date', '<=', datetime.combine(datetime.today(), datetime.max.time())),
                    ('date', '>=', datetime.combine(datetime.today(), datetime.min.time()))
                ]
            },
            'Yesterday': {
                'label': 'Yesterday',
                'domain': [
                    ('date', '<=', datetime.combine(datetime.today() - relativedelta(days=1), datetime.max.time())),
                    ('date', '>=', datetime.combine(datetime.today() - relativedelta(days=1), datetime.min.time()))
                ]
            },
            'Delayed': {'label': 'Delayed', 'domain': [('is_delayed', '=', True)]},
            'Shortage': {'label': 'Shortage', 'domain': [('is_shortage', '=', True)]},
            'Is Day Off': {'label': 'Is Day Off', 'domain': [('is_dayoff', '=', True)]},
            'Is Vacation': {'label': 'Is Vacation', 'domain': [('is_vacation', '=', True)]},
            'Is Public Vacation': {'label': 'Is Public Vacation', 'domain': [('is_public_vacation', '=', True)]},
            'Is Absent': {'label': 'Is Absent', 'domain': [('is_absent', '=', True)]},
            'Is Attend Day': {'label': 'Is Attend Day', 'domain': [('is_attend_day', '=', True)]},
            'Is Working Day': {'label': 'Is Working Day', 'domain': [('is_working_day', '=', True)]},
            'Manual Edit': {'label': 'Manual Edit', 'domain': [('is_manual_edit', '=', True)]},
        }

        # Define the group by options
        groupby_options = {
            'none': {'label': 'None', 'sequence': 0},
            'date': {'label': 'Date', 'sequence': 10},
            'shift_id': {'label': 'Shift', 'sequence': 20},
            'time_off_hours': {'label': 'Time-off', 'sequence': 30},
        }

        search_domain = searchbar_inputs[search_in]['domain']
        filter_domain = searchbar_filters.get(filterby, searchbar_filters['All'])['domain']
        combined_domain = search_domain + filter_domain + [('employee_id', '=', request.env.user.employee_id.id)]

        # Debug print
        print(f"Combined Domain: {combined_domain}")

        # Apply group by
        grouped = groupby != 'none'

        if grouped:
            all_groups = request.env['ams_ta.timesheet'].sudo().read_group(
                domain=combined_domain,
                fields=[groupby],
                groupby=[groupby]
            )

            print(f"All Groups: {all_groups}")

            total_groups = len(all_groups)
            pager = portal_pager(
                url='/timesheet',
                url_args={'search': search, 'search_in': search_in, 'filterby': filterby, 'groupby': groupby},
                total=total_groups,
                page=page,
                step=self._items_per_page,
            )

            paged_groups = all_groups[pager['offset']:pager['offset'] + self._items_per_page]

            search_timesheets = []
            for group in paged_groups:
                group_data = {
                    groupby: group[groupby],
                    'records': request.env['ams_ta.timesheet'].sudo().search(group['__domain'])
                }
                search_timesheets.append(group_data)
        else:
            timesheets_count = request.env['ams_ta.timesheet'].sudo().search_count(combined_domain)

            pager = portal_pager(
                url='/timesheet',
                url_args={'search': search, 'search_in': search_in, 'filterby': filterby, 'groupby': groupby},
                total=timesheets_count,
                page=page,
                step=self._items_per_page,
            )
            search_timesheets = request.env['ams_ta.timesheet'].sudo().search(
                combined_domain,
                offset=pager['offset'],
                limit=self._items_per_page
            )

        return request.render('ams_ta.portal_my_home_timesheet_views', {
            'timesheets': search_timesheets,
            'page_name': 'timesheet',
            'pager': pager,
            'search': search,
            'search_in': search_in,
            'searchbar_inputs': searchbar_inputs,
            'filterby': filterby,
            'searchbar_filters': searchbar_filters,
            'grouped': grouped,
            'groupby': groupby,
            'searchbar_groupby': groupby_options,
            'default_url': '/timesheet',
        })

    @http.route(['/timesheet/details', '/timesheet/details/<int:timesheet_id>'], type='http', auth="user", website=True)
    def portal_timesheet_form(self, timesheet_id = None, **kwargs):
        """Render the form view for a specific timesheet."""
        timesheet = request.env['ams_ta.timesheet'].sudo().browse(timesheet_id)

        timesheets = request.env['ams_ta.timesheet'].sudo().search([

        ])

        timesheets_ids = timesheets.ids

        current_index = timesheets_ids.index(timesheet_id)

        if current_index != 0 and timesheets_ids[current_index - 1]:
            prev_record = '/timesheet/details/{}'.format(timesheets_ids[current_index - 1])
        else:
            prev_record = None

        if current_index < len(timesheets_ids) - 1 and timesheets_ids[current_index + 1]:
            next_record = '/timesheet/details/{}'.format(timesheets_ids[current_index + 1])
        else:
            next_record = None



        return request.render('ams_ta.portal_timesheet_form_view', {
            'timesheet': timesheet,
            'page_name': 'timesheet_form',
            # 'default_url': '/timesheet',
            'prev_record': prev_record,
            'next_record': next_record
        })