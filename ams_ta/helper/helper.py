from datetime import date, datetime, time, timedelta
from dateutil.relativedelta import relativedelta
import pytz
import re

WEEK_DAYS = [('1', 'Saturday'), ('2', 'Sunday'), ('3', 'Monday'), ('4', 'Tuesday'),
             ('5', 'Wednesday'), ('6', 'Thursday'), ('7', 'Friday')]
WEEK_DAYS_LIST = ['Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']


def convert_to_time_object(float_time):
    """
    Convert float time to a time object (e.g., 1.5 -> 01:30:00).

    Args:
        float_time (float): Time in float format (e.g., 1.5 for 1 hour 30 minutes).

    Returns:
        time: Time object representing hours and minutes.
    """
    hours = int(float_time)
    minutes = int(round((float_time - hours) * 60))
    return time(hour=hours, minute=minutes)


def convert_to_float_time(time):
    """
    Convert a time object to float time (e.g., 01:30:00 -> 1.5).

    Args:
        time (time): Time object.

    Returns:
        float: Float representation of time, rounded to 2 decimal places.
    """
    return round(time.hour + time.minute / 60, 2)


def convert_to_str_time_format(float_time):
    """
    Convert float time to a string in H:MM format (e.g., 1.5 -> '1:30').

    Args:
        float_time (float): Time in float format.

    Returns:
        str: Time string in H:MM format.
    """
    hours = int(float_time)
    minutes = int(round((float_time - hours) * 60))
    return f"{hours}:{minutes}"


def convert_to_str_time_2digit_format(float_time: float) -> str:
    """
    Convert float time to a string in HH:MM format (e.g., 1.5 -> '01:30').

    Args:
        float_time (float): Time in float format.

    Returns:
        str: Time string in HH:MM format.
    """
    hours = int(float_time)
    minutes = int(round((float_time - hours) * 60))
    
    # Handle case where minutes round to 60
    if minutes == 60:
        hours += 1
        minutes = 0
        
    return f"{hours:02d}:{minutes:02d}"


def minutes_to_str_time(minutes: float) -> str:
    """
    Convert total minutes to a string in HH:MM format (e.g., 90 -> '01:30').

    Args:
        minutes (float): Total minutes.

    Returns:
        str: Time string in HH:MM format.
    """
    hours = int(minutes // 60)
    mins = int(minutes % 60)
    return f"{hours:02d}:{mins:02d}"


def get_minutes(float_time):
    """
    Extract minutes from float time (e.g., 1.5 -> 30).

    Args:
        float_time (float): Time in float format.

    Returns:
        int: Minutes component.
    """
    return int(round((float_time - int(float_time)) * 60))


def get_total_minutes(float_time):
    """
    Convert float time to total minutes (e.g., 1.5 -> 90).

    Args:
        float_time (float): Time in float format.

    Returns:
        int: Total minutes.
    """
    hours = int(float_time)
    minutes = get_minutes(float_time)
    return hours * 60 + minutes


def get_float_min(min):
    """
    Convert minutes to float time (e.g., 15 -> 0.25).

    Args:
        min (int): Minutes.

    Returns:
        float: Float time rounded to 2 decimal places.
    """
    return round(min / 60, 2)


def get_diff(time, minutes):
    """
    Subtract minutes from float time, handling negative results (e.g., 0.5 - 60 min -> 23.5).

    Args:
        time (float): Initial time in float format.
        minutes (int): Minutes to subtract.

    Returns:
        float: Resulting float time.
    """
    total_minutes = get_total_minutes(time) - minutes
    hours = total_minutes // 60 % 24
    mins = total_minutes % 60
    return round(hours + mins / 60, 2)


def get_sum(time, minutes):
    """
    Add minutes to float time, handling 24-hour overflow (e.g., 23.5 + 60 min -> 0.5).

    Args:
        time (float): Initial time in float format.
        minutes (int): Minutes to add.

    Returns:
        float: Resulting float time.
    """
    total_minutes = get_total_minutes(time) + minutes
    hours = total_minutes // 60 % 24
    mins = total_minutes % 60
    return round(hours + mins / 60, 2)


def compine_date_time(date, float_time):
    """
    Combine a date with float time to create a datetime object (e.g., date, 14.5 -> 14:30).

    Args:
        date (date): Date object.
        float_time (float): Time in float format.

    Returns:
        datetime: Combined datetime object.
    """
    time_obj = convert_to_time_object(float_time)
    return datetime.combine(date, time_obj)


def convert_to_utc(log_datetime, timezone):
    """
    Convert a local datetime to UTC.

    Args:
        log_datetime (datetime): Local datetime object.
        timezone (str): Timezone name (e.g., 'America/New_York').

    Returns:
        datetime: UTC datetime object.
    """
    try:
        tz = pytz.timezone(timezone)
        return tz.localize(log_datetime).astimezone(pytz.UTC).replace(tzinfo=None)
    except pytz.exceptions.UnknownTimeZoneError:
        return log_datetime


def float_str_to_hhmm(value) -> str:
    """
    Convert a string representing float time to HH:MM format (e.g., '1.5' -> '01:30').

    Args:
        value (str): String representing float time.

    Returns:
        str: Time in HH:MM format, or '00:00' on error.
    """
    try:
        return convert_to_str_time_2digit_format(float(value))
    except (ValueError, TypeError):
        return "00:00"


def convert_to_dotnet_date(odoo_date_or_datetime):
    """
    Convert a date or datetime to .NET date format (e.g., '/Date(1234567890000+0000)/').

    Args:
        odoo_date_or_datetime (date | datetime): Input date or datetime.

    Returns:
        str: .NET formatted date string.

    Raises:
        ValueError: If input is not a date or datetime object.
    """
    if isinstance(odoo_date_or_datetime, datetime):
        utc_dt = odoo_date_or_datetime.astimezone(pytz.UTC)
    elif isinstance(odoo_date_or_datetime, date):
        utc_dt = datetime.combine(odoo_date_or_datetime, time()).astimezone(pytz.UTC)
    else:
        raise ValueError("Input must be a date or datetime object")

    timestamp_ms = int(utc_dt.timestamp() * 1000)
    tz_offset = utc_dt.utcoffset() or timedelta(0)
    sign = '+' if tz_offset.total_seconds() >= 0 else '-'
    hours_offset = abs(tz_offset.seconds // 3600)
    minutes_offset = (abs(tz_offset.seconds) % 3600) // 60
    timezone_str = f"{sign}{hours_offset:02}{minutes_offset:02}"
    return f"/Date({timestamp_ms}{timezone_str})/"


def convert_from_dotnet_date(dotnet_date_str: str) -> datetime:
    """
    Convert a .NET formatted date string (e.g., /Date(1234567890000+0000)/) to a Python datetime object.

    Args:
        dotnet_date_str (str): .NET formatted date string.

    Returns:
        datetime: Converted Python datetime object.

    Raises:
        ValueError: If the input is not in the expected .NET date format.
    """
    # Regex to match the /Date(timestamp+offset)/ format
    match = re.match(r'/Date\((\d+)([+-]\d{4})\)/', dotnet_date_str)
    if not match:
        raise ValueError(f"Invalid .NET date format: {dotnet_date_str}")

    timestamp_ms = int(match.group(1))  # Extract timestamp in milliseconds
    tz_offset_str = match.group(2)  # Extract timezone offset

    # Convert timestamp to seconds
    timestamp = timestamp_ms / 1000

    # Create datetime object in UTC (since .NET DateTime is UTC)
    dt = datetime.utcfromtimestamp(timestamp)

    # Apply the timezone offset
    sign = 1 if tz_offset_str[0] == '+' else -1
    hours_offset = int(tz_offset_str[1:3])
    minutes_offset = int(tz_offset_str[3:])

    # Adjust the datetime with the timezone offset
    dt = dt.replace(hour=dt.hour + sign * hours_offset, minute=dt.minute + sign * minutes_offset)

    return dt


def get_date_range(period_filter, reference_date=None):
    """
    Calculate the start and end dates based on the period filter.

    Args:
        period_filter (str): The period filter (e.g., 'current_week', 'previous_month').
        reference_date (date, optional): The reference date. Defaults to today.

    Returns:
        tuple: (start_date, end_date) or (None, None) if invalid filter.
    """
    if not period_filter:
        return None, None

    ref_date = reference_date or date.today()

    def get_week_range(offset=0):
        date = ref_date - relativedelta(weeks=offset)
        start = date - relativedelta(days=(date.weekday() + 1) % 7)  # Sunday
        end = start + relativedelta(days=6)  # Saturday
        return start, end

    def get_period_range(period: str, offset=0):
        date = ref_date - relativedelta(**{f"{period}s": offset})
        start = date.replace(day=1) if period == "month" else date.replace(month=1, day=1)
        end = start + relativedelta(**{f"{period}s": 1}) - relativedelta(days=1)
        return start, end

    handlers = {
        "current_week": lambda: get_week_range(),
        "previous_week": lambda: get_week_range(1),
        "current_month": lambda: get_period_range("month"),
        "previous_month": lambda: get_period_range("month", 1),
        "current_year": lambda: get_period_range("year"),
        "previous_year": lambda: get_period_range("year", 1),
    }

    return handlers.get(period_filter, lambda: (None, None))()


def _parse_date_range(date_from_str: str, date_to_str: str):
    dt_from = datetime.strptime(date_from_str, "%d%m%Y").date()
    dt_to = datetime.strptime(date_to_str, "%d%m%Y").date()
    return dt_from, dt_to





