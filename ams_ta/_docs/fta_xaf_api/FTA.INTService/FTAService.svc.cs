﻿using System;
using System.ServiceModel.Activation;
using FTA.INTBLL;
using System.Globalization;

namespace FTA.INTService
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "Service1" in code, svc and config file together.
    // NOTE: In order to launch WCF Test Client for testing this service, please select Service1.svc or Service1.svc.cs at the Solution Explorer and start debugging.

    [AspNetCompatibilityRequirements(RequirementsMode = AspNetCompatibilityRequirementsMode.Allowed)]
    public class FTAService : IFTAService
    {
        //public static readonly LP.Logger.LPLogger Log = new LP.Logger.LPLogger(MethodBase.GetCurrentMethod().DeclaringType);
        FTAIntegration ftaInt = null;
       
        public FTAService()
        {

            ftaInt = new FTAIntegration();

        }

        public string IsAlive()
        {
            var res = ftaInt.RegisterModule();
            var res_config=ftaInt.InitLog();
            ftaInt.LogRequest();

            if (string.IsNullOrEmpty(res))
            {
                return "OK,"+ res_config;
            }
            else
            {
                return res;
            }
            // return ;
        }

        public string TestPost(AuthDTO auth)
        {
            ftaInt.LogRequest(auth);
            return "OK";
        }
        // Method to log the request details
     
        #region Employee Implementation Methods

        //Employees----------------------------------------------------

        public String AddEmployee(EmployeeDTO emp)
        {
            return ftaInt.AddEmployee(emp);
        }
        public String AddEmployees(EmployeeListDTO empList)
        {
            return ftaInt.AddEmployees(empList);
        }

        public EmployeeDTO GetEmployeeByEmpNo(String empNo)
        {
            return ftaInt.GetEmployeeInfo(empNo);
        }
        public EmployeeDTO GetEmployeeByUserName(String userName)
        {
            return ftaInt.GetEmployeeInfo(userName, FilterKeyType.Username);
        }
        public EmployeeListDTO GetEmployees()
        {
            return ftaInt.GetEmployeesInfo();//All Employees
        }
        public EmployeeListDTO GetEmployeesByDeptName(String deptName)
        {
            return ftaInt.GetEmployeesInfo(deptName, FilterKeyType.DeptName);
        }
        public EmployeeListDTO GetEmployeesByDeptCode(String deptCode)
        {
            return ftaInt.GetEmployeesInfo(deptCode, FilterKeyType.DeptCode);
        }
        public EmployeeListDTO GetEmployeeByBGName(String BGName)
        {
            return ftaInt.GetEmployeesInfo(BGName, FilterKeyType.BusinessGroupName);
        }
        public EmployeeListDTO GetEmployeesByBGCode(String BGCode)
        {
            return ftaInt.GetEmployeesInfo(BGCode, FilterKeyType.BusinessGroupCode);
        }
        #endregion

        #region Permissions Implementation Methods
        public RequestLogDTO AddPermissionRequest(RequestLogDTO request)
        {
            ftaInt.LogRequest(request);
            return ftaInt.AddPermissionRequest(request);
        }
        public RequestDTO GetEmployeePermissionsByEmpNo(String empNo, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeePermissions(empNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestDTO GetEmployeePermissionsByUserName(String userName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeePermissions(userName, dtFrom, dtTo, FilterKeyType.Username);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesPermissions(String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesPermissions("", dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesPermissionsByDeptName(String deptName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesPermissions(deptName, dtFrom, dtTo, FilterKeyType.DeptName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesPermissionsByDeptCode(String deptCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesPermissions(deptCode, dtFrom, dtTo, FilterKeyType.DeptCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesPermissionsByBGName(String BGName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesPermissions(BGName, dtFrom, dtTo, FilterKeyType.BusinessGroupName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesPermissionsByBGCode(String BGCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesPermissions(BGCode, dtFrom, dtTo, FilterKeyType.BusinessGroupCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        #endregion

        #region Vacations Implementation Methods
        public RequestLogDTO AddVacationRequest(RequestLogDTO request)
        {
            ftaInt.LogRequest(request);
            return ftaInt.AddVacationRequest(request);
        }
        public RequestDTO GetEmployeeVacationsByEmpNo(String empNo, String dateFrom, String dateTo)
        {
            try
            { 
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeVacations(empNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestDTO GetEmployeeVacationsByUserName(String userName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeVacations(userName, dtFrom, dtTo, FilterKeyType.Username);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesVacations(String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesVacations("", dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesVacationsByDeptName(String deptName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesVacations(deptName, dtFrom, dtTo, FilterKeyType.DeptName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesVacationsByDeptCode(String deptCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesVacations(deptCode, dtFrom, dtTo, FilterKeyType.DeptCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesVacationsByBGName(String BGName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesVacations(BGName, dtFrom, dtTo, FilterKeyType.BusinessGroupName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public RequestListDTO GetEmployeesVacationsByBGCode(String BGCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesVacations(BGCode, dtFrom, dtTo, FilterKeyType.BusinessGroupCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new RequestListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        #endregion

        #region Attendance Implementation Methods
        public String AddAttendance(EmpAttendanceLogDTO log)
        {
            ftaInt.LogRequest(log);
            return ftaInt.AddAttendanceLog(log);
        }
        public AttendanceDTO GetEmployeeAttendanceByEmpNo(String empNo, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(empNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceDTO GetEmployeeAttendanceByUserName(String userName, String dateFrom, String dateTo)
        {

            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(userName, dtFrom, dtTo, FilterKeyType.Username);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceListDTO GetEmployeesAttendance(String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendance("", dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceListDTO GetEmployeesAttendanceByDeptName(String deptName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendance(deptName, dtFrom, dtTo, FilterKeyType.DeptName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceListDTO GetEmployeesAttendanceByDeptCode(String deptCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendance(deptCode, dtFrom, dtTo, FilterKeyType.DeptCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceListDTO GetEmployeesAttendanceByBGName(String BGName, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendance(BGName, dtFrom, dtTo, FilterKeyType.BusinessGroupName);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceListDTO GetEmployeesAttendanceByBGCode(String BGCode, String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendance(BGCode, dtFrom, dtTo, FilterKeyType.BusinessGroupCode);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceDeductionListDTO GetEmployeesAttendanceDeduction(String empNo,String dateFrom, String dateTo)
        {
            try
            {
                ftaInt.LogRequest();
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeesAttendanceDeduction(empNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDeductionListDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        #endregion

        #region Attendance SharePoint Integration Methods

        public AttendanceDTO GetAttByEmpNo(string EmpNo, string dateFrom, string dateTo)
        {
            try
            {
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(EmpNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = ex.Message };
            }
            //return new AttendanceDTO();

        }

        public AttendanceDTO GetAttByUserName(string username, string dateFrom, string dateTo)
        {
            try
            {
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(username, dtFrom, dtTo, FilterKeyType.Username);
            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = ex.Message + "," + ex.StackTrace };
            }
        }

        #endregion

        #region Mobile  App Service
        public String MAuthenticateNative(AuthDTO auth)
        {
            return ftaInt.Authenticate(auth, true);
        }
        public EmployeeDTO MAuthenticate(AuthDTO data)
        {
            return ftaInt.MAuthenticate(data);
        }
        public string MRegisterApp(AuthDTO auth)
        {
            return ftaInt.RegisterApp(auth);
        }
        public EmployeeDTO MGetEmployee(AuthDTO data)
        {
            return ftaInt.MGetEmployee(data);
        }
        public EmployeeDTO MGetEmployeeByEmpNo(String empNo)
        {
            return ftaInt.GetEmployeeInfo(empNo);
        }
        public EmployeeDTO MGetEmployeeByUserName(String userName)
        {
            return ftaInt.GetEmployeeInfo(userName, FilterKeyType.Username);
        }
        public String MAddAttendanceNative(EmpAttendanceLogDTO log)
        {
            return ftaInt.AddAttendanceLog(log);
        }

        public MobileAttendanceDTO MGetAtt(string username, string from, string to)
        {
            return ftaInt.MGetAtt(username, from,to);
        }
        public MobileAttendanceDTO MGetEmployeeAttendance(PrmAttendance data)
        {
            return ftaInt.MGetEmployeeAttendance(data);
        }
        public EmpAttendanceLogDTO MAddAttendance(EmpAttendanceLogDTO log)
        {
            return ftaInt.MAddAttendance(log);
        }
        public AttendanceDTO MGetEmployeeAttendanceByEmpNo(String empNo, String dateFrom, String dateTo)
        {
            try
            {
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(empNo, dtFrom, dtTo);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        public AttendanceDTO MGetEmployeeAttendanceByUserName(String userName, String dateFrom, String dateTo)
        {

            try
            {
                var dtFrom = DateTime.ParseExact(dateFrom, "ddMMyyyy", CultureInfo.InvariantCulture);
                var dtTo = DateTime.ParseExact(dateTo, "ddMMyyyy", CultureInfo.InvariantCulture);
                return ftaInt.GetEmployeeAttendance(userName, dtFrom, dtTo, FilterKeyType.Username);

            }
            catch (Exception ex)
            {
                FTAIntegration.Log.Error(ex.Message, ex);
                return new AttendanceDTO { ErrorMessage = "Error:" + ex.Message };
            }
        }
        #endregion
    }


}
