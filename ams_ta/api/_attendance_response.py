# # response for attendance log
# {
#     "ResponseCode": "1",
#     "ResponseMessage": "OK",
#     "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
#     "Date": "/Date(1530738000000+0300)/",
#     "Status": 0,
#     "InTime": "08:30",
#     "OutTime": "17:15",
#     "RequiredTime": "08:00",
#     "WorkingTime": "08:45",
#     "Delay": "00:30",
#     "Shortage": "00:00",
#     "PermissionMinutes": 15,
#     "Comment": "Late due to traffic",
#     "ErrorMessage": ""
# }
#
# # response for attendance
#
# {
#     "ResponseCode": "1",
#     "ResponseMessage": "OK",
#     "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
#     "Emp": {
#         "EmpNo": "EMP001",
#         "EnglishName": "<PERSON>",
#         "ArabicName": "جون دو",
#         ...
#     },
#     "Logs": [
#         {
#             "Date": "/Date(1530738000000+0300)/",
#             "Status": 0,
#             "InTime": "08:30",
#             "OutTime": "17:15",
#             "RequiredTime": "08:00",
#             "WorkingTime": "08:45",
#             "Delay": "00:30",
#             "Shortage": "00:00",
#             "PermissionMinutes": 15,
#             "Comment": "Late due to traffic",
#             ...
#         }
#     ],
#     "ErrorMessage": ""
# }
#
# # response for attendance list
# # {
# #     "ResponseCode": "1",
# #     "ResponseMessage": "OK",
# #     "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
# #     "AttList": [
# #         {
# #             "Emp": {
# #                 "EmpNo": "EMP001",
# #                 "EnglishName": "John Doe",
# #                 "ArabicName": "جون دو",
# #                 ...
# #             },
# #             "Logs": [
# #                 {
# #                     "Date": "/Date(1530738000000+0300)/",
# #                     "Status": 0,
# #                     "InTime": "08:30",
# #                     "OutTime": "17:15",
# #                     "RequiredTime": "08:00",
# #                     "WorkingTime": "08:45",
# #                     "Delay": "00:30",
# #                     "Shortage": "00:00",
# #                     "PermissionMinutes": 15,
# #                     "Comment": "Late due to traffic"
# #                 }
# #             ]
# #         }
# #     ],
# #     "ErrorMessage": ""
# # }
#
# # response for attendance deduction
#
# {
#     "ResponseCode": "1",
#     "ResponseMessage": "OK",
#     "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
#     "EmpNo": "EMP001",
#     "DeductionMinutes": 120,
#     "DeductionHours": 2,
#     "ErrorMessage": ""
# }
#
#
# # response for attendance deduction list
# {
#     "ResponseCode": "1",
#     "ResponseMessage": "OK",
#     "ResponseMessageAR": "تم تسجيل الدخول بنجاح",
#     "DeductionList": [
#         {
#             "EmpNo": "EMP001",
#             "DeductionMinutes": 120,
#             "DeductionHours": 2
#         },
#         {
#             "EmpNo": "EMP002",
#             "DeductionMinutes": 60,
#             "DeductionHours": 1
#         }
#     ],
#     "ErrorMessage": ""
# }
#
#
