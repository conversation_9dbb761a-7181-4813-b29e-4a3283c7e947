from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast
from .utils import *

@dataclass
class EmployeeDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    emp_no: str
    user_name: str
    english_name: str
    arabic_name: str
    email: str
    phone_no: str
    dept_arabic_name: str
    dept_english_name: str
    bg_arabic_name: str
    bg_english_name: str
    area_arabic_name: str
    area_english_name: str
    branch_arabic_name: str
    branch_english_name: str
    error_message: str

    @staticmethod
    def from_dict(obj: Any) -> 'EmployeeDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        emp_no = from_str(obj.get("EmpNo"))
        user_name = from_str(obj.get("UserName"))
        english_name = from_str(obj.get("EnglishName"))
        arabic_name = from_str(obj.get("ArabicName"))
        email = from_str(obj.get("Email"))
        phone_no = from_str(obj.get("PhoneNo"))
        dept_arabic_name = from_str(obj.get("DeptArabicName"))
        dept_english_name = from_str(obj.get("DeptEnglishName"))
        bg_arabic_name = from_str(obj.get("BGArabicName"))
        bg_english_name = from_str(obj.get("BGEnglishName"))
        area_arabic_name = from_str(obj.get("AreaArabicName"))
        area_english_name = from_str(obj.get("AreaEnglishName"))
        branch_arabic_name = from_str(obj.get("BranchArabicName"))
        branch_english_name = from_str(obj.get("BranchEnglishName"))
        error_message = from_str(obj.get("ErrorMessage"))
        return EmployeeDTO(response_code, response_message, response_message_ar, emp_no, user_name, english_name, arabic_name, email, phone_no, dept_arabic_name, dept_english_name, bg_arabic_name, bg_english_name, area_arabic_name, area_english_name, branch_arabic_name, branch_english_name, error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["EmpNo"] = from_str(self.emp_no)
        result["UserName"] = from_str(self.user_name)
        result["EnglishName"] = from_str(self.english_name)
        result["ArabicName"] = from_str(self.arabic_name)
        result["Email"] = from_str(self.email)
        result["PhoneNo"] = from_str(self.phone_no)
        result["DeptArabicName"] = from_str(self.dept_arabic_name)
        result["DeptEnglishName"] = from_str(self.dept_english_name)
        result["BGArabicName"] = from_str(self.bg_arabic_name)
        result["BGEnglishName"] = from_str(self.bg_english_name)
        result["AreaArabicName"] = from_str(self.area_arabic_name)
        result["AreaEnglishName"] = from_str(self.area_english_name)
        result["BranchArabicName"] = from_str(self.branch_arabic_name)
        result["BranchEnglishName"] = from_str(self.branch_english_name)
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def employee_dto_from_dict(s: Any) -> EmployeeDTO:
    return EmployeeDTO.from_dict(s)


def employee_dto_to_dict(x: EmployeeDTO) -> Any:
    return to_class(EmployeeDTO, x)
