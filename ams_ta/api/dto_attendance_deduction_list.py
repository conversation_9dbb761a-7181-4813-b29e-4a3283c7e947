from dataclasses import field
from .dto_attendance_deduction import *


@dataclass
class AttendanceDeductionListDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    deduction_list: List[AttendanceDeductionDTO] = field(default_factory=list)
    error_message: str = ''

    def __init__(self,
                 response_code: str = '',
                 response_message: str = '',
                 response_message_ar: str = '',
                 deduction_list: List[AttendanceDeductionDTO] = None,
                 error_message: str = ''):
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.deduction_list = deduction_list if deduction_list is not None else []
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceDeductionListDTO':
        assert isinstance(obj, dict)
        response_code = (from_str(obj.get("ResponseCode")))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        deduction_list = from_list(AttendanceDeductionDTO.from_dict, obj.get("DeductionList"))
        error_message = from_str(obj.get("ErrorMessage"))
        return AttendanceDeductionListDTO(response_code, response_message, response_message_ar, deduction_list,
                                          error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["DeductionList"] = from_list(lambda x: to_class(AttendanceDeductionDTO, x), self.deduction_list)
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def attendance_deduction_list_dto_from_dict(s: Any) -> AttendanceDeductionListDTO:
    """Create AttendanceDeductionListDTO from dictionary"""
    return AttendanceDeductionListDTO.from_dict(s)


def attendance_deduction_list_dto_to_dict(x: AttendanceDeductionListDTO) -> Any:
    """Convert AttendanceDeductionListDTO to dictionary"""
    return to_class(AttendanceDeductionListDTO, x)
