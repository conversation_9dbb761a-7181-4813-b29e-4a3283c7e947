from odoo.addons.ams_ta.helper.helper import convert_to_dotnet_date, convert_from_dotnet_date
from .utils import *
from dataclasses import dataclass
from typing import Any
import logging

def format_float_to_time(value):
    """Convert float time value to HH:MM:SS format matching Odoo's display"""
    if not value or value == "--:--:--":
        return "00:00:00"
    
    try:
        # Convert string to float if needed
        if isinstance(value, str) and value != "--:--:--":
            value = float(value)
            
        # Calculate hours and minutes like Odoo does
        hours = int(value)
        minutes = int(round((value - hours) * 60))
        
        # Handle minute overflow
        if minutes >= 60:
            hours += 1
            minutes -= 60
            
        return f"{hours:02d}:{minutes:02d}:00"
    except (ValueError, TypeError):
        return "00:00:00"

class AttendanceLogDTO:
    response_code: str=''
    response_message: str=''
    response_message_ar: str=''
    date: datetime
    status: int=0
    in_time: str=''
    out_time: str=''
    required_time: str=''
    working_time: str=''
    delay: str=''
    shortage: str=''
    comment: str=''
    date_string: str=''
    error_message: str=''

    def __init__(self, response_code: str, response_message: str, response_message_ar: str, date: datetime, status: int, in_time: str, out_time: str, required_time: str, working_time: str, delay: str, shortage: str,comment: str, date_string: str,error_message: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.date = date
        self.status = status
        self.in_time = in_time
        self.out_time = out_time
        self.required_time = required_time
        self.working_time = working_time
        self.delay = delay
        self.shortage = shortage
        self.comment = comment
        self.date_string = date_string
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceLogDTO':
        assert isinstance(obj, dict)
        response_code = (from_str(obj.get("ResponseCode")))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        date =convert_from_dotnet_date(obj.get("Date"))
        status = from_int(obj.get("Status"))
        in_time = from_str(obj.get("InTime"))
        out_time = from_str(obj.get("OutTime"))
        required_time = from_str(obj.get("RequiredTime"))
        working_time = from_str(obj.get("WorkingTime"))
        delay = from_str(obj.get("Delay"))
        shortage = from_str(obj.get("Shortage"))
        comment = from_str(obj.get("Comment"))
        date_string = from_str(obj.get("Date"))
        error_message = from_str(obj.get("ErrorMessage"))
        return AttendanceLogDTO(response_code, response_message, response_message_ar, date, status, in_time, out_time, required_time, working_time, delay, shortage,  comment, date_string,error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["Date"] = convert_to_dotnet_date(self.date)
        result["Status"] = from_int(self.status)
        result["InTime"] = from_str(self.in_time or "--:--:--")
        result["OutTime"] = from_str(self.out_time or "--:--:--")
        result["RequiredTime"] = format_float_to_time(self.required_time)
        result["WorkingTime"] = format_float_to_time(self.working_time)
        result["Delay"] = format_float_to_time(self.delay)
        result["Shortage"] = format_float_to_time(self.shortage)
        result["Comment"] = from_str(self.comment or "")
        result["DateString"] = from_str(self.date_string or "")
        result["ErrorMessage"] = from_str(self.error_message or "")
        return result


def attendance_log_dto_from_dict(s: Any) -> AttendanceLogDTO:
    return AttendanceLogDTO.from_dict(s)


def attendance_log_dto_to_dict(x: AttendanceLogDTO) -> Any:
    return to_class(AttendanceLogDTO, x)
