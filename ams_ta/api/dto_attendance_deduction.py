
from .utils import *
from dataclasses import dataclass
from typing import Any, List
import logging

@dataclass
class AttendanceDeductionDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    emp_no: str
    deduction_minutes: int
    deduction_hours: int
    required_minutes: int
    error_message: str

    def __init__(self, response_code: str, response_message: str, response_message_ar: str, emp_no: str, deduction_minutes: int, deduction_hours: int, required_minutes: int,error_message: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.emp_no = emp_no
        self.deduction_minutes = deduction_minutes
        self.deduction_hours = deduction_hours
        self.required_minutes = required_minutes
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceDeductionDTO':
        assert isinstance(obj, dict)
        response_code = (from_str(obj.get("ResponseCode")))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        emp_no = from_str(obj.get("EmpNo"))
        deduction_minutes = from_int(obj.get("DeductionMinutes"))
        deduction_hours = from_int(obj.get("DeductionHours"))
        required_minutes = from_int(obj.get("RequiredMinutes"))
        error_message = from_str(obj.get("ErrorMessage"))
        return AttendanceDeductionDTO(response_code, response_message, response_message_ar, emp_no, deduction_minutes, deduction_hours, required_minutes, error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["EmpNo"] = from_str(self.emp_no)
        result["DeductionMinutes"] = from_int(self.deduction_minutes)
        result["DeductionHours"] = from_int(self.deduction_hours)
        result["RequiredMinutes"] = from_int(self.required_minutes)
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def attendance_deduction_dto_from_dict(s: Any) -> AttendanceDeductionDTO:
    return AttendanceDeductionDTO.from_dict(s)


def attendance_deduction_dto_to_dict(x: AttendanceDeductionDTO) -> Any:
    return to_class(AttendanceDeductionDTO, x)
