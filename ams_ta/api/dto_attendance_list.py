from .dto_attendance import *


@dataclass
class AttendanceListDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    att_list: List[AttendanceDTO] = None
    error_message: str = ''

    def __post_init__(self):
        if self.att_list is None:
            self.att_list = []

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceListDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        att_list = from_list(AttendanceDTO.from_dict, obj.get("AttList", []))
        error_message = from_str(obj.get("ErrorMessage", ""))
        return AttendanceListDTO(
            response_code=response_code,
            response_message=response_message,
            response_message_ar=response_message_ar,
            att_list=att_list,
            error_message=error_message
        )

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["AttList"] = from_list(lambda x: to_class(AttendanceDTO, x), self.att_list)
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def attendance_list_dto_from_dict(s: Any) -> AttendanceListDTO:
    return AttendanceListDTO.from_dict(s)


def attendance_list_dto_to_dict(x: AttendanceListDTO) -> Any:
    return to_class(AttendanceListDTO, x)
