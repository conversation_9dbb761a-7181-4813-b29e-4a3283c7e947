from odoo.addons.ams_ta.api.dto_employee import *


@dataclass
class EmployeeListDTO:
    response_code: str
    response_message: str
    response_message_ar: str
    employees: List[EmployeeDTO]
    error_message: str

    def __init__(self, response_code: str, response_message: str, response_message_ar: str,
                 employees: List[EmployeeDTO], error_message: str) -> None:
        self.response_code = response_code
        self.response_message = response_message
        self.response_message_ar = response_message_ar
        self.employees = employees
        self.error_message = error_message

    @staticmethod
    def from_dict(obj: Any) -> 'EmployeeListDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        employees = from_list(EmployeeDTO.from_dict, obj.get("Employees"))
        error_message = from_str(obj.get("ErrorMessage"))
        return EmployeeListDTO(response_code, response_message, response_message_ar, employees, error_message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(str(self.response_code))
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["Employees"] = from_list(lambda x: to_class(EmployeeDTO, x), self.employees)
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def employee_list_dto_from_dict(s: Any) -> EmployeeListDTO:
    return EmployeeListDTO.from_dict(s)


def employee_list_dto_to_dict(x: EmployeeListDTO) -> Any:
    return to_class(EmployeeListDTO, x)
