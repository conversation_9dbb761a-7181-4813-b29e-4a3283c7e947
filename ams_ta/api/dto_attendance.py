from .dto_attendance_log import AttendanceLogDTO
from .dto_employee import EmployeeDTO
from .utils import *
from dataclasses import dataclass
from typing import Any, List
import logging

@dataclass
class AttendanceDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    emp: Optional[EmployeeDTO] = None  # Now accepts None
    logs: List[AttendanceLogDTO] = None
    error_message: str = ''

    def __post_init__(self):
        if self.emp is None:
            self.emp = None
        if self.logs is None:
            self.logs = []

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        response_message_ar = from_str(obj.get("ResponseMessageAR"))
        emp = EmployeeDTO.from_dict(obj.get("Emp", {}))
        logs = from_list(AttendanceLogDTO.from_dict, obj.get("Logs", []))
        error_message = from_str(obj.get("ErrorMessage", ""))
        return AttendanceDTO(
            response_code=response_code,
            response_message=response_message,
            response_message_ar=response_message_ar,
            emp=emp,
            logs=logs,
            error_message=error_message
        )

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["ResponseMessageAR"] = from_str(self.response_message_ar)
        result["Emp"] = to_class(EmployeeDTO, self.emp) if self.emp is not None else None
        # The logs field might be missing or using wrong key name
        result["Logs"] = [log.to_dict() for log in self.logs] if self.logs else []
        result["ErrorMessage"] = from_str(self.error_message)
        return result


def attendance_dto_from_dict(s: Any) -> AttendanceDTO:
    return AttendanceDTO.from_dict(s)


def attendance_dto_to_dict(x: AttendanceDTO) -> Any:
    return to_class(AttendanceDTO, x)
