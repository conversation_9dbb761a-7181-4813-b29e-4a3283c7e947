# -*- coding: utf-8 -*-
from typing_extensions import override

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class EventLog(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = 'ams_bs.event_log'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------


    def _create_punch_log(self, enroll_number, device_serial, log_date):
        """
        Override the base _create_punch_log method to create punch logs
        following the AMS TA module's requirements.

        Args:
            enroll_number (str): Employee enrollment number from biometric device
            device_serial (str): Serial number of the biometric device
            log_date (datetime): Date/time of the punch event

        Returns:
            ams_ta.punch_log: Created punch log record
        """
        # Search for employee based on enroll_number
        employee = self.env['hr.employee'].search(
            [('enroll_number', '=', enroll_number)], limit=1)

        if not employee:
            raise ValidationError(f"No employee found with enroll number {enroll_number}")

        # Prepare punch log values according to ams_ta.punch_log model
        punch_log_vals = {
            'name': f"Punch Log for {employee.name}",
            'employee_id': employee.id,
            'device_serial': device_serial,
            'date_time': log_date,
            'state': 'pending',
            'from_mobile': False,
            'notes': f"Punch Log for {employee.name} from device {device_serial} at {log_date}",

        }
        # Create the punch log record
        # Note: The create method will automatically call action_execute_punch_log
        punch_log = self.env['ams_ta.punch_log'].create(punch_log_vals)

        return punch_log
    # endregion
