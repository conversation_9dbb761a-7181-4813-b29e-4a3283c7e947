# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class GeoFenceLocation(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.geo_fence_location"
    _description = "Geo-Fence Location"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------


    name = fields.Char(string="Location Name", required=True)
    latitude = fields.Float(string="Latitude", digits=(10, 7), required=True)
    longitude = fields.Float(string="Longitude", digits=(10, 7), required=True)
    radius = fields.Float(string="Radius (meters)", default=100.0, required=True)
    address = fields.Char(string="Address")
    is_active = fields.Boolean(string="Active", default=True)

    # Many locations can belong to one user group
    user_group_id = fields.Many2one('ams.user_group', string="User Group", required=True, ondelete='cascade')

    # For visualization on map
    color = fields.Char(string="Color", default="#FF5733")
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('latitude', 'longitude')
    def _check_coordinates(self):
        for record in self:
            if not (-90 <= record.latitude <= 90):
                raise ValidationError("Latitude must be between -90 and 90.")
            if not (-180 <= record.longitude <= 180):
                raise ValidationError("Longitude must be between -180 and 180.")
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion




