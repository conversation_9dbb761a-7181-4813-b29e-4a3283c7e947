# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class TimeUnitRule(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.time_unit_rule"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
    _description = 'Time Unit Rule'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Name", required=True, tracking=True)
    grace_in_time = fields.Float(string="Grace In Time", default=0.0, tracking=True,
                                 help="Time allowed to checkin after start of time_unit without calculate delay")
    grace_out_time = fields.Float(string="Grace Out Time", default=0.0, tracking=True,
                                  help="Time allowed to checkout before end of time_unit without calculate shortage")
    apply_half_work = fields.Boolean(string="Apply Half Work", tracking=True)
    half_work_checkin_criteria = fields.Float(string="If Checkin After", tracking=True,
                                              help="Calculate half of working time if checkin > ( Start time + this count of [hours:minutes] )")
    half_work_checkout_criteria = fields.Float(string="If Checkout Before", tracking=True,
                                               help="Calculate half of working time if checkout < ( End time - this count of [hours:minutes] )")
    calc_half_no_checkout_time = fields.Boolean(string="If No Checkout", tracking=True,
                                                help="Calculate half of working time if no checkout")
    overtime_factor = fields.Float(string="Overtime Factor", default=1.5, tracking=True)
    apply_working_hour_deduction = fields.Boolean(string="Apply Working Hour Deduction",
                                                  help="Consider Working Hour in calculating Deduction", default=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('grace_in_time', 'grace_out_time', 'overtime_factor')
    def _check_non_negative_values(self):
        for record in self:
            if record.overtime_factor < 1:
                raise ValidationError("Overtime Factor must be greater than or equal to 1.0")

    @api.constrains('apply_half_work', 'half_work_checkin_criteria', 'half_work_checkout_criteria', 'grace_in_time',
                    'grace_out_time')
    def _constrains_half_work(self):
        for record in self:
            if record.apply_half_work:
                if record.calc_half_no_checkout_time:
                    continue
                if record.half_work_checkin_criteria <= record.grace_in_time:
                    raise ValidationError("Half work checkin criteria must be greater than grace in time")
                elif record.half_work_checkout_criteria <= record.grace_out_time:
                    raise ValidationError("Half work checkout criteria must be greater than grace out time")

    @api.model
    def _get_time_fields(self):
        """Specify which float fields represent time values"""
        return [
            'grace_in_time',
            'grace_out_time',
            'half_work_checkin_criteria',
            'half_work_checkout_criteria',
        ]
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
