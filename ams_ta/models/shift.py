# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Shift(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.shift"
    _description = "Shift"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]  # "ams_base.follow_up_model",
    _sql_constraints = [('name_unique', 'unique(name)', 'Shift name must be unique!')]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, tracking=True)
    start_date = fields.Date(required=True, tracking=True)
    end_date = fields.Date(required=True, tracking=True)
    state = fields.Selection([('pending', 'Pending'), ('running', 'Running'), ('finished', 'Finished')],
                             compute="_compute_state", store=False, default="pending")
    is_default = fields.Boolean(tracking=True)
    is_exception = fields.Boolean(readonly=True)
    status = fields.Selection([('pending', 'Pending'), ('executed', 'Executed')], default='pending', readonly=True)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one('hr.employee', ondelete='restrict', string='Employee',
                                  domain=lambda self: self.env['hr.employee']._domain_follow_up())
    schedule_id = fields.Many2one('ams.schedule', ondelete='restrict', string='Schedule', required=True,
                                  domain="[('activate', '=', True)]")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('start_date', 'end_date', 'status')
    def _compute_state(self):
        """
        Compute the state based on the start_date, end_date, and status.
        Uses the user's timezone to determine the current date.
        """
        for record in self:
            if not record.start_date or not record.end_date:
                record.state = 'pending'
                continue

            # Get the current UTC datetime
            now_utc = fields.Datetime.now()
            # Convert the UTC datetime to the user's timezone
            now_tz = fields.Datetime.context_timestamp(record, now_utc).date()

            start_date = record.start_date
            end_date = record.end_date

            if now_tz < start_date:
                record.state = 'pending'
            elif start_date <= now_tz <= end_date:
                record.state = 'running'
            else:
                record.state = 'finished'

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods --------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for record in self:
            # if record.end_date <= fields.Datetime.now().date():
            #     raise ValidationError("End Date must be in the future.")
            if record.start_date and record.end_date and record.end_date < record.start_date:
                raise ValidationError("End Date must be greater than Start Date.")

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods --------------------------------------
    def action_execute(self):
        """
        Execute the shift processing logic:
        - Recalculate punch logs and associated timesheets.
        - Log processing details as notifications.
        - Update the shift status from 'pending' to 'executed'.
        """
        for record in self:
            # Recalculate punch logs and get the results
            results = self._recalculate_shift_punch_logs()

            # Log messages for each shift based on the processing results
            for result in results:
                message = (
                    f"Shift processed:\n"
                    f"- {result['punch_logs_processed']} punch logs processed.\n"
                    f"- {result['timesheets_deleted']} timesheets re-calculated."
                )
                record.message_post(body=message)

            # Update the shift status to 'executed' if it was 'pending'
            if record.status == 'pending':
                record.status = 'executed'

            # notification
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Shift Processed',
                    'message': 'Shift processed successfully.',
                    'sticky': False,
                },
            }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _recalculate_shift_punch_logs(self):
        """
        Recalculate punch logs for the current shift. This method performs the following:
        - Retrieves all punch logs related to the employee within the shift's date range.
        - Deletes any timesheets associated with these punch logs.
        - Executes the 'action_execute_punch_log' method for each punch log.

        Returns:
            List[Dict]: A list of dictionaries containing processing details for each shift.
        """
        results = []  # To store the results for each shift
        for record in self:
            # Search for punch logs linked to the employee within the shift's date range
            punch_logs = self.env['ams_ta.punch_log'].search([
                ('employee_id', '=', record.employee_id.id),
                ('date', '>=', record.start_date),
                ('date', '<=', record.end_date)
            ])

            # Gather all associated timesheet IDs (ignoring empty values)
            timesheet_ids = punch_logs.mapped('timesheet_id').ids

            # Delete the timesheet records in bulk if any are found
            timesheets_deleted = 0
            if timesheet_ids:
                timesheets_deleted = len(timesheet_ids)
                self.env['ams_ta.timesheet'].browse(timesheet_ids).unlink()

            # Execute the 'action_execute_punch_log' method for all punch logs
            for punch_log in punch_logs:
                punch_log.action_execute_punch_log()

            # Append the processing results for the current shift
            results.append({
                'punch_logs_processed': len(punch_logs),
                'timesheets_deleted': timesheets_deleted,
            })

        return results

    # endregion

    # region ---------------------- TODO[IMP]: Helper Methods --------------------------------------
    # endregion
