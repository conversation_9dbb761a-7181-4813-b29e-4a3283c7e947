from odoo import models, fields, api
from odoo.addons.ams_ta.helper.helper import convert_to_float_time

class TimesheetTimeUnit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.timesheet_time_unit"
    _inherit = ["ams_ta.base_timesheet"]
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region Basic

    apply_min_max = fields.Boolean(string="Apply Min/Max", readonly=True)
    apply_half_work = fields.Boolean(string="Apply Half Work", readonly=True)
    min_checkin_time = fields.Float(string="Min Check-in Time", help="Minimum time allowed to checkin", readonly=True)
    max_checkout_time = fields.Float(string="Max Check-out Time", help="Maximum time allowed to checkout",
                                     readonly=True)
    grace_in_time = fields.Float(string="Grace In Time", readonly=True)
    grace_out_time = fields.Float(string="Grace Out Time", readonly=True)
    absent_time_criteria = fields.Float(string="Absent Time Criteria", readonly=True)
    half_work_checkin_criteria = fields.Float(string="If Checkin After", readonly=True)
    half_work_checkout_criteria = fields.Float(string="If Checkout Before", readonly=True)
    calc_half_no_checkout_time = fields.Boolean(string="If No Checkout", readonly=True)
    start_time = fields.Float(string="Start Time", readonly=True)
    end_time = fields.Float(string="End Time", readonly=True)
    start_limit = fields.Float(string="Start Limit", readonly=True)
    unit_type = fields.Selection(selection=[('normal', 'Normal'), ('flexible', 'Flexible'), ('open', 'Open')],
                                 string="Unit Type", help="", default='normal', readonly=True)
    color = fields.Char(string="Color")
    overtime_factor = fields.Float(string="Overtime Factor", default=1.0, readonly=True)
    apply_working_hour_deduction = fields.Boolean(string="Apply Working Hour Deduction",
                                                  help="Consider Working Hour in calculating Deduction", default=True,
                                                  readonly=True)
    # endregion

    # region computed
    first_checkin_date = fields.Datetime(string="First Check-in Date", compute='_compute_checkin_dates_time',
                                         store=True)
    last_checkout_date = fields.Datetime(string="Last Check-out Date", compute='_compute_checkout_dates_time',
                                         store=True)
    first_checkin_time = fields.Float(string="First Check-in Time", compute='_compute_checkin_dates_time', store=True)
    last_checkout_time = fields.Float(string="Last Check-out Time", compute='_compute_checkout_dates_time', store=True)
    # endregion
    # region Relational
    timesheet_id = fields.Many2one('ams_ta.timesheet', string="Timesheet", ondelete='cascade')

    # endregion
    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('last_checkout_datetime')
    def _compute_checkout_dates_time(self):
        """ Compute the Hijri date, Gregorian date, and time from the date_time field. """
        for record in self:
            if record.last_checkout_datetime:
                date_info = self._convert_datetime(record.last_checkout_datetime)
                if date_info:
                    # record.date_hj = date_info['hijri_date']
                    record.last_checkout_date = date_info['gregorian_date']
                    record.last_checkout_time = convert_to_float_time(date_info['time'])
            else:
                record.last_checkout_date = None
                record.last_checkout_time = 0

            # calculate times
            record.calculate_times()

    @api.depends('first_checkin_datetime')
    def _compute_checkin_dates_time(self):
        """ Compute the Hijri date, Gregorian date, and time from the date_time field. """
        for record in self:
            if record.first_checkin_datetime:
                date_info = self._convert_datetime(record.first_checkin_datetime)
                if date_info:
                    # record.date_hj = date_info['hijri_date']
                    record.first_checkin_date = date_info['gregorian_date']
                    record.first_checkin_time = convert_to_float_time(date_info['time'])
            else:
                record.first_checkin_date = None
                record.first_checkin_time = 0

            # calculate times
            record.calculate_times()

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_manual_edit(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Manual Edit',
            'res_model': 'ams_ta.timesheet_time_unit',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def calculate_times(self):
        """
        Calculate working_time, delay_time, shortage_time, total_delay_shortage, overtime and overtime_factored
        based on check-in/out times and time unit configuration.
        """
        for record in self:
            # assign is_checked_in and is_checked_out
            record.is_checked_in = True if record.first_checkin_datetime else False
            record.is_checked_out = True if record.last_checkout_datetime else False

            # Ensure required fields are available and required time is greater than 0
            if not (record.first_checkin_time and record.end_time) or record.required_time <= 0.0:
                continue

            # check for overnight time unit and get first_checkin_time and last_checkout_time
            if record.is_overnight and record.last_checkout_time < record.first_checkin_time:
                first_checkin_time = record.first_checkin_time
                last_checkout_time = record.last_checkout_time + 24.0
                start_time = record.start_time
                end_time = record.end_time + 24.0
            else:
                first_checkin_time = record.first_checkin_time
                last_checkout_time = record.last_checkout_time
                start_time = record.start_time
                end_time = record.end_time

            # Calculate working time and half working time
            duration = last_checkout_time - first_checkin_time
            if record.apply_half_work:
                if not record.last_checkout_datetime:
                    if record.calc_half_no_checkout_time:
                        record.working_time = record.required_time / 2
                    else:
                        record.working_time = 0
                else:
                    checkin_limit = start_time + record.half_work_checkin_criteria
                    checkout_limit = end_time - record.half_work_checkout_criteria

                    # Adjust for time wraparound (e.g. 25.0 becomes 1.0)
                    if checkin_limit >= 24.0:
                        checkin_limit -= 24.0

                    # Calculate working time with half working
                    if duration > (record.required_time / 2) and (
                        first_checkin_time > checkin_limit or last_checkout_time < checkout_limit
                    ):
                        record.working_time = record.required_time / 2
                    else:
                        record.working_time = duration
            else:
                # Calculate working time without half working
                if record.last_checkout_datetime:
                    record.working_time = last_checkout_time - first_checkin_time
                else:
                    record.working_time = 0.0

            # Calculate Remaining time
            if record.last_checkout_datetime and record.working_time < record.required_time:
                record.remaining_time = record.required_time - record.working_time
            else:
                record.remaining_time = 0.0

            # Calculate delay time
            if record.is_overnight and first_checkin_time < 12.0:  # Check-in after midnight for overnight shift
                if first_checkin_time > ((start_time - 24.0) + record.grace_in_time):
                    record.delay_time = first_checkin_time - (start_time - 24.0)
                    record.is_delayed = True
                else:
                    record.delay_time = 0.0
                    record.is_delayed = False
            else:  # Regular case or overnight check-in before midnight
                if first_checkin_time > (start_time + record.grace_in_time):
                    record.delay_time = first_checkin_time - start_time
                    record.is_delayed = True
                else:
                    record.delay_time = 0.0
                    record.is_delayed = False

            # Calculate shortage time
            if record.last_checkout_datetime and last_checkout_time < (end_time - record.grace_out_time):
                record.shortage_time = end_time - last_checkout_time
                record.is_shortage = True
            else:
                record.shortage_time = 0.0
                record.is_shortage = False

            # Calculate total delay and shortage
            record.total_delay_shortage = record.delay_time + record.shortage_time

            # Calculate overtime
            if record.working_time > record.required_time:
                record.overtime = record.working_time - record.required_time
                record.is_overtime = True
            else:
                record.overtime = 0.0
                record.is_overtime = False

            # Calculate overtime factored
            record.overtime_factored = record.overtime * record.overtime_factor

            # assign is_absent
            if not record.first_checkin_datetime and not record.last_checkout_datetime:
                record.is_absent = True
            elif record.first_checkin_datetime and not record.last_checkout_datetime:
                # if record.apply_half_work and record.calc_half_no_checkout_time:
                #     record.is_absent = False
                # else:
                #     record.is_absent = True
                record.is_absent = False
            elif record.first_checkin_datetime and record.last_checkout_datetime:
                if record.working_time < record.absent_time_criteria:
                    record.is_absent = True
                else:
                    record.is_absent = False

            # Calculate total_deduction
            if record.is_absent:
                # Calculate total_deduction based on required time
                record.total_deduction = record.required_time
            elif record.apply_working_hour_deduction:
                # Calculate total_deduction based on remaining time
                record.total_deduction = record.remaining_time
            else:
                # Calculate total_deduction based on total_delay_shortage
                record.total_deduction = record.total_delay_shortage

            # Calculate Manual Edit
            if record._context.get('manual_edit', 0) == 1:
                record.is_manual_edit = True
                record.timesheet_id.is_manual_edit = True
                # record.timesheet_id.message_post(body='Manual Edit')

            # calculate ts times
            record.timesheet_id.calculate_ts_times()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
