# -*- coding: utf-8 -*-
from odoo.exceptions import ValidationError
from odoo import fields, models, api, _
from odoo.addons.ams_ta.helper.helper import *
from odoo.addons.ams_ta.api.dto_employee_list import *
import logging

_logger = logging.getLogger(__name__)


class Employee(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "hr.employee"
    _inherit = [
        "hr.employee",
        "mail.thread",
        "mail.activity.mixin"
    ]
    # ,"ams_base.follow_up_model",
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    last_generate_absent_date = fields.Date(string="Last Absent Generation Date")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    user_group_id = fields.Many2one(
        comodel_name="ams.user_group", string="User Group", tracking=True
    )

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate_yesterday_absent(self):
        yesterday = fields.Date.today() - relativedelta(days=1)
        employees = []
        for employee in self:
            employees.append(employee)

        self.generate_absent(employees, yesterday)

    def _cron_generate_absences(self):
        """
        Cron job to generate absences for all employees from the last `last_generate_absent_date` to yesterday.
        """
        today = fields.Date.today()
        yesterday = today - relativedelta(days=1)

        employees = self.search([])  # Fetch all employees

        for employee in employees:
            last_generate_date = employee.last_generate_absent_date or yesterday

            while last_generate_date < yesterday:
                last_generate_date += relativedelta(days=1)
                employee.generate_absent([employee], last_generate_date)

            # Update last generate date
            employee.last_generate_absent_date = yesterday

    def action_generate_test_punch_logs(self):
        """
        Generate random punch logs for each selected employee
        near Riyadh Gallery Mall in Saudi Arabia
        for the last 7 days including today
        """
        import random
        from datetime import datetime, timedelta
        import pytz

        if not self:
            raise ValidationError(_("No employees selected. Please select employees to generate punch logs."))

        # Get the coordinates of Riyadh Gallery Mall
        mall_lat = 24.7741
        mall_lng = 46.7250

        # Get the current user's timezone
        user_tz = pytz.timezone(self.env.user.tz or 'UTC')

        # Get today in the user's timezone
        today = datetime.now(user_tz).replace(hour=0, minute=0, second=0, microsecond=0)

        # Counter for created logs
        created_logs = 0

        for employee in self:
            # Generate punch logs for each of the last 7 days
            for day_offset in range(7):
                # Calculate the date (0 = today, 1 = yesterday, etc.)
                current_date = today - timedelta(days=day_offset)

                # Generate 1-3 random punch logs for each day
                num_logs = random.randint(1, 3)

                for i in range(num_logs):
                    # Generate random time during working hours (8 AM to 6 PM)
                    work_start_hour = 8
                    work_end_hour = 18
                    random_hour = random.randint(work_start_hour, work_end_hour)
                    random_minute = random.randint(0, 59)

                    # Create datetime for this punch
                    log_datetime = current_date.replace(hour=random_hour, minute=random_minute)

                    # Convert to UTC naive datetime for storage
                    # First convert to UTC
                    log_datetime_utc = log_datetime.astimezone(pytz.UTC)
                    # Then make it naive by removing the timezone info
                    naive_datetime = log_datetime_utc.replace(tzinfo=None)

                    # Generate random coordinates near Riyadh Gallery Mall (within ~500m)
                    # 0.005 degrees is approximately 500 meters
                    lat_offset = random.uniform(-0.005, 0.005)
                    lng_offset = random.uniform(-0.005, 0.005)
                    latitude = mall_lat + lat_offset
                    longitude = mall_lng + lng_offset

                    # Create the punch log
                    self.env['ams_ta.punch_log'].create({
                        'employee_id': employee.id,
                        'date_time': naive_datetime,
                        'latitude': latitude,
                        'longitude': longitude,
                        'from_mobile': True,
                        'device_serial': f"MOBILE-{random.randint(1000, 9999)}",
                        'notes': f"Auto-generated test punch log for {employee.name} on {current_date.strftime('%Y-%m-%d')}"
                    })
                    created_logs += 1

        # Show success message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _(f"Successfully generated {created_logs} punch logs for {len(self)} employees."),
                'sticky': False,
                'type': 'success',
                'next': {
                    'type': 'ir.actions.act_window_close'
                }
            }
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def generate_absent(self, employees, date):
        for employee in employees:
            employee_shift = employee.get_available_employee_shift(date)

            if not employee_shift:
                raise ValidationError("No valid shift found for the employee on the given date.")

            day_record = employee._get_day(employee_shift, date)

            if not day_record:
                raise ValidationError("No valid day record found for the employee on the given date.")

            if day_record.is_day_off:
                continue
            else:
                day_time_units = day_record.time_units_ids

                # Prepare the timesheet values
                timesheet_record_vals = employee._prepare_timesheet_record_vals(employee, employee_shift, day_record,
                                                                                date)

                # Handle timesheet record
                timesheet_record = employee._get_or_create_timesheet(timesheet_record_vals, employee, date)

                for day_time_unit in day_time_units:
                    # Handle timesheet Time unit
                    ts_time_unit = employee._get_or_create_ts_time_unit(timesheet_record, day_time_unit)

    def get_available_employee_shift(self, date):
        """return shift which date located and return shift based on priority
        exception  shift >> secondary shift >> primary shift >> default shift
        determines the applicable shift for an employee on a specific date, following the priority sequence.
        Args:
        date (datetime.date): The target date.

        Returns:
             recordset: The shift record or None if no shift is found.
        """

        exception_shift = self.env["ams_ta.shift"].search(
            [
                ("is_exception", "=", True),
                ("employee_id", "=", self.id),
                ("start_date", "<=", date),
                ("end_date", ">=", date),
            ],
            limit=1,
        )
        if exception_shift:
            return exception_shift

        # UserGroup connects employees to primary and secondary shifts.
        user_group = self.user_group_id
        if user_group:
            secondary_shift = user_group.secondary_shift_id
            if secondary_shift and secondary_shift.start_date <= date <= secondary_shift.end_date:
                return secondary_shift

            primary_shift = user_group.primary_shift_id
            if primary_shift and primary_shift.start_date <= date <= primary_shift.end_date:
                return primary_shift

        default_shift = self.env["ams_ta.shift"].search(
            [
                ("is_default", "=", True),
            ],
            limit=1,
        )

        if default_shift:
            return default_shift

        return None

    def _get_day(self, employee_shift, date):
        """
        Retrieve the day record for the given employee shift.
        """
        schedule = employee_shift.schedule_id

        if not schedule:
            return None

        # Get the day index
        if schedule.schedule_type == 'daily':
            start_date = employee_shift.start_date
            cycle_days = schedule.cycle_days
            day_difference = (date - start_date).days
            day_index = day_difference % cycle_days + 1
        else:
            day_index = WEEK_DAYS_LIST.index(date.strftime("%A")) + 1

        # Get the day record
        day_record = schedule.day_ids.filtered(lambda day: day.index == day_index)

        return day_record

    def _prepare_timesheet_record_vals(self, employee, shift, day_record, date):
        return {
            'name': f"TS_{employee.name}_{date}",
            'employee_id': employee.id,
            'date': date,
            'shift_id': shift.id,
            'schedule_id': shift.schedule_id.id,
            'schedule_day_id': day_record.id,
            'is_weekend': day_record.is_day_off,
            'is_dayoff': day_record.is_day_off,
            'is_absent': False,
            'is_attend_day': False,
        }

    def _get_or_create_timesheet(self, timesheet_record_vals, employee, date):
        """
        Retrieve or create a timesheet record for the given employee and date.
        """
        # Retrieve or create the timesheet
        timesheet_record = self.env['ams_ta.timesheet'].search(
            [('employee_id', '=', employee.id), ('date', '=', date)], limit=1
        )
        if timesheet_record:
            timesheet_record.write(timesheet_record_vals)
        else:
            timesheet_record = self.env['ams_ta.timesheet'].create(timesheet_record_vals)

        return timesheet_record

    def _get_or_create_ts_time_unit(self, timesheet_record, day_time_unit):
        """
        Retrieve or create a ts_time_unit record for the given timesheet and day_time_unit.
        """
        timesheet_time_unit_vals = self._prepare_timesheet_time_unit_record_vals(timesheet_record, day_time_unit)
        ts_time_unit = self._get_ts_time_unit(timesheet_record.ts_time_units_ids, timesheet_time_unit_vals.get('name'))

        if ts_time_unit:
            ts_time_unit.write(timesheet_time_unit_vals)
        else:
            ts_time_unit = self.env['ams_ta.timesheet_time_unit'].create(timesheet_time_unit_vals)

        return ts_time_unit

    def _prepare_timesheet_time_unit_record_vals(self, timesheet, time_unit):
        # Prepare the time unit values
        if time_unit:
            return {
                'name': f"time Unit [{time_unit.start_time} - {time_unit.end_time}]",
                'apply_min_max': time_unit.apply_min_max,
                'min_checkin_time': time_unit.min_checkin_time,
                'max_checkout_time': time_unit.max_checkout_time,
                'grace_in_time': time_unit.rule_id.grace_in_time,
                'grace_out_time': time_unit.rule_id.grace_out_time,
                'absent_time_criteria': time_unit.absent_time_criteria,
                'apply_half_work': time_unit.rule_id.apply_half_work,
                'half_work_checkin_criteria': time_unit.rule_id.half_work_checkin_criteria,
                'half_work_checkout_criteria': time_unit.rule_id.half_work_checkout_criteria,
                'calc_half_no_checkout_time': time_unit.rule_id.calc_half_no_checkout_time,
                'overtime_factor': time_unit.rule_id.overtime_factor,
                'apply_working_hour_deduction': time_unit.rule_id.apply_working_hour_deduction,
                'start_time': time_unit.start_time,
                'end_time': time_unit.end_time,
                'start_limit': time_unit.start_limit,
                'unit_type': time_unit.unit_type,
                'color': time_unit.color,
                'timesheet_id': timesheet.id,
                'required_time': time_unit.duration,
                'is_overnight': time_unit.is_overnight,
            }
        else:
            return {
                'name': f"Unknown time Unit",
                'apply_min_max': False,
                'grace_in_time': 0,
                'grace_out_time': 0,
                'absent_time_criteria': 0,
                'apply_half_work': False,
                'start_time': 0,
                'end_time': 23.99,
                'unit_type': 'normal',
                'timesheet_id': timesheet.id,
                'required_time': 0,
                'is_overnight': False
            }

    def _get_ts_time_unit(self, ts_time_units, time_unit_name):
        for ts_time_unit in ts_time_units:
            if ts_time_unit.name == time_unit_name:
                return ts_time_unit

    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def _prepare_employee_dto(self, employee=None, response_code='200', response_message='Success',
                              response_message_ar='تم بنجاح', error_message=''):
        """Prepare and return an EmployeeDTO object from hr.employee record"""
        return EmployeeDTO(
            response_code=response_code,
            response_message=response_message,
            response_message_ar=response_message_ar,
            emp_no=employee.employee_number if employee else '',
            user_name=employee.user_name if employee else '',
            english_name=employee.name if employee else '',
            arabic_name=employee.name if employee else '',
            email=employee.work_email if employee else '',
            phone_no=employee.work_phone if employee else '',
            dept_arabic_name=employee.department_id.name if employee and employee.department_id else '',
            dept_english_name=employee.department_id.name if employee and employee.department_id else '',
            bg_arabic_name=employee.user_group_id.name if employee and employee.user_group_id else '',
            bg_english_name=employee.user_group_id.name if employee and employee.user_group_id else '',
            area_arabic_name='',
            area_english_name='',
            branch_arabic_name='',
            branch_english_name='',
            error_message=error_message,
        )

    def _prepare_employee_vals(self, employee_dto):
        """Prepare and return an Employee object from EmployeeDTO"""
        employee_vals = {
            'employee_number': employee_dto.emp_no,
            'name': employee_dto.english_name or employee_dto.arabic_name,
            'work_email': employee_dto.email,
            'work_phone': employee_dto.phone_no,
            # 'private_city': employee_dto.area_english_name or employee_dto.area_arabic_name,
            # 'company_id': employee_dto.branch_english_name or employee_dto.branch_arabic_name
        }
        # If user_name is provided, find or create a res.users record associated with the employee
        if employee_dto.user_name:
            user = self.env['res.users'].search([('login', '=', employee_dto.user_name)], limit=1)

            if not user:
                user = self.env['res.users'].sudo().create({
                    'name': employee_dto.english_name or employee_dto.arabic_name,
                    'login': employee_dto.user_name,
                    'email': employee_dto.email,
                })

            employee_vals['user_id'] = user.id

        # If department_id is provided, find or create a hr.department record associated with the employee
        if employee_dto.dept_english_name or employee_dto.dept_arabic_name:
            raw_name = (employee_dto.dept_english_name or employee_dto.dept_arabic_name or "").strip()

            department = self.env['hr.department'].search([('name', '=ilike', raw_name)], limit=1)

            if not department:
                department = self.env['hr.department'].sudo().create({
                    'name': raw_name,
                })

            employee_vals['department_id'] = department.id

        # If bs is provided, find or create a ams.user_group record associated with the employee
        if employee_dto.bg_english_name or employee_dto.bg_arabic_name:
            user_group = self.env['ams.user_group'].search(
                [('name', '=', employee_dto.bg_english_name or employee_dto.bg_arabic_name)], limit=1)

            if not user_group:
                user_group = self.env['ams.user_group'].sudo().create({
                    'name': employee_dto.bg_english_name or employee_dto.bg_arabic_name,
                })

            employee_vals['user_group_id'] = user_group.id

        return employee_vals

    def _create_or_update_employee(self, employee_vals):
        """Create or update an employee record based on the provided values"""
        # Step 1: Check if the employee already exists
        employee = self.env['hr.employee'].search([
            '|', '|',
            ('employee_number', '=', employee_vals['employee_number'] ),
            ('user_id', '=', employee_vals['user_id']),
            ('work_email', '=', employee_vals['work_email'])
        ], limit=1)

        # Step 2: Create or update the employee
        if employee:
            employee.write(employee_vals)
        else:
            employee = self.env['hr.employee'].sudo().create(employee_vals)

        return employee

    def get_employee_info(self, identifier: str, filter_type: str) -> EmployeeDTO:
        """Search employee by employee_number or username based on filter_type"""
        search_field = 'user_name' if filter_type.lower() == 'username' else 'employee_number'

        employee = self.env['hr.employee'].search([(search_field, '=', identifier)], limit=1)

        if employee:
            return self._prepare_employee_dto(employee)

        # If not found, return default DTO with custom error message
        return self._prepare_employee_dto(
            response_code='404',
            response_message='Employee not found',
            response_message_ar='الموظف غير موجود',
            error_message=f'No employee found with {search_field}: {identifier}'
        )

    def get_employees_info(self, key=None, filter_type="all") -> EmployeeListDTO:
        domain = []

        if key is not None:
            key = key.strip()
            if filter_type == 'deptname':
                dept = self.env['hr.department'].search([('name', '=ilike', key)], limit=1)
                if not dept:
                    return EmployeeListDTO(
                        response_code="404",
                        response_message="Department not found",
                        response_message_ar="القسم غير موجود",
                        employees=[],
                        error_message=f"No department found with name: {key}"
                    )
                domain = [('department_id', '=', dept.id)]

        employees = self.sudo().search(domain)

        if not employees:
            return EmployeeListDTO(
                response_code="404",
                response_message="No employees found",
                response_message_ar="لا يوجد موظفين",
                employees=[],
                error_message="No employees found"
            )

        employee_list = [self._prepare_employee_dto(emp) for emp in employees]

        return EmployeeListDTO(
            response_code="200",
            response_message="Success",
            response_message_ar="تم بنجاح",
            employees=employee_list,
            error_message=""
        )

    def add_employee_data(self, emp):
        """Add employee data or update data if the employee exists"""
        # Step 1: Validate input data and convert to EmployeeDTO
        if not emp:
            return "Error:No input found"

        employee_dto = EmployeeDTO.from_dict(emp)

        if not isinstance(employee_dto, EmployeeDTO):
            return "Error:invalid input data expected EmployeeDTO"

        if not (employee_dto.emp_no and employee_dto.user_name):
            return "Error: Missing required fields in EmployeeDTO (emp_no, user_name, or email)"

        # Step 2: prepare the employee odoo record values
        employee_vals = self._prepare_employee_vals(employee_dto)

        # Step 3: create or update the employee odoo record
        self._create_or_update_employee(employee_vals)

        # Step 4: return success message
        return ""

    # endregion
