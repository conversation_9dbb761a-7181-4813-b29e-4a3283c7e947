from odoo.addons.ams_ta.helper.helper import *
from odoo import models, fields, api, _
from datetime import datetime, timezone, date, time

from odoo.addons.ams_ta.api.dto_attendance_deduction import AttendanceDeductionDTO
from odoo.addons.ams_ta.api.dto_attendance_deduction_list import AttendanceDeductionListDTO
from odoo.addons.ams_ta.api.dto_attendance_list import AttendanceListDTO
from odoo.addons.ams_ta.api.dto_attendance_log import AttendanceLogDTO
from odoo.addons.ams_ta.api.dto_employee import EmployeeDTO
from odoo.addons.ams_ta.api.dto_attendance import AttendanceDTO
from typing import List, Optional
from pytz import timezone, UTC


class Timesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.timesheet"
    _inherit = ["ams_ta.base_timesheet"]
    _order = 'date'
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region Basic
    is_dayoff = fields.Boolean(string="Is Day Off", readonly=True)
    is_weekend = fields.Boolean(string="Is Weekend", readonly=True)
    is_vacation = fields.Boolean(string="Is Vacation", readonly=True)
    is_permission = fields.Boolean(string="Is Permission", readonly=True)
    is_public_vacation = fields.Boolean(string="Is Public Vacation", readonly=True)
    is_absent = fields.Boolean(string="Is Absent", readonly=True)
    is_attend_day = fields.Boolean(string="Is Attend Day", readonly=True)
    is_working_day = fields.Boolean(string="Is Working Day", readonly=True)

    delay_alert_sent = fields.Boolean(string="Delay Alert Sent", readonly=True)
    shortage_alert_sent = fields.Boolean(string="Shortage Alert Sent", readonly=True)
    absent_alert_sent = fields.Boolean(string="Absent Alert Sent", readonly=True)

    time_off_hours = fields.Float(string="Time Off Hours", readonly=True)

    delay_count = fields.Integer(string="Delay Count")
    shortage_count = fields.Integer(string="Shortage Count")
    absent_count = fields.Integer(string="Absent Count")
    dayoff_count = fields.Integer(string="Dayoff Count")
    weekend_count = fields.Integer(string="Weekend Count")
    attend_day_count = fields.Integer(string="Attend Day Count")
    working_day_count = fields.Integer(string="Working Day Count")
    overtime_day_count = fields.Integer(string="Overtime Day Count")
    vacation_count = fields.Integer(string="Vacation Count")
    permission_count = fields.Integer(string="Permission Count")
    public_vacation_count = fields.Integer(string="Total Vacation Count")

    color = fields.Char(string="Color")
    punch_logs = fields.Char(string="Punch Logs")

    # endregion

    # region Relational
    shift_id = fields.Many2one('ams_ta.shift', string="Shift")
    schedule_id = fields.Many2one('ams.schedule', string="Schedule")
    schedule_day_id = fields.Many2one('ams.schedule_day', string="Schedule Day")
    ts_time_units_ids = fields.One2many('ams_ta.timesheet_time_unit', 'timesheet_id', string="Time Units")

    # endregion
    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def write(self, vals):
        res = super(Timesheet, self).write(vals)
        if any(key in vals for key in ['is_vacation', 'time_off_hours', 'is_public_vacation', 'is_weekend']):
            self.calculate_ts_times()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _send_timesheet_notification(self, notification_type):
        """ Send notifications for delay, shortage, or absence """
        for record in self:
            if not record.employee_id.user_id:
                continue  # Skip if the employee has no related user

            message = ""
            if notification_type == "delay":
                message = _("Employee %s has a delay of %.2f hours on %s.") % (
                    record.employee_id.name, record.delay_time, record.date)
            elif notification_type == "shortage":
                message = _("Employee %s has a shortage of %.2f hours on %s.") % (
                    record.employee_id.name, record.shortage_time, record.date)
            elif notification_type == "absent":
                message = _("Employee %s was absent on %s.") % (record.employee_id.name, record.date)

            # Create a notification as an activity
            self.env['mail.activity'].create({
                'res_model_id': self.env['ir.model']._get_id('ams_ta.timesheet'),
                'res_id': record.id,
                'user_id': record.employee_id.user_id.id,  # Notify the employee's user
                'summary': _("Timesheet Alert"),
                'note': message,
                'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,  # Generic "To Do"
                'date_deadline': fields.Date.today(),
            })

    def _check_and_send_notifications(self):
        """ Check conditions and send notifications for delay, shortage, and absence """
        for record in self:
            if record.is_delayed and not record.delay_alert_sent:
                record._send_timesheet_notification("delay")
                record.delay_alert_sent = True  # Avoid duplicate notifications

            if record.is_shortage and not record.shortage_alert_sent:
                record._send_timesheet_notification("shortage")
                record.shortage_alert_sent = True

            if record.is_absent and not record.absent_alert_sent:
                record._send_timesheet_notification("absent")
                record.absent_alert_sent = True

    def calculate_ts_times(self):
        """Main method to calculate time tracking statistics for records"""
        for record in self:
            # get time units
            time_units = record.ts_time_units_ids

            # Aggregate common fields from time_units
            record._aggregate_common_fields(time_units)

            # Determine if current day is a day off
            record.is_dayoff = any([record.is_weekend,
                                    record.is_vacation,
                                    record.is_public_vacation])

            # Apply different calculation logic based on day type
            if record.is_dayoff:
                record._process_dayoff()
            else:
                # Determine if permission
                record.is_permission = record.time_off_hours > 0

                record._process_working_day(time_units)

            # Set computed fields , counters fields , and set minutes fields
            record._set_computed_fields()
            record._update_counters()
            record._set_minutes_fields()

            # record._check_and_send_notifications()

    def _aggregate_common_fields(self, time_units):
        """Aggregates common fields from all time units """
        # initialize variables
        first_checkin_datetime = None
        last_checkout_datetime = None
        first_log_checkin_datetime = None
        last_log_checkout_datetime = None

        working_time = 0
        is_overnight = False

        for time_unit in time_units:
            # update variables
            if first_checkin_datetime is None or time_unit.first_checkin_datetime < first_checkin_datetime:
                first_checkin_datetime = time_unit.first_checkin_datetime
                first_log_checkin_datetime = time_unit.first_log_checkin_datetime

            if last_checkout_datetime is None or time_unit.last_checkout_datetime > last_checkout_datetime:
                last_checkout_datetime = time_unit.last_checkout_datetime
                last_log_checkout_datetime = time_unit.last_log_checkout_datetime

            working_time += time_unit.working_time

            if time_unit.is_overnight:
                is_overnight = True

        # update record
        self.write({
            'first_checkin_datetime': first_checkin_datetime,
            'last_checkout_datetime': last_checkout_datetime,
            'first_log_checkin_datetime': first_log_checkin_datetime,
            'last_log_checkout_datetime': last_log_checkout_datetime,
            'working_time': working_time,
            'is_overnight': is_overnight
        })

    def _process_dayoff(self):
        """Handle calculations for non-working days (all hours count as overtime)"""
        self.update({
            'required_time': 0.0,  # No required time on days off
            'remaining_time': 0.0,
            'delay_time': 0.0,
            'shortage_time': 0.0,
            'total_delay_shortage': 0.0,
            'overtime': self.working_time,  # All hours count as overtime
            'overtime_factored': self.working_time * 1.5,
            # All hours count as overtime (1.5x) TODO[IMP]: Retrieve overtime factor from configuration
            'total_deduction': 0.0,
            'is_absent': False
        })

    def _process_working_day(self, time_units):
        """Handle calculations for regular working days"""
        # initialize variables
        required_time = 0.0
        remaining_time = 0.0
        delay_time = 0.0
        shortage_time = 0.0
        total_delay_shortage = 0.0
        total_deduction = 0.0
        overtime = 0.0
        over_time_factored = 0.0
        is_absent = True

        # loop through time units
        for time_unit in time_units:
            # update variables
            required_time += time_unit.required_time
            remaining_time += time_unit.remaining_time
            delay_time += time_unit.delay_time
            shortage_time += time_unit.shortage_time
            total_delay_shortage += time_unit.total_delay_shortage
            total_deduction += time_unit.total_deduction
            overtime += time_unit.overtime
            over_time_factored += time_unit.overtime_factored
            if not time_unit.is_absent:
                is_absent = False

        # Handle permission: use time_off_hours to reduce delay and shortage deficits
        forgiven_hours = 0.0
        if self.is_permission:
            permission_hours = self.time_off_hours or 0.0

            # Reduce delay time first using permission hours
            delay_reduction = min(delay_time, permission_hours)
            delay_time -= delay_reduction
            permission_hours -= delay_reduction

            # Then reduce shortage time using any remaining permission hours
            shortage_reduction = min(shortage_time, permission_hours)
            shortage_time -= shortage_reduction
            permission_hours -= shortage_reduction

            # Update total delay and shortage after applying permission
            total_delay_shortage = delay_time + shortage_time

            # Calculate total forgiven hours
            forgiven_hours = delay_reduction + shortage_reduction

            # Adjust remaining_time: Increase effective working time by forgiven_hours
            # Note: working_time is computed from check-in/out times in calculate_times.
            # Here, we simulate the forgiveness by effectively increasing the working_time.
            effective_working_time = self.working_time + forgiven_hours
            # Recalculate remaining time as the difference between required_time and effective working time.
            remaining_time = max(required_time - effective_working_time, 0)

        # update record
        self.update({
            'required_time': required_time,
            'remaining_time': remaining_time,
            'delay_time': delay_time,
            'shortage_time': shortage_time,
            'total_delay_shortage': total_delay_shortage,
            'overtime': overtime,
            'overtime_factored': over_time_factored,
            'total_deduction': total_deduction,
            'is_absent': is_absent
        })

    def _set_computed_fields(self):
        """Set common fields for all record types"""
        self.update({
            'is_working_day': not self.is_dayoff,
            'is_attend_day': not self.is_absent,
            'is_delayed': self.delay_time > 0,
            'is_shortage': self.shortage_time > 0,
            'is_overtime': self.overtime > 0
        })

    def _set_minutes_fields(self):
        """Set minutes fields for all record types"""
        self.update({
            'required_time_min': self.required_time * 60,
            'working_time_min': self.working_time * 60,
            'remaining_time_min': self.remaining_time * 60,
            'delay_time_min': self.delay_time * 60,
            'shortage_time_min': self.shortage_time * 60,
            'total_delay_shortage_min': self.total_delay_shortage * 60,
            'overtime_min': self.overtime * 60,
            'overtime_factored_min': self.overtime_factored * 60,
        })

    def _update_counters(self):
        """Convert boolean flags to integer counters"""
        self.update({
            'delay_count': int(self.is_delayed),
            'shortage_count': int(self.is_shortage),
            'absent_count': int(self.is_absent),
            'dayoff_count': int(self.is_dayoff),
            'attend_day_count': int(self.is_attend_day),
            'weekend_count': int(self.is_weekend),
            'working_day_count': int(self.is_working_day),
            'overtime_day_count': int(self.is_overtime),
            'vacation_count': int(self.is_vacation),
            'public_vacation_count': int(self.is_public_vacation),
            'permission_count': int(self.is_permission),
        })

    # endregion

    # region ---------------------- TODO[IMP]: Api Methods -------------------------------------
    def get_employees_attendance_deduction(self, date_from, date_to, emp_no: str = None,
                                           filter_type: str = "employee_number") -> AttendanceDeductionListDTO:
        """
        Business logic for processing attendance deduction requests.
        If emp_no is provided, returns attendance deduction for that specific employee.
        Otherwise, returns deductions for all employees within the date range.

        Args:
            emp_no: Optional employee number or other identifier.
            date_from: Start date (datetime object).
            date_to: End date (datetime object).
            filter_type: Field to filter on, default is 'employee_number'.
        """

        domain = []
        if emp_no:
            domain += [(filter_type, '=', emp_no)]

        employees = self.env['hr.employee'].sudo().search(domain)
        if not employees:
            return AttendanceDeductionListDTO(
                response_code="404",
                response_message="No employees found",
                response_message_ar="لم يتم العثور على موظفين",
                deduction_list=[],
                error_message="No employees found for the given criteria"
            )

        deduction_dtos = []

        for employee in employees:
            # Build domain using employee ID
            att_domain = self._build_attendance_domain(employee.id, date_from, date_to)

            # Get timesheet records
            timesheets = self.env['ams_ta.timesheet'].sudo().search(att_domain)

            # Sum all total_deduction from time units (Float values in hours)
            total_deduction_hours_float = sum(
                unit.total_deduction or 0.0
                for ts in timesheets
                for unit in ts.ts_time_units_ids
            )

            # Sum required_time directly from timesheet records
            total_required_hours_float = sum(
                ts.required_time or 0.0
                for ts in timesheets
            )

            # Convert to total minutes
            total_deduction_hours = int(total_deduction_hours_float)
            total_deduction_minutes = int(total_deduction_hours_float * 60)
            total_required_minutes = int(total_required_hours_float * 60)

            # Create DTO per employee
            deduction_dto = AttendanceDeductionDTO(
                response_code="200",
                response_message="Success",
                response_message_ar="تم بنجاح",
                emp_no=employee.employee_number,
                deduction_minutes=total_deduction_minutes,
                deduction_hours=total_deduction_hours,
                required_minutes=total_required_minutes,
                error_message=""
            )
            deduction_dtos.append(deduction_dto)

        # Return list DTO
        return AttendanceDeductionListDTO(
            response_code="200",
            response_message="Success",
            response_message_ar="تم بنجاح",
            deduction_list=deduction_dtos,
            error_message=""
        )

    def get_employees_attendance(self, key: str, date_from: date, date_to: date,
                                 filter_type: str) -> AttendanceListDTO:

        domain = []
        if key:
            # Search department using the filter
            key = key.strip()
            department = self.env['hr.department'].search([(filter_type, '=ilike', key)], limit=1)

            if not department:
                return AttendanceListDTO(
                    response_code="404",
                    response_message="Department not found",
                    response_message_ar="القسم غير موجود",
                    att_list=[],
                    error_message=f"Department '{key}' not found using filter {filter_type}"
                )

            domain = [('department_id', '=', department.id)]

        employees = self.env['hr.employee'].sudo().search(domain)

        if not employees:
            return AttendanceListDTO(
                response_code="404",
                response_message="No employees found",
                response_message_ar="لم يتم العثور على موظفين",
                att_list=[],
                error_message="No active employees found for the given criteria"
            )

        attendance_dtos = []

        for employee in employees:
            # Build domain using employee ID
            domain = self._build_attendance_domain(employee.id, date_from, date_to)

            # Search attendance records
            attendance_records = self.env['ams_ta.timesheet'].sudo().search(domain, order='date')

            # Create EmployeeDTO and attendance logs
            emp_dto = employee._prepare_employee_dto(employee)
            logs = [self._prepare_log_dto(record) for record in attendance_records]

            # Create AttendanceDTO for each employee
            attendance_dto = AttendanceDTO(
                response_code="200",
                response_message="Success",
                response_message_ar="تم بنجاح",
                emp=emp_dto,
                logs=logs,
                error_message=""
            )
            attendance_dtos.append(attendance_dto)

        # Create final AttendanceListDTO
        dept_msg = f" for department {key}" if key else ""
        return AttendanceListDTO(
            response_code="200",
            response_message=f"Successfully retrieved attendance records{dept_msg}",
            response_message_ar=f"تم استرجاع سجلات الحضور بنجاح{' لقسم ' + key if key else ''}",
            att_list=attendance_dtos,
            error_message=""
        )

    def get_employee_attendance_by_identifier(self, identifier: str, date_from, date_to,
                                              filter_type: str) -> AttendanceDTO:
        """Get attendance for a single employee"""

        # Find employee by either employee_number or username
        employee = self.env['hr.employee'].search([(filter_type, '=', identifier)], limit=1)

        if not employee:
            return AttendanceDTO(
                response_code="404",
                response_message="Employee not found",
                response_message_ar="الموظف غير موجود",
                emp=None,
                logs=[],
                error_message=f"Error: Employee {identifier} doesn't exist!!"
            )
        domain = self._build_attendance_domain(employee.id, date_from, date_to)

        # Get attendance records
        emp_dto = employee._prepare_employee_dto(employee)
        attendance_records = self.env['ams_ta.timesheet'].sudo().search(domain, order='date')

        logs = [self._prepare_log_dto(record) for record in attendance_records]
        return AttendanceDTO(
            response_code="200",
            response_message="Success",
            response_message_ar="تم بنجاح",
            emp=emp_dto,
            logs=logs,
            error_message=""
        )

    # def _prepare_log_dto(self, record):
    #     status = self._get_attendance_status(record)
    #
    #     # in_time = str(record.first_checkin_datetime.strftime("%H:%M:%S") if record.first_checkin_datetime# else "--:--:--" )
    #     # out_time = str(record.last_checkout_datetime.strftime("%H:%M:%S") if record.last_checkout_datetime else "--:--:--")
    #     tz = timezone(record.employee_id.resource_id.tz or 'UTC')
    #     in_time = record.first_checkin_datetime.astimezone(tz).strftime(
    #         "%H:%M:%S") if record.first_checkin_datetime else "--:--:--"
    #     out_time = record.last_checkout_datetime.astimezone(tz).strftime(
    #         "%H:%M:%S") if record.last_checkout_datetime else "--:--:--"
    #     date_str = record.date.strftime("%d/%m/%Y %H:%M:%S") if record.date else "--/--/---- --:--:--"
    #     required_time = str(record.required_time if record.required_time else "--:--:--")
    #     working_time = str(record.working_time if record.working_time else "--:--:--")
    #     delay_time = str(record.delay_time if record.delay_time else "--:--:--")
    #     shortage_time = str(record.shortage_time if record.shortage_time else "--:--:--")
    #     date_value = datetime.combine(record.date,
    #                                   time()) if record.date else None  # permission_count = int(record.permission_count if record.permission_count else 0) // 60
    #     notes = str(record.notes or "")
    #
    #     return AttendanceLogDTO(
    #         response_code="1",
    #         response_message="OK",
    #         response_message_ar="تم بنجاح",
    #         date=date_value,
    #         status=status or 0,
    #         in_time=in_time,
    #         out_time=out_time,
    #         required_time=required_time,
    #         working_time=working_time,
    #         delay=delay_time,
    #         shortage=shortage_time,
    #         comment=notes,
    #         date_string=date_str,
    #         error_message=""
    #     )

    def _prepare_log_dto(self, record):

        def _format_time(dt, tz):
            return dt.astimezone(tz).strftime("%H:%M:%S") if dt else "--:--:--"

        def _safe_str(value):
            return str(value) if value else "--:--:--"

        tz = timezone(record.employee_id.resource_id.tz or 'UTC')
        status = self._get_attendance_status(record)

        return AttendanceLogDTO(
            response_code="200",
            response_message="Success",
            response_message_ar="تم بنجاح",
            date=datetime.combine(record.date, time()) if record.date else None,
            status=status or 0,
            in_time=_format_time(record.first_checkin_datetime, tz),
            out_time=_format_time(record.last_checkout_datetime, tz),
            required_time=_safe_str(record.required_time),
            working_time=_safe_str(record.working_time),
            delay=_safe_str(record.delay_time),
            shortage=_safe_str(record.shortage_time),
            comment=str(record.notes or ""),
            date_string=record.date.strftime("%d/%m/%Y %H:%M:%S") if record.date else "--/--/---- --:--:--",
            error_message=""
        )

    def _get_attendance_status(self, record) -> int:
        """Get attendance status code"""
        if record.is_dayoff:
            return 1  # DayOff
        if record.is_absent:
            return 2  # Absent
        if record.is_vacation:
            return 3  # Vacation
        return 0  # Working Day

    def _build_attendance_domain(self, employee_id, date_from=None, date_to=None):
        domain = [('employee_id', '=', employee_id)]

        if date_from:
            domain.append(('date', '>=', date_from))
        if date_to:
            domain.append(('date', '<=', date_to))

        return domain

    # endregion

    # region ---------------------- TODO[IMP]: Helper Methods -------------------------------------
    # endregion
