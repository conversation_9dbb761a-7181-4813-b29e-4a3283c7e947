import datetime
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.addons.ams_ta.helper.helper import *
from math import radians, sin, cos, sqrt, atan2


class PunchLog(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.punch_log"
    _description = 'Punch Log'
    _inherit = ["ams_base.abstract_model", "ams_base.follow_up_model"]
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region Basic
    name = fields.Char(string="Name", compute='_compute_name', store=True)
    date_time = fields.Datetime(string="Log Date Time", required=True)
    execute_log_time = fields.Datetime(string="Execute Log Time", readonly=True, copy=False)
    # employee_number = fields.Char(string="Employee Number")
    # enroll_number = fields.Char(string="Enroll Number")
    device_serial = fields.Char(string="Device Serial")
    state = fields.Selection([('pending', 'Pending'), ('executed', 'Executed')], string="State", default='pending')
    dept_path_code = fields.Char(string="Department Path Code")
    notes = fields.Char(string="Notes")
    latitude = fields.Float(string='Geo Latitude', digits=(10, 7))
    longitude = fields.Float(string='Geo Longitude', digits=(10, 7))
    from_mobile = fields.Boolean(string="From Mobile")

    # region  Computed
    date = fields.Date(string="Log Date", compute='_compute_dates_times', store=True)
    time = fields.Float(string="Log Time", compute='_compute_dates_times', store=True)

    # endregion

    # region Relational
    timesheet_id = fields.Many2one('ams_ta.timesheet', string="Timesheet", readonly=True, copy=False)
    employee_id = fields.Many2one('hr.employee', string="Employee", required=True,
                                  domain=lambda self: self.env['hr.employee']._domain_follow_up())
    employee_number = fields.Char(string="Employee Number", related='employee_id.employee_number', store=True)
    enroll_number = fields.Char(string="Enroll Number", related='employee_id.enroll_number', store=True)
    department_id = fields.Many2one(string="Department", related='employee_id.department_id', store=True)
    user_group_id = fields.Many2one(string="User Group", related='employee_id.user_group_id', store=True)
    # endregion
    # region of Geo Fence Fields
    is_within_geo_fence = fields.Boolean(string="Within Geo-Fence", default=True)
    geo_fence_distance = fields.Float(string="Distance from Geo-Fence (m)", default=0.0)
    nearest_location_id = fields.Many2one('ams_ta.geo_fence_location', string="Nearest Location")
    geo_fence_status = fields.Selection([
        ('valid', 'Valid'),
        ('outside', 'Outside Boundary'),
        ('no_fence', 'No Geo-Fence')
    ], string="Geo-Fence Status", default='valid')
    geo_fence_action_taken = fields.Selection([
        ('none', 'None'),
        ('warn', 'Warning'),
        ('flag', 'Flagged'),
        ('reject', 'Rejected'),
    ], string="Geo-Fence Action Taken", default='none')

    # endregion

    # endregion
    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('employee_id.name', 'date_time')
    def _compute_name(self):
        employees = self.employee_id
        employee_names = {emp.id: emp.name for emp in employees}
        for record in self:
            if not record.employee_id or not record.date_time:
                record.name = False
                continue

            dt_tz = fields.Datetime.context_timestamp(record, record.date_time)
            formatted_datetime = dt_tz.strftime("%Y-%m-%d %H:%M")

            record.name = f"{record.employee_id.name} - {formatted_datetime}"

    @api.depends('date_time')
    def _compute_dates_times(self):

        """ Compute the Hijri date, Gregorian date, and time from the date_time field. """
        for record in self:
            if record.date_time:
                date_info = self._convert_datetime(record.date_time)
                if date_info:
                    # record.date_hj = date_info['hijri_date']
                    record.date = date_info['gregorian_date']
                    record.time = convert_to_float_time(date_info['time'])
            else:
                record.date = None
                record.time = 0

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('latitude', 'longitude')
    def _check_coordinates(self):
        for record in self:
            if not (-90 <= record.latitude <= 90):
                raise ValidationError("Latitude must be between -90 and 90.")
            if not (-180 <= record.longitude <= 180):
                raise ValidationError("Longitude must be between -180 and 180.")

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self, values_list):
        if not isinstance(values_list, list):
            values_list = [values_list]

        punch_logs = super(PunchLog, self).create(values_list)

        # Execute action in batch for all created records
        punch_logs.action_execute_punch_log()

        return punch_logs  # Ensure the records are returned

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_execute_punch_log(self):
        """
        Process the punch log, validate input, retrieve or create necessary records, and handle check-in/out.
        """
        for log in self:
            # Validate required fields
            log._validate_punch_log()

            # Geo-Fence Validation
            log._validate_geo_fence_punch_log()

            # Handle Geo-Fence Action Based on Validation
            user_group = log.employee_id.user_group_id
            if log.geo_fence_status == 'outside' and user_group:
                action = user_group.geo_fence_action
                log.geo_fence_action_taken = action
                if action == 'reject':
                        return

            # Find Employee Shift
            employee_shift = log.employee_id.get_available_employee_shift(log.date)

            if not employee_shift:
                raise ValidationError("No valid shift found for the employee on the given date.")

            day_record = log.employee_id._get_day(employee_shift, log.date)

            if not day_record:
                raise ValidationError("No valid day record found for the employee on the given date.")

            day_time_unit = log._get_day_time_unit(day_record)

            # Determine the time_unit start date for this punch log
            time_unit_start_date = log._get_time_unit_start_date(day_time_unit, log.date, log.time)

            # Prepare the timesheet values
            timesheet_record_vals = log.employee_id._prepare_timesheet_record_vals(log.employee_id, employee_shift,
                                                                                   day_record, time_unit_start_date)

            # Handle timesheet record
            timesheet_record = log.employee_id._get_or_create_timesheet(timesheet_record_vals, log.employee_id,
                                                                        time_unit_start_date)

            # Handle timesheet Time unit
            ts_time_unit = log.employee_id._get_or_create_ts_time_unit(timesheet_record, day_time_unit)

            # Process check-in/out for the time_unit
            log._handle_checkin_checkout(ts_time_unit, log.date_time, time_unit_start_date, log.time)

            # update from mobile and notes for  ts_time_unit
            ts_time_unit.from_mobile = log.from_mobile
            ts_time_unit.notes = log.notes

            # Update the state to done
            log.state = 'executed'
            log.execute_log_time = fields.Datetime.now()
            log.timesheet_id = timesheet_record.id

    # region ---------------------- TODO[IMP]: Action Methods For Geo Fence -------------------------------------
    def action_validate_geo_fence_punch_log(self):
        """
        Validate the punch log and update the geo-fence status.
        """
        self._validate_geo_fence_punch_log()

    def _validate_geo_fence_punch_log(self):
        """
        Process the punch log with geo-fence validation.
        """
        for log in self:
            # Skip geo-fence validation if coordinates are not provided
            if log.latitude == 0 and log.longitude == 0:
                log.geo_fence_status = 'no_fence'
                continue

            # Get user group and check if geo-fencing is enabled
            user_group = log.employee_id.user_group_id
            if not user_group or not user_group.apply_geo_fencing:
                log.geo_fence_status = 'no_fence'
            else:
                # Validate against geo-fence locations
                log._validate_geo_fence()


    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """
        Calculate the Haversine distance between two points in meters.
        """

        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))
        distance = 6371000 * c  # Earth radius is 6371 km, result in meters

        return distance

    def _validate_geo_fence(self):
        """
        Validate if the punch location is within any of the allowed geo-fence locations.
        """
        user_group = self.employee_id.user_group_id
        locations = user_group.geo_fence_location_ids.filtered(lambda l: l.is_active)

        if not locations:
            self.geo_fence_status = 'no_fence'
            return

        min_distance = float('inf')
        nearest_location = None
        is_within_fence = False

        # Get tolerance from user group
        tolerance = user_group.geo_fence_tolerance

        # Check distance to each location
        for location in locations:
            distance = self.calculate_distance(
                self.latitude, self.longitude,
                location.latitude, location.longitude
            )

            # Update nearest location if this one is closer
            if distance < min_distance:
                min_distance = distance
                nearest_location = location

            # Check if within this location's radius + tolerance
            if distance <= (location.radius + tolerance):
                is_within_fence = True
                break

        # Update punch log with geo-fence validation results
        self.is_within_geo_fence = is_within_fence
        self.geo_fence_distance = min_distance
        # Still tracks nearest location even if outside all fences
        self.nearest_location_id = nearest_location.id if nearest_location else False
        self.geo_fence_status = 'valid' if is_within_fence else 'outside'

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------


    def _validate_punch_log(self):
        """
        Validate required fields for the punch log.
        """
        for record in self:  # Loop through each record
            if not record.date_time or not record.employee_id:
                raise ValidationError("Punch Log must have a valid Date Time and Employee.")

    def _get_day_time_unit(self, day_record):
        """
        Retrieve the appropriate time unit for the punch log.
        """
        if not day_record:
            return None

        # Get the time units
        time_units = day_record.time_units_ids

        # Get punch log time
        time = self.time

        if not time_units or not time:
            return None

        # Get the appropriate time unit
        day_time_unit = self._get_time_unit(time_units, time)

        # If no time unit is found, find the nearest one
        if not day_time_unit:
            day_time_unit = self._get_nearest_time_unit(time_units, time)

        return day_time_unit

    def _get_time_unit(self, time_units, time):
        """
        Determines the appropriate time unit for a given time.
        """
        if not time_units or time is None:
            return None

        for time_unit in time_units:
            # Handle 'open' type times
            if time_unit.unit_type == 'open' or (time_unit.start_time == 0.0 and time_unit.end_time == 23.99):
                return time_unit

            # get the start and end times for a time unit
            start_time = time_unit.min_checkin_time
            end_time = time_unit.max_checkout_time

            # Handle overnight time_units
            if time_unit.is_overnight:
                # Check if time is within the overnight range
                if (start_time <= time < 24) or (0 <= time <= end_time):
                    return time_unit
            else:
                # Check if time falls within the range for non-overnight time_units
                if start_time <= time <= end_time:
                    return time_unit

        # No appropriate time_unit was found
        return None

    def _get_nearest_time_unit(self, time_units, time):
        """
        Find the nearest time_unit to the given time from the list of time_units.
        """
        if not time_units or time is None:
            return None

        nearest_time_unit = None
        smallest_difference = float('inf')

        for time_unit in time_units:
            # Handle 'open' type time_units
            if time_unit.unit_type == 'open' or (time_unit.start_time == 0.0 and time_unit.end_time == 0.0):
                return time_unit  # Immediately return the open time_unit if found

            # get the start and end times for a time_unit
            start_time = time_unit.min_checkin_time
            end_time = time_unit.max_checkout_time

            # Calculate the distance from the time to both start_time and end_time
            start_diff = abs(start_time - time)
            end_diff = abs(end_time - time)

            # Find the minimum difference for this time_unit
            min_diff = min(start_diff, end_diff)

            # Update the nearest time_unit if the difference is smaller
            if min_diff < smallest_difference:
                smallest_difference = min_diff
                nearest_time_unit = time_unit

        return nearest_time_unit

    def _get_time_unit_start_date(self, time_unit, log_date, log_time):
        """
        Determine the correct time_unit start date for a punch log.

        Args:
            time_unit: The time unit record
            log_datetime: The datetime of the punch log

        Returns:
            date: The date when the time_unit started
        """
        end_time = time_unit.max_checkout_time if time_unit else 23.99

        if time_unit and time_unit.is_overnight:
            if 0 <= log_time <= end_time:
                # If punch is after midnight but before time_unit end,
                # the time_unit started yesterday
                return log_date - datetime.timedelta(days=1)
            else:
                # If punch is after time_unit start but before midnight,
                # the shift starts today
                return log_date
        else:
            # For regular time_units, use the log date
            return log_date

    def _handle_checkin_checkout(self, ts_time_unit, log_datetime, log_date, log_time):
        """
        Handles the check-in and check-out times for a time unit,
        ensuring proper handling for overnight time units and boundary cases.
        """
        # get the start and end times for a ts_time_unit
        start_time = ts_time_unit.min_checkin_time
        end_time = ts_time_unit.max_checkout_time

        # get timezone
        timezone = self.env.user.partner_id.tz

        # Helper method to check if time falls within the range
        def check_is_log_time_in_time_unit_range():
            """
            Check if the log time falls within the time_unit's range.
            Handles both normal and overnight time_units.
            """
            if ts_time_unit.is_overnight:
                # Check if time is within the overnight range
                if (start_time <= log_time < 24) or (0 <= log_time <= end_time):
                    return True
            else:
                # Check if time falls within the range for non-overnight time_units
                if start_time <= log_time <= end_time:
                    return True

            # Log time does not fall within the time_unit's range
            return False

        # Helper method to check if time falls within the start limit range
        def check_is_log_time_in_start_limit_range():
            """
            Check if the log time falls within the start limit range.
            Handles both normal and overnight time_units.
            """
            if ts_time_unit.is_overnight:
                # Check if time is within the overnight start limit range
                if (start_time <= log_time < 24) or (0 <= log_time <= ts_time_unit.start_limit):
                    return True
            else:
                # Check if time falls within the range for non-overnight time_units
                if start_time <= log_time <= ts_time_unit.start_limit:
                    return True

            # Log time does not fall within the time_unit's start limit range
            return False

        # Helper method to calculate the datetime based on the given date and time
        def calculate_datetime(date, time, is_checkout=False):
            """
            Calculate the correct datetime for check-in or check-out.
            Adjust for overnight time_units if necessary.
            """
            if is_checkout and ts_time_unit.is_overnight and end_time < 12:  # Assuming overnight ends in the morning
                date += datetime.timedelta(days=1)

            # get local datetime
            local_datetime = compine_date_time(date, time)

            # convert to UTC
            utc_datetime = convert_to_utc(local_datetime, timezone)

            return utc_datetime

        # Helper method to update first check-in details
        def update_checkin():
            """
            Updates the first check-in details, either using log time
            or the time_unit's start time if outside the valid range.
            """
            ts_time_unit.in_latitude = self.latitude
            ts_time_unit.in_longitude = self.longitude
            ts_time_unit.first_log_checkin_datetime = log_datetime
            if check_is_log_time_in_time_unit_range():
                if ts_time_unit.unit_type == 'flexible' and not check_is_log_time_in_start_limit_range():
                    ts_time_unit.first_checkin_datetime = calculate_datetime(log_date, ts_time_unit.start_limit)
                else:
                    ts_time_unit.first_checkin_datetime = log_datetime
            else:
                ts_time_unit.first_checkin_datetime = calculate_datetime(log_date, start_time)

            ts_time_unit._compute_checkin_dates_time()

        # Helper method to update last check-out details
        def update_checkout():
            """
            Updates the last check-out details, either using log time
            or the time_unit's end time if outside the valid range.
            """
            ts_time_unit.out_latitude = self.latitude
            ts_time_unit.out_longitude = self.longitude
            ts_time_unit.last_log_checkout_datetime = log_datetime
            if check_is_log_time_in_time_unit_range():
                ts_time_unit.last_checkout_datetime = log_datetime
            else:
                ts_time_unit.last_checkout_datetime = calculate_datetime(log_date, end_time, is_checkout=True)

            ts_time_unit._compute_checkout_dates_time()

        # Main logic: updating check-in and check-out times

        # Case 1: No first check-in exists, meaning this is the first record
        if not ts_time_unit.first_checkin_datetime:
            update_checkin()

        # Case 2: A new check-in time earlier than the existing first check-in is detected
        elif log_datetime < ts_time_unit.first_checkin_datetime:
            # If there's no last check-out time or the current last check-out is earlier than the existing first check-in
            if not ts_time_unit.last_checkout_datetime or ts_time_unit.last_checkout_datetime < ts_time_unit.first_checkin_datetime:
                # Move the current first check-in to last check-out
                ts_time_unit.last_checkout_datetime = ts_time_unit.first_checkin_datetime
                ts_time_unit.last_log_checkout_datetime = ts_time_unit.first_log_checkin_datetime
                ts_time_unit.out_latitude = ts_time_unit.in_latitude
                ts_time_unit.out_longitude = ts_time_unit.in_longitude

            # Update with the new earlier check-in
            update_checkin()

        # Case 3: A new check-out time later than the existing last check-out is detected
        elif not ts_time_unit.last_checkout_datetime or ts_time_unit.last_checkout_datetime < log_datetime:
            update_checkout()
        # endregion

    # region ---------------------- TODO[IMP]: Helper Methods -------------------------------------
    def _convert_to_user_timezone(self, datetime_value):
        """Convert datetime from UTC to the user's timezone."""
        return fields.Datetime.context_timestamp(self, datetime_value)

    def _convert_datetime(self, datetime_value):
        """Convert datetime to hijri and gregorian dates along with time."""
        user_datetime = self._convert_to_user_timezone(datetime_value)
        # Get Gregorian date
        gregorian_date = user_datetime.date()
        return {
            # 'hijri_date': self.convert_to_hijri_date_format(datetime_value),
            'gregorian_date': gregorian_date,  # Gregorian date object (date only) in YYYY-MM-DD format
            'time': user_datetime.time()  # Time component in HH:MM:SS format (24-hour format)
        }

    # endregion
