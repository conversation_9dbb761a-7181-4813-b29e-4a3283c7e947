from odoo import models, fields, api
from odoo.addons.ams_ta.helper.helper import convert_to_float_time

class BaseTimesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_ta.base_timesheet"
    _inherit = ["ams_base.abstract_model", "ams_base.follow_up_model", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region Basic
    name = fields.Char(string="Name", readonly=True)
    date = fields.Date(string="Date")
    notes = fields.Char(string="Notes")
    # endregion

    # region Special
    first_checkin_datetime = fields.Datetime(string="First Check-in Datetime", tracking=True)
    last_checkout_datetime = fields.Datetime(string="Last Check-out Datetime", tracking=True)
    first_log_checkin_datetime = fields.Datetime(string="First log Check-in Datetime", readonly=True)
    last_log_checkout_datetime = fields.Datetime(string="Last log Check-out Datetime", readonly=True)
    required_time = fields.Float(string="Required Time", readonly=True)
    working_time = fields.Float(string="Working Time", readonly=True)
    remaining_time = fields.Float(string="Remaining Time", readonly=True)
    delay_time = fields.Float(string="Delay Time", readonly=True)
    shortage_time = fields.Float(string="Shortage Time", readonly=True)
    total_delay_shortage = fields.Float(string="Total Delay and Shortage", readonly=True)
    total_deduction = fields.Float(string="Total Deduction", readonly=True)
    overtime = fields.Float(string="Overtime", readonly=True)
    overtime_factored = fields.Float(string="Overtime Factored", readonly=True)
    is_checked_in = fields.Boolean(string="Is Checked In", readonly=True)
    is_checked_out = fields.Boolean(string="Is Checked Out", readonly=True)
    is_delayed = fields.Boolean(string="Is Delayed", readonly=True)
    is_shortage = fields.Boolean(string="Is Shortage", readonly=True)
    is_overtime = fields.Boolean(string="Is Overtime", readonly=True)
    is_absent = fields.Boolean(string="Is Absent", readonly=True, default=True)
    is_manual_edit = fields.Boolean(string="Manual Edit", default=False, readonly=True)
    is_overnight = fields.Boolean(string="Is Overnight", default=False, readonly=True)
    from_mobile = fields.Boolean(string="From Mobile", default=False, readonly=True)
    in_longitude = fields.Float(string="Check-in Longitude", readonly=True)
    in_latitude = fields.Float(string="Check-in Latitude", readonly=True)
    out_longitude = fields.Float(string="Check-out Longitude", readonly=True)
    out_latitude = fields.Float(string="Check-out Latitude", readonly=True)

    required_time_min = fields.Float(string="Required Time (Minutes)")
    working_time_min = fields.Float(string="Working Time (Minutes)")
    remaining_time_min = fields.Float(string="Remaining Time (Minutes)")
    delay_time_min = fields.Float(string="Delay Time (Minutes)")
    shortage_time_min = fields.Float(string="Shortage Time (Minutes)")
    total_delay_shortage_min = fields.Float(string="Total Delay and Shortage (Minutes)")
    overtime_min = fields.Float(string="Overtime (Minutes)")
    overtime_factored_min = fields.Float(string="Overtime Factored (Minutes)")

    # endregion

    # region Relational
    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee', string="Employee Manager", related='employee_id.parent_id',store=True)
    coach_id = fields.Many2one('hr.employee', string="Employee Coach", related='employee_id.coach_id',store=True)
    employee_number = fields.Char(string="Employee Number", related='employee_id.employee_number', store=True)
    enroll_number = fields.Char(string="Enroll Number", related='employee_id.enroll_number', store=True)
    department_id = fields.Many2one(string="Department", related='employee_id.department_id', store=True)
    user_group_id = fields.Many2one(string="User Group", related='employee_id.user_group_id', store=True)
    # endregion

    # region Computed
    first_checkin_date = fields.Datetime(string="First Check-in Date" , compute='_compute_checkin_dates_time', store=True)
    last_checkout_date = fields.Datetime(string="Last Check-out Date" , compute='_compute_checkout_dates_time', store=True)
    first_checkin_time = fields.Float(string="First Check-in Time" , compute='_compute_checkin_dates_time', store=True)
    last_checkout_time = fields.Float(string="Last Check-out Time" , compute='_compute_checkout_dates_time', store=True)

    first_checkin_time_char = fields.Char(string="First Check-in Time (Char)", )  # compute="_compute_checkin_time_char"
    last_checkout_time_char = fields.Char(
        string="Last Check-out Time (Char)", )  # compute="_compute_checkout_time_char"

    # endregion
    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # @api.depends('first_checkin_time')
    # def _compute_checkin_time_char(self):
    #     ...
    #
    # @api.depends('last_checkout_time')
    # def _compute_checkout_time_char(self):
    #     ...

    @api.depends('required_time', 'working_time', 'remaining_time', 'delay_time', 'shortage_time', 'overtime',
                 'overtime_factored')
    def _compute_time_min(self):
        for record in self:
            record.required_time_min = record.required_time * 60
            record.working_time_min = record.working_time * 60
            record.remaining_time_min = record.remaining_time * 60
            record.delay_time_min = record.delay_time * 60
            record.shortage_time_min = record.shortage_time * 60
            record.total_delay_shortage_min = record.delay_time_min + record.shortage_time_min
            record.overtime_min = record.overtime * 60
            record.overtime_factored_min = record.overtime_factored * 60

    @api.depends('first_checkin_datetime')
    def _compute_checkin_dates_time(self):
        """ Compute the Hijri date, Gregorian date, and time from the date_time field. """
        for record in self:
            if record.first_checkin_datetime:
                date_info = self._convert_datetime(record.first_checkin_datetime)
                if date_info:
                    # record.date_hj = date_info['hijri_date']
                    record.first_checkin_date = date_info['gregorian_date']
                    record.first_checkin_time = convert_to_float_time(date_info['time'])
            else:
                record.first_checkin_date = None
                record.first_checkin_time = 0

            # # calculate times
            # record.calculate_times()

    @api.depends('last_checkout_datetime')
    def _compute_checkout_dates_time(self):
        """ Compute the Hijri date, Gregorian date, and time from the date_time field. """
        for record in self:
            if record.last_checkout_datetime:
                date_info = self._convert_datetime(record.last_checkout_datetime)
                if date_info:
                    # record.date_hj = date_info['hijri_date']
                    record.last_checkout_date = date_info['gregorian_date']
                    record.last_checkout_time = convert_to_float_time(date_info['time'])
            else:
                record.last_checkout_date = None
                record.last_checkout_time = 0

            # # calculate times
            # record.calculate_times()

    # @api.depends('first_checkin_time','last_checkout_time','first_checkin_date','last_checkout_date')
    # def _compute_calculate_times(self):
    #     for record in self:
    #         record.calculate_times()

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # def calculate_times(self):
    #     pass
    # endregion

    # region ---------------------- TODO[IMP]: Private Methods -------------------------------------
    # endregion
    # region ---------------------- TODO[IMP]: Helper Methods -------------------------------------
    def _convert_to_user_timezone(self, datetime_value):
        """Convert datetime from UTC to the user's timezone."""
        return fields.Datetime.context_timestamp(self, datetime_value)

    def _convert_datetime(self, datetime_value):
        """Convert datetime to hijri and gregorian dates along with time."""
        user_datetime = self._convert_to_user_timezone(datetime_value)
        # Get Gregorian date
        gregorian_date = user_datetime.date()
        return {
            # 'hijri_date': self.convert_to_hijri_date_format(datetime_value),
            'gregorian_date': gregorian_date,  # Gregorian date object (date only) in YYYY-MM-DD format
            'time': user_datetime.time()  # Time component in HH:MM:SS format (24-hour format)
        }
    # endregion
