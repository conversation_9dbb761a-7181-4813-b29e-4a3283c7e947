from odoo import models, fields, api, _
from odoo.exceptions import UserError


class ResUser(models.Model):
    """Predefined Odoo module addons/base/res_users"""

    _name = "res.users"
    _inherit = "res.users"

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _get_allowed_department_ids(self, employee=False):
        """return allowed department based on current login user & employee path code
        and follow-up level"""
        # if not employee:
        #     employee = self._get_associated_employee()
        #
        # if employee:
        #     if employee.follow_up_level == 'supervisor' or employee.follow_up_level == 'user':
        #         if employee.department_id:
        #             return employee.department_id
        #     elif employee.follow_up_level == 'manager':
        #         # TODO refine search to get all depts start with path code to get main department and sub departments
        #         depts = self.env['hr.department'].search([('path_code', 'ilike', employee.path_code)])
        #         return depts
        #     elif employee.follow_up_level == 'admin':
        #         # return all departments
        #         return self.env['hr.department'].search([])

        return False

    def _get_allowed_employee_ids(self, employee=False, allowed_department_ids=False):
        if not employee:
            employee = self._get_associated_employee()
        if employee:
            if not allowed_department_ids:
                allowed_department_ids = self._get_allowed_department_ids(employee)

        if employee and allowed_department_ids:
            if employee.follow_up_level != 'user':
                employee_ids = self.env['hr.employee'].search([('department_id', 'in', allowed_department_ids.ids)])
                return employee_ids
            else:
                return employee

        return []

    def _get_associated_employee(self):
        return self.env['hr.employee'].search([('user_id', '=', self.env.uid)], limit=1)
    # endregion ----------------------