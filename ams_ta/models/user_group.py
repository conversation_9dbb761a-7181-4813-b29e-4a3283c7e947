# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class UserGroup(models.Model):
    _name = "ams.user_group"
    _inherit = "ams.user_group"
    _description = "User Group"

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    employee_ids = fields.One2many(comodel_name="hr.employee", inverse_name="user_group_id", string="Employees",
                                   help="")
    primary_shift_id = fields.Many2one(comodel_name="ams_ta.shift", string="Primary shift", help="", tracking=True)
    secondary_shift_id = fields.Many2one(comodel_name="ams_ta.shift", string="Secondary shift", help="", tracking=True)

    # Enable/disable geo-fencing for this user group
    apply_geo_fencing = fields.Boolean(string="Apply Geo-Fencing", default=False)

    # Geo-fence locations (one-to-many relationship)
    geo_fence_location_ids = fields.One2many(
        'ams_ta.geo_fence_location',
        'user_group_id',
        string="Geo-Fence Locations"
    )

    # Tolerance distance in meters
    geo_fence_tolerance = fields.Float(
        string="Geo-Fence Tolerance (meters)",
        default=100.0,
        help="Maximum allowed distance from defined locations in meters"
    )

    # Action when outside geo-fence
    geo_fence_action = fields.Selection([
        ('warn', 'Warn Only'),
        ('reject', 'Reject Punch'),
        ('flag', 'Flag for Review')
    ], string="Action When Outside Geo-Fence", default='warn')
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('geo_fence_tolerance')
    def _check_geo_fence_tolerance(self):
        for record in self:
            if record.geo_fence_tolerance < 0:
                raise ValidationError("Geo-fence tolerance cannot be negative")
            if record.geo_fence_tolerance > 1000:  # Example maximum of 1km
                raise ValidationError("Geo-fence tolerance cannot exceed 1000 meters")
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
