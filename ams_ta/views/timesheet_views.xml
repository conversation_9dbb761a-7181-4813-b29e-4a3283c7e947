<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- timesheet_view_form-->
    <record id="timesheet_view_form" model="ir.ui.view">
        <field name="name">ams_ta.timesheet.form</field>
        <field name="model">ams_ta.timesheet</field>
        <field name="arch" type="xml">
            <form string="Timesheet">
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id" readonly="1"/>
                            <field name="date" readonly="1"/>
                            <field name="first_checkin_datetime" widget="datetime" readonly="1"
                                   decoration-danger="first_checkin_datetime != first_log_checkin_datetime"/>
                            <field name="last_checkout_datetime" widget="datetime" readonly="1"
                                   decoration-danger="last_checkout_datetime != last_log_checkout_datetime"/>
                        </group>
                        <group>
                            <field name="required_time" widget="float_time" readonly="1"/>
                            <field name="working_time" widget="float_time"/>
                            <field name="delay_time" widget="float_time" decoration-danger="delay_time > 0"/>
                            <field name="shortage_time" widget="float_time" decoration-danger="shortage_time > 0"/>
                            <field name="overtime" widget="float_time" decoration-success="overtime > 0"/>
                        </group>
                    </group>
                    <group>
                        <field name="notes"/>
                    </group>
                    <notebook>
                        <page string="Time Units">
                            <!--                            readonly="context.get('manual_edit', '0')=='0'" -->
                            <field name="ts_time_units_ids" context="{'manual_edit':0}">
                                <list delete="0" create="0">
                                    <field name="name"/>
                                    <field name="first_checkin_time" widget="float_time"/>
                                    <field name="first_checkin_datetime" optional="hide"
                                           decoration-danger="first_checkin_datetime != first_log_checkin_datetime"/>
                                    <field name="last_checkout_time" widget="float_time"
                                           decoration-danger="last_checkout_datetime != last_log_checkout_datetime"/>
                                    <field name="last_checkout_datetime" optional="hide"/>
                                    <button type="object" name="action_manual_edit" title="Manual Edit"
                                            icon="fa-edit" context="{'manual_edit':1}"
                                            invisible="current_employee_level == 'user'"/>
                                    <field name="required_time" widget="float_time"/>
                                    <field name="working_time" widget="float_time"/>
                                    <field name="remaining_time" optional="hide" widget="float_time"
                                           decoration-danger="remaining_time > 0"/>
                                    <field name="delay_time" widget="float_time" decoration-danger="delay_time > 0"/>
                                    <field name="shortage_time" widget="float_time"
                                           decoration-danger="shortage_time > 0"/>
                                    <field name="total_delay_shortage" optional="hide" widget="float_time"
                                           decoration-danger="shortage_time > 0"/>
                                    <field name="overtime" widget="float_time" decoration-success="overtime > 0"/>
                                    <field name="overtime_factored" widget="float_time" optional="hide"
                                           decoration-success="overtime_factored > 0"/>
                                    <field name="total_deduction" widget="float_time"
                                           decoration-danger="shortage_time > 0"/>
                                    <field name="notes" optional="hide"/>
                                </list>
                            </field>
                        </page>
                        <page string="Time Summary">
                            <group>
                                <group>
                                    <field name="overtime_factored" widget="float_time"
                                           decoration-success="overtime_factored > 0"/>
                                    <field name="remaining_time" widget="float_time"
                                           decoration-danger="remaining_time > 0"/>
                                    <field name="total_delay_shortage" widget="float_time"
                                           decoration-danger="total_delay_shortage > 0"/>
                                    <field name="total_deduction" widget="float_time"
                                           decoration-danger="total_deduction > 0"/>
                                    <field name="first_log_checkin_datetime" widget="datetime"/>
                                    <field name="last_log_checkout_datetime" widget="datetime"/>
                                    <!-- <field name="first_checkin_time" widget="float_time"/>-->
                                    <!-- <field name="last_checkout_time" widget="float_time"/>-->
                                </group>
                                <group>
                                    <field name="is_checked_in"/>
                                    <field name="is_checked_out"/>
                                    <field name="is_delayed"/>
                                    <field name="is_shortage"/>
                                    <field name="is_overtime"/>
                                    <field name="is_absent"/>
                                </group>
                            </group>
                        </page>
                        <Page string="Employee Info">
                            <group>
                                <group>
                                    <field name="shift_id" readonly="1"/>
                                    <field name="schedule_id" readonly="1"/>
                                    <field name="schedule_day_id" readonly="1"/>
                                </group>
                                <group>
                                    <field name="department_id" readonly="1"/>
                                    <field name="last_department_id" readonly="1"/>
                                    <field name="manager_id" readonly="1"/>
                                    <field name="coach_id" readonly="1"/>
                                </group>
                            </group>
                        </Page>
                        <page string="Other Info" name="other_info">
                            <group>
                                <group>
                                    <field name="delay_alert_sent"/>
                                    <field name="shortage_alert_sent"/>
                                    <field name="absent_alert_sent"/>
                                    <field name="is_manual_edit"/>
                                </group>
                                <group>
                                    <field name="is_weekend"/>
                                    <field name="is_overnight"/>
                                    <field name="is_attend_day"/>
                                    <field name="is_working_day"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: timesheet_view_list-->
    <record id="timesheet_view_list" model="ir.ui.view">
        <field name="name">ams_ta.timesheet.list</field>
        <field name="model">ams_ta.timesheet</field>
        <field name="arch" type="xml">
            <list string="Timesheet">
                <field name="color" readonly="1" string="`" widget="color"/>
                <field name="name" optional="hide"/>
                <field name="employee_id" />
                <field name="date" />
                <!--                <field name="first_checkin_time" widget="float_time"/>-->
                <field name="first_checkin_datetime" optional="hide"/>
                <field name="first_checkin_time" widget="float_time" optional="show"/>
                <!--                <field name="last_checkout_time" widget="float_time"/>-->
                <field name="last_checkout_datetime" optional="hide"/>
                <field name="last_checkout_time"  widget="float_time" optional="show"/>
                <field name="required_time" optional="show" widget="float_time"/>
                <field name="working_time" optional="show" widget="float_time"/>
                <field name="remaining_time" optional="hide"/>
                <field name="delay_time" widget="float_time" decoration-danger="is_delayed==1"/>
                <field name="shortage_time" widget="float_time" decoration-danger="is_shortage==1"/>
                <field name="total_delay_shortage" optional="hide"/>
                <field name="overtime" optional="hide" widget="float_time"/>
                <field name="overtime_factored" optional="hide"/>
                <field name="is_delayed" optional="hide"/>
                <field name="is_shortage" optional="hide"/>
                <field name="is_overnight" optional="hide"/>
                <field name="department_id" optional="hide"/>
                <field name="notes" optional="hide"/>
                <field name="total_deduction" optional="hide"/>
                <field name="is_dayoff" optional="hide"/>
                <field name="is_weekend" optional="hide"/>
                <field name="is_vacation" optional="hide"/>
                <field name="is_public_vacation" optional="hide"/>
                <field name="is_absent" optional="hide"/>
                <field name="is_attend_day" optional="hide"/>
                <field name="is_working_day" optional="hide"/>
                <field name="is_manual_edit" optional="hide"/>
                <field name="delay_alert_sent" optional="hide"/>
                <field name="shortage_alert_sent" optional="hide"/>
                <field name="absent_alert_sent" optional="hide"/>
                <field name="shift_id" optional="hide"/>
                <field name="schedule_id" optional="hide"/>
                <field name="schedule_day_id" optional="hide"/>
                <field name="required_time_min" optional="hide"/>
                <field name="delay_time_min" optional="hide"/>
                <field name="is_checked_in" optional="hide"/>
                <field name="is_checked_out" optional="hide"/>
                <field name="last_department_id" optional="hide"/>
                <field name="dept_depth" optional="hide"/>
                <field name="last_dept_depth" optional="hide"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: timesheet_view_search-->
    <record id="timesheet_view_search" model="ir.ui.view">
        <field name="name">ams_ta.timesheet.search</field>
        <field name="model">ams_ta.timesheet</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <field name="enroll_number"/>
                <filter string="Today" name="today"
                        domain="[('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23, 59, 59))),
                ('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0, 0, 0)))]"/>
                <filter string="Yesterday" name="yesterday"
                        domain="[('date', '&lt;=', datetime.datetime.combine(context_today() - relativedelta(days=1), datetime.time(23, 59, 59))),
                ('date', '&gt;=', datetime.datetime.combine(context_today() - relativedelta(days=1), datetime.time(0, 0, 0)))]"/>
                <filter string="Delayed" name="is_delayed" domain="[('is_delayed', '=', True)]"/>
                <filter string="Shortage" name="is_shortage" domain="[('is_shortage', '=', True)]"/>
                <filter string="Is Day Off" name="is_dayoff" domain="[('is_dayoff', '=', True)]"/>
                <filter string="Is Vacation" name="is_vacation" domain="[('is_vacation', '=', True)]"/>
                <filter string="Is Absent" name="is_absent" domain="[('is_absent', '=', True)]"/>
                <filter string="Is Attend Day" name="is_attend_day" domain="[('is_attend_day', '=', True)]"/>
                <filter string="Is Working Day" name="is_working_day" domain="[('is_working_day', '=', True)]"/>
                <filter string="Manual Edit" name="manual_edit" domain="[('is_manual_edit', '=', True)]"/>

                <group expand="1" string="Group By">
                    <filter string="Date" name="date" context="{'group_by': 'date'}"/>
                    <filter string="Shift" name="shift" context="{'group_by': 'shift_id'}"/>
                    <filter string="Time-off" name="time_off_hours" context="{'group_by': 'time_off_hours'}"/>
                    <filter string="Public Vacation" name="public_vacation"
                            context="{'group_by': 'is_public_vacation'}"/>
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="group_by_department" context="{'group_by': 'department_id'}"/>
                    <filter string="User Group" name="group_by_user_group" context="{'group_by': 'user_group_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--Timesheet Pivot View-->
    <record id="timesheet_view_pivot" model="ir.ui.view">
        <field name="name">ams_ta.timesheet.pivot</field>
        <field name="model">ams_ta.timesheet</field>
        <field name="arch" type="xml">
            <pivot string="Timesheet Analysis">
                <!-- Measures -->
                <field name="required_time" type="measure" widget="float_time"/>
                <field name="working_time" type="measure" widget="float_time"/>
                <field name="delay_time" type="measure" widget="float_time"/>
                <field name="shortage_time" type="measure" widget="float_time"/>
                <field name="overtime" type="measure" widget="float_time"/>

                <!-- Default groupings -->
                <field name="employee_id" type="row"/>
                <field name="date" interval="day" type="col"/>
            </pivot>
        </field>
    </record>

    <record id="timesheet_server_action" model="ir.actions.server">
        <field name="name">Timesheet</field>
        <field name="model_id" ref="model_ams_ta_timesheet"/>
        <field name="binding_model_id" ref="model_ams_ta_timesheet"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_ta.timesheet'].sudo().action_open_views(
                {
                    'action_ref': 'ams_ta.timesheet_action',
                    'use_domain_follow_up_visibility': False
                }
            )

        </field>
        <field name="type">ir.actions.server</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
    </record>


    <!--TODO[IMP]: timesheet_action-->
    <record id="timesheet_action" model="ir.actions.act_window">
        <field name="name">Timesheet</field>
        <field name="res_model">ams_ta.timesheet</field>
        <field name="view_mode">list,pivot,form</field>
        <field name="context">{'search_default_available': 1, 'apply_followup_domain': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Timesheet
            </p>
            <p>
                Create Timesheet
            </p>
        </field>
    </record>

</odoo>
