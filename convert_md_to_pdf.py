#!/usr/bin/env python3
"""
python3 convert_md_to_pdf_improved.py
<PERSON><PERSON> to convert Markdown file with Mermaid diagrams to PDF
"""

import os
import re
import subprocess
import tempfile
import shutil
import sys
import argparse
from pathlib import Path

def extract_mermaid_diagrams(content):
    """
    Extract mermaid diagrams from markdown content and replace with image references
    """
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    diagrams = []

    def replace_mermaid(match):
        diagram_content = match.group(1)
        diagram_id = len(diagrams)
        diagrams.append(diagram_content)
        # Use absolute path to ensure proper image resolution
        return f'\n\n<div class="diagram-container">\n![Diagram {diagram_id + 1}](diagram_{diagram_id + 1}.png)\n</div>\n\n'

    # Replace mermaid blocks with image references
    modified_content = re.sub(mermaid_pattern, replace_mermaid, content, flags=re.DOTALL)

    # Clean up problematic Unicode characters in code blocks
    # Replace tree characters with ASCII equivalents
    unicode_replacements = {
        '├──': '|--',
        '└──': '`--',
        '│': '|',
        '├': '|',
        '└': '`'
    }

    for unicode_char, ascii_replacement in unicode_replacements.items():
        modified_content = modified_content.replace(unicode_char, ascii_replacement)

    return modified_content, diagrams

def create_mermaid_images(diagrams, output_dir):
    """
    Create PNG images from mermaid diagram content
    """
    for i, diagram in enumerate(diagrams):
        # Create temporary mermaid file
        mermaid_file = os.path.join(output_dir, f'temp_diagram_{i + 1}.mmd')
        with open(mermaid_file, 'w', encoding='utf-8') as f:
            f.write(diagram)

        # Convert to PNG with better sizing and quality
        output_image = os.path.join(output_dir, f'diagram_{i + 1}.png')
        cmd = [
            'mmdc',
            '-i', mermaid_file,
            '-o', output_image,
            '-w', '1400',  # Increased width for better quality
            '-H', '1000',  # Increased height for better quality
            '-s', '2',     # Scale factor for better resolution
            '--backgroundColor', 'white'  # Ensure white background
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"Created diagram_{i + 1}.png")
        except subprocess.CalledProcessError as e:
            print(f"Error creating diagram {i + 1}: {e}")
            print(f"Error output: {e.stderr}")

        # Clean up temporary mermaid file
        if os.path.exists(mermaid_file):
            os.remove(mermaid_file)

def convert_to_pdf(markdown_file, output_pdf, temp_dir):
    """
    Convert markdown to PDF using pandoc via HTML
    """
    # First convert to HTML
    html_file = os.path.join(temp_dir, 'document.html')

    cmd_html = [
        'pandoc',
        markdown_file,
        '-o', html_file,
        '--css', os.path.join(temp_dir, 'style.css'),
        '--standalone',
        '--toc',
        '--toc-depth=3',
        '--number-sections',
        '--highlight-style=tango',  # Changed from github to tango
        '--from=markdown',
        '--to=html5',
        '--resource-path', temp_dir  # Ensure images are found
    ]

    try:
        # Change to temp directory so relative image paths work
        subprocess.run(cmd_html, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"HTML created successfully: {html_file}")

        # Then convert HTML to PDF using wkhtmltopdf directly
        cmd_pdf = [
            'wkhtmltopdf',
            '--page-size', 'A4',
            '--margin-top', '20mm',
            '--margin-bottom', '20mm',
            '--margin-left', '20mm',
            '--margin-right', '20mm',
            '--enable-local-file-access',
            '--print-media-type',
            '--disable-smart-shrinking',  # Prevent image shrinking issues
            '--image-quality', '100',     # High quality images
            '--image-dpi', '300',         # High DPI for crisp images
            html_file,
            output_pdf
        ]

        subprocess.run(cmd_pdf, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"PDF created successfully: {output_pdf}")

    except subprocess.CalledProcessError as e:
        print(f"Error converting to PDF: {e}")
        print(f"Error output: {e.stderr}")

        # Fallback: try pandoc direct to PDF with different settings
        cmd_fallback = [
            'pandoc',
            markdown_file,
            '-o', output_pdf,
            '--standalone',
            '--from=markdown',
            '--to=pdf',
            '--variable=geometry:margin=2cm',
            '--variable=graphics=true',
            '--variable=links-as-notes=true',
            '--resource-path', temp_dir
        ]

        try:
            subprocess.run(cmd_fallback, check=True, capture_output=True, text=True, cwd=temp_dir)
            print(f"PDF created successfully with fallback method: {output_pdf}")
        except subprocess.CalledProcessError as e2:
            print(f"Error with fallback method: {e2}")
            print(f"Error output: {e2.stderr}")

def create_css_style(temp_dir):
    """
    Create CSS for better PDF formatting
    """
    css_content = """
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        margin: 40px;
        color: #333;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #2c3e50;
        margin-top: 30px;
        margin-bottom: 15px;
    }
    
    h1 {
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
    }
    
    h2 {
        border-bottom: 2px solid #95a5a6;
        padding-bottom: 8px;
    }
    
    table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
    }
    
    th, td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    
    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    code {
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }
    
    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        background-color: white;
        page-break-inside: avoid;
        clear: both;
    }

    .diagram-container {
        text-align: center;
        margin: 30px 0;
        page-break-inside: avoid;
        clear: both;
    }

    .diagram-container img {
        max-width: 95%;
        height: auto;
        margin: 10px auto;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    ul, ol {
        margin: 15px 0;
        padding-left: 30px;
    }
    
    li {
        margin: 5px 0;
    }
    
    blockquote {
        border-left: 4px solid #3498db;
        margin: 20px 0;
        padding: 10px 20px;
        background-color: #f8f9fa;
    }
    
    .page-break {
        page-break-before: always;
    }
    """
    
    css_file = os.path.join(temp_dir, 'style.css')
    with open(css_file, 'w') as f:
        f.write(css_content)

def verify_images(temp_dir, num_diagrams):
    """
    Verify that all diagram images were created successfully
    """
    missing_images = []
    for i in range(1, num_diagrams + 1):
        image_path = os.path.join(temp_dir, f'diagram_{i}.png')
        if not os.path.exists(image_path):
            missing_images.append(f'diagram_{i}.png')
        else:
            # Check if image file is not empty
            if os.path.getsize(image_path) == 0:
                missing_images.append(f'diagram_{i}.png (empty file)')

    if missing_images:
        print(f"Warning: Missing or empty images: {missing_images}")
    else:
        print(f"All {num_diagrams} diagram images created successfully")

    return len(missing_images) == 0

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Convert Markdown with Mermaid diagrams to PDF')
    parser.add_argument('input_file', nargs='?',
                       default='/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/ams_mep/_docs_ams_mep/22_LLD.md',
                       help='Input markdown file path')
    parser.add_argument('-o', '--output',
                       help='Output PDF file path (default: same name as input with .pdf extension)')

    args = parser.parse_args()
    input_file = args.input_file

    # Determine output file path
    if args.output:
        output_file = args.output
    else:
        # Generate output filename based on input filename
        input_path = Path(input_file)
        output_file = str(input_path.parent / f"{input_path.stem}.pdf")

    # Verify input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found!")
        sys.exit(1)

    print(f"Converting: {input_file}")
    print(f"Output: {output_file}")

    # Read the input markdown file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Working in temporary directory: {temp_dir}")

        # Extract mermaid diagrams and replace with image references
        modified_content, diagrams = extract_mermaid_diagrams(content)

        # Create mermaid images
        if diagrams:
            print(f"Found {len(diagrams)} mermaid diagrams")
            create_mermaid_images(diagrams, temp_dir)

            # Verify all images were created
            verify_images(temp_dir, len(diagrams))

        # Create CSS file
        create_css_style(temp_dir)

        # Write modified markdown to temporary file
        temp_md_file = os.path.join(temp_dir, 'document.md')
        with open(temp_md_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)

        # Debug: Print first few lines of modified content
        print("Modified markdown preview:")
        print(modified_content[:500] + "..." if len(modified_content) > 500 else modified_content)

        # Convert to PDF
        convert_to_pdf(temp_md_file, output_file, temp_dir)

    print(f"Conversion complete! Output: {output_file}")

if __name__ == '__main__':
    main()