#!/usr/bin/env python3
"""
python3 convert_md_to_pdf_improved.py
<PERSON><PERSON> to convert Markdown file with Mermaid diagrams to PDF
"""

import os
import re
import subprocess
import tempfile
import shutil
from pathlib import Path

def extract_mermaid_diagrams(content):
    """
    Extract mermaid diagrams from markdown content and replace with image references
    """
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    diagrams = []
    
    def replace_mermaid(match):
        diagram_content = match.group(1)
        diagram_id = len(diagrams)
        diagrams.append(diagram_content)
        return f'![Diagram {diagram_id + 1}](diagram_{diagram_id + 1}.png)'
    
    # Replace mermaid blocks with image references
    modified_content = re.sub(mermaid_pattern, replace_mermaid, content, flags=re.DOTALL)
    
    # Clean up problematic Unicode characters in code blocks
    # Replace tree characters with ASCII equivalents
    unicode_replacements = {
        '├──': '|--',
        '└──': '`--',
        '│': '|',
        '├': '|',
        '└': '`'
    }
    
    for unicode_char, ascii_replacement in unicode_replacements.items():
        modified_content = modified_content.replace(unicode_char, ascii_replacement)
    
    return modified_content, diagrams

def create_mermaid_images(diagrams, output_dir):
    """
    Create PNG images from mermaid diagram content
    """
    for i, diagram in enumerate(diagrams):
        # Create temporary mermaid file
        mermaid_file = os.path.join(output_dir, f'temp_diagram_{i + 1}.mmd')
        with open(mermaid_file, 'w') as f:
            f.write(diagram)
        
        # Convert to PNG
        output_image = os.path.join(output_dir, f'diagram_{i + 1}.png')
        cmd = ['mmdc', '-i', mermaid_file, '-o', output_image, '-w', '1200', '-H', '800']
        
        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"Created diagram_{i + 1}.png")
        except subprocess.CalledProcessError as e:
            print(f"Error creating diagram {i + 1}: {e}")
            print(f"Error output: {e.stderr}")
        
        # Clean up temporary mermaid file
        os.remove(mermaid_file)

def convert_to_pdf(markdown_file, output_pdf, temp_dir):
    """
    Convert markdown to PDF using pandoc via HTML
    """
    # First convert to HTML
    html_file = os.path.join(temp_dir, 'document.html')
    
    cmd_html = [
        'pandoc',
        markdown_file,
        '-o', html_file,
        '--css', os.path.join(temp_dir, 'style.css'),
        '--standalone',
        '--toc',
        '--toc-depth=3',
        '--number-sections',
        '--highlight-style=github',
        '--from=markdown',
        '--to=html5'
    ]
    
    try:
        # Change to temp directory so relative image paths work
        subprocess.run(cmd_html, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"HTML created successfully: {html_file}")
        
        # Then convert HTML to PDF using wkhtmltopdf directly
        cmd_pdf = [
            'wkhtmltopdf',
            '--page-size', 'A4',
            '--margin-top', '20mm',
            '--margin-bottom', '20mm',
            '--margin-left', '20mm',
            '--margin-right', '20mm',
            '--enable-local-file-access',
            '--print-media-type',
            html_file,
            output_pdf
        ]
        
        subprocess.run(cmd_pdf, check=True, capture_output=True, text=True, cwd=temp_dir)
        print(f"PDF created successfully: {output_pdf}")
        
    except subprocess.CalledProcessError as e:
        print(f"Error converting to PDF: {e}")
        print(f"Error output: {e.stderr}")
        
        # Fallback: try pandoc direct to PDF with different settings
        cmd_fallback = [
            'pandoc',
            markdown_file,
            '-o', output_pdf,
            '--standalone',
            '--from=markdown',
            '--to=pdf',
            '--variable=geometry:margin=2cm'
        ]
        
        try:
            subprocess.run(cmd_fallback, check=True, capture_output=True, text=True, cwd=temp_dir)
            print(f"PDF created successfully with fallback method: {output_pdf}")
        except subprocess.CalledProcessError as e2:
            print(f"Error with fallback method: {e2}")
            print(f"Error output: {e2.stderr}")

def create_css_style(temp_dir):
    """
    Create CSS for better PDF formatting
    """
    css_content = """
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        margin: 40px;
        color: #333;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #2c3e50;
        margin-top: 30px;
        margin-bottom: 15px;
    }
    
    h1 {
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
    }
    
    h2 {
        border-bottom: 2px solid #95a5a6;
        padding-bottom: 8px;
    }
    
    table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
    }
    
    th, td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    
    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    code {
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }
    
    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        background-color: white;
    }
    
    ul, ol {
        margin: 15px 0;
        padding-left: 30px;
    }
    
    li {
        margin: 5px 0;
    }
    
    blockquote {
        border-left: 4px solid #3498db;
        margin: 20px 0;
        padding: 10px 20px;
        background-color: #f8f9fa;
    }
    
    .page-break {
        page-break-before: always;
    }
    """
    
    css_file = os.path.join(temp_dir, 'style.css')
    with open(css_file, 'w') as f:
        f.write(css_content)

def main():
    input_file = '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/ams_mep/_docs_ams_mep/22_LLD.md'
    output_file = '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_ams/22_LLD.pdf'
    
    # Read the input markdown file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Working in temporary directory: {temp_dir}")
        
        # Extract mermaid diagrams and replace with image references
        modified_content, diagrams = extract_mermaid_diagrams(content)
        
        # Create mermaid images
        if diagrams:
            print(f"Found {len(diagrams)} mermaid diagrams")
            create_mermaid_images(diagrams, temp_dir)
        
        # Create CSS file
        create_css_style(temp_dir)
        
        # Write modified markdown to temporary file
        temp_md_file = os.path.join(temp_dir, 'document.md')
        with open(temp_md_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Convert to PDF
        convert_to_pdf(temp_md_file, output_file, temp_dir)
    
    print(f"Conversion complete! Output: {output_file}")

if __name__ == '__main__':
    main()