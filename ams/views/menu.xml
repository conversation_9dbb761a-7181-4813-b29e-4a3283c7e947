<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <menuitem id="ams_combined_dashboard_menu" name="AMS Dashboards" sequence="3"
                  web_icon="ams,static/description/images/dashboard.png"
                  groups="ams_base.ams_group_manager,base.group_system,ams_base.ams_group_user"/>
        <menuitem id="top_menu" name="Access Management" sequence="17"
                  web_icon="ams_base,static/description/icon.png"
                  groups="ams_base.ams_group_manager,base.group_system,ams_base.ams_group_user"/>

        <menuitem id="op_menu" name="Operations" sequence="40" parent="top_menu">
            <menuitem id="event_log_menu" name="Event Log" action="event_log_action" sequence="5025"/>
        </menuitem>

        <menuitem id="menu_hr_root" name="HR" sequence="30" parent="top_menu">
            <menuitem id="menu_hr_employee" name="Employees" action="ams_base.employee_action" sequence="10"/>
            <menuitem id="menu_hr_department" name="Departments" action="hr.hr_department_kanban_action" sequence="20"/>

        </menuitem>

        <menuitem id="device_config_menu" name="Devices" sequence="45" parent="top_menu">
            <menuitem id="device_group_menu" name="Device Group" action="device_group_action" sequence="10"/>
            <menuitem id="device_menu" name="Device" action="device_action" sequence="20"/>
            <menuitem id="user_group_menu" name="User Group" action="user_group_action" sequence="30"/>
            <menuitem id="user_menu" name="User" action="user_action" sequence="40"/>
        </menuitem>
        <menuitem id="config_menu" name="Configurations" sequence="50" parent="top_menu">
            <menuitem id="schedule_menu" name="Schedules" action="schedule_action" sequence="10"/>
            <menuitem id="time_unit_menu" name="Time Unit" action="time_unit_action" sequence="15"/>
            <menuitem id="door_group_menu" name="Door Groups" action="door_group_action" sequence="20"/>
            <menuitem id="door_menu" name="Doors" action="door_action" sequence="30"/>
            <menuitem id="access_level_menu" name="Access Levels" action="access_level_action" sequence="40"/>
            <menuitem id="access_group_menu" name="Access Groups" action="access_group_action" sequence="50"/>

            <menuitem id="card_type_menu" name="Card Type" action="card_type_action" sequence="60"/>
            <menuitem id="card_menu" name="Cards" action="card_action" sequence="70"/>

            <menuitem id="company_menu" name="Company" action="base.action_res_company_form" sequence="100"/>

        </menuitem>

        <menuitem id="event_log_menu" name="Event Log" parent="op_menu" action="event_log_action" sequence="5030"/>


    </data>
</odoo>