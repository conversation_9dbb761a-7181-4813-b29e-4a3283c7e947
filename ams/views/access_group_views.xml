<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: access_group_view_form-->
    <record id="access_group_view_form" model="ir.ui.view">
        <field name="name">ams.access_group.form</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='basic_info']" position="after">
                <page name="group_users" string="Users">
                    <field name="group_users_ids">
                        <list editable="bottom">
                            <field name="user_id"/>
                            <field name="config_applied" optional="hide"/>
                            <field name="ac_group_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
            <xpath expr="//page[@name='group_users']" position="after">
                <page name="user_groups" string="User Groups">
                    <field name="user_groups_ids">
                        <list editable="bottom">
                            <field name="user_group_id"/>
                            <field name="config_applied" optional="hide"/>
                            <field name="ac_group_id" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
            <xpath expr="//group[@name='basic_info_full_width']" position="inside">
                <field name="ac_levels_ids" widget="many2many_tags"/>
                <field name="device_ids" widget="many2many_tags" groups="base.group_no_one"/>
                <field name="is_visitor_group"/>
                <field name="api_type"/>
                 <field name="api_type" readonly="0" groups="base.group_no_one"/>


            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_view_list-->
    <record id="access_group_view_list" model="ir.ui.view">
        <field name="name">ams.access_group.list</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="inside">
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_view_search-->
    <record id="access_group_view_search" model="ir.ui.view">
        <field name="name">ams.access_group.search</field>
        <field name="model">ams.access_group</field>
        <field name="inherit_id" ref="ams_base.api_model_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">

            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: access_group_action-->
    <record id="access_group_action" model="ir.actions.act_window">
        <field name="name">Access Groups</field>
        <field name="res_model">ams.access_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an access group
            </p>
            <p>
                Create access group
            </p>
        </field>
    </record>
</odoo>
