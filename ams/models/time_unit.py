# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class TimeUnit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams.time_unit"
    _inherit = ['ams_base.abstract_model', 'mail.thread', 'mail.activity.mixin']
    _description = 'Time Unit'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Name", required=True, tracking=True)
    start_time = fields.Float(string="Start Time", compute='_compute_start_time', store=True, readonly=False,
                              default=9.0, help="Start time of shift", tracking=True)
    end_time = fields.Float(string="End Time", compute='_compute_end_time', store=True, readonly=False,
                            default=17.0, help="End time of shift", tracking=True)
    start_limit = fields.Float(string="Start Limit", help="Max allowed checkin time when shift is flexible",
                               tracking=True)
    end_limit = fields.Float(string="End Limit", compute='_compute_end_limit', store=True,
                             help="Max allowed checkout time when Time is flexible")
    # TODO:rename unit_type
    unit_type = fields.Selection(selection=[('normal', 'Normal'), ('flexible', 'Flexible'), ('open', 'Open')],
                                 string="Unit Type", default='normal', tracking=True, required=True)
    duration = fields.Float(string="Duration", compute='_compute_duration', store=True, readonly=False,
                            help="Required time to complete Time Unit", tracking=True)
    is_overnight = fields.Boolean(string="Is Overnight", compute='_compute_is_overnight', store=True)
    color = fields.Char(string="Color")
    apply_min_max = fields.Boolean(string="Apply Min Max", help="Apply Min Checkin Time & Max Checkout Time")
    absent_time_criteria = fields.Float(string="Absent Time Criteria",
                                        help="make employee absent if working time less than this time (hours:minutes)",
                                        default=0.0)
    purpose_type = fields.Selection(selection=[('ac', 'Access Control'), ('ta', 'TA'), ('all', 'All')], default='all')
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregio

    # region  Computed
    min_checkin_time = fields.Float(string="Min Check-in Time", compute='_compute_min_max_time', store=True,
                                    readonly=False, help="Minimum time allowed to checkin before start of Time")
    max_checkout_time = fields.Float(string="Max Checkout Time", compute='_compute_min_max_time', store=True,
                                     readonly=False, help="Maximum time allowed to checkout after end of Time")

    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('start_time', 'end_time', 'unit_type')
    def _compute_duration(self):
        for rec in self:
            if rec.unit_type == 'normal':
                if rec.start_time > rec.end_time:
                    rec.duration = rec.end_time + (24 - rec.start_time)
                else:
                    rec.duration = rec.end_time - rec.start_time

    @api.depends('start_time', 'duration', 'unit_type')
    def _compute_end_time(self):
        for rec in self:
            if rec.unit_type in ['normal', 'flexible']:
                end_time = rec.start_time + rec.duration
                if 0 <= end_time <= 23.59:
                    rec.end_time = end_time
                else:
                    rec.end_time = end_time - 24
            elif rec.unit_type == 'open':
                rec.end_time = 23.99

    @api.depends('unit_type')
    def _compute_start_time(self):
        for rec in self:
            if rec.unit_type == 'open':
                rec.start_time = 0.0

    @api.depends('start_time', 'start_time', 'duration', 'unit_type')
    def _compute_end_limit(self):
        for rec in self:
            if rec.unit_type == 'flexible':
                end_time_limit = rec.start_limit + rec.duration
                if 0 <= end_time_limit <= 23.59:
                    rec.end_limit = end_time_limit
                else:
                    rec.end_limit = end_time_limit - 24

    @api.depends('start_time', 'end_time', 'start_limit', 'end_limit', 'unit_type')
    def _compute_is_overnight(self):
        for rec in self:
            if rec.unit_type == 'normal':
                rec.is_overnight = rec.start_time >= rec.end_time
            elif rec.unit_type == 'flexible':
                rec.is_overnight = rec.start_time >= rec.end_time or rec.start_limit >= rec.end_limit or rec.start_time > rec.start_limit
            elif rec.unit_type == 'open':
                rec.is_overnight = False

    @api.depends('apply_min_max', 'unit_type', 'start_time', 'end_time', 'end_limit')
    def _compute_min_max_time(self):
        """
        Compute min_checkin_time and max_checkout_time based on shift type
        when apply_min_max is not enabled.
        """
        for rec in self:
            if rec.apply_min_max:
                continue

            # Default values based on shift type
            if rec.unit_type == 'normal':
                rec.min_checkin_time = rec.start_time
                rec.max_checkout_time = rec.end_time
            elif rec.unit_type == 'flexible':
                rec.min_checkin_time = rec.start_time
                rec.max_checkout_time = rec.end_limit
            elif rec.unit_type == 'open':
                rec.min_checkin_time = 0.0
                rec.max_checkout_time = 23.99

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    @api.constrains('apply_min_max', 'unit_type', 'min_checkin_time', 'max_checkout_time')
    def _check_min_max_time(self):
        """
        Validate the min_checkin_time and max_checkout_time for shift records.
        Ensures times are within valid ranges and follow rules based on shift type.
        """
        for rec in self:

            # Skip validation if apply_min_max is not enabled
            if not rec.apply_min_max:
                continue

            # Define end time based on shift type
            end_time = rec.end_limit if rec.unit_type == 'flexible' else rec.end_time

            # Validate min_checkin_time and max_checkout_time against start and end times
            if rec.min_checkin_time > rec.start_time:
                raise ValidationError("Min Checkin Time must be less than or equal to Start Time.")
            if rec.max_checkout_time < end_time:
                raise ValidationError(
                    "Max Checkout Time must be greater than or equal to End Time." if rec.unit_type == 'normal' else "Max Checkout Time must be greater than or equal to End Limit.")

    @api.onchange('unit_type')
    def _onchange_shift_type(self):
        """
        Handle logic when the shift type is changed.
        If the shift type is 'open', disable the apply_min_max flag.
        """
        if self.unit_type == 'open':
            self.apply_min_max = False

    @api.model
    def _get_time_fields(self):
        """Specify which float fields represent time values"""
        return [
            'start_time',
            'start_limit',
            'end_limit',
            'min_checkin_time',
            'max_checkout_time',
            'duration'
        ]

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
