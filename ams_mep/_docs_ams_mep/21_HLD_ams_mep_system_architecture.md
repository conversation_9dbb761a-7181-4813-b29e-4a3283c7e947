# Visitor Management System (HLD)

## Ministry of Economy and Planning



- **Document Version:** 1.1
- **Date:** 2025-03-08
- **Author:** Software Architecture Team
- **Target Audience:** Stakeholders, Developers, System Integrators

---

## 1. Executive Summary

The AMS MEP (Access Management System - Ministry of Economy and Planning) is a comprehensive visitor management solution built on Odoo 18 framework. It provides secure visitor registration, invitation management, access control integration, and real-time monitoring capabilities specifically customized for government building requirements.

### 1.1 Key Features

- **Visitor Management**: Complete visitor lifecycle from registration to exit
- **QR Code-based Access Control**: Secure, contactless entry system
- **BioStar 2 Integration**: Hardware access control system integration
- **SAML Authentication**: Enterprise-grade authentication via auth_saml
- **Email Notifications**: Automated invitation and notification system
- **Security Controls**: Debug access restrictions and security monitoring
- **Multi-language Support**: Arabic and English interface

---

## 2. System Architecture Overview

### 2.1 Module Hierarchy and Dependencies

```mermaid
graph BT;
    hr[HR Module<br/>Odoo Core]
    base[Base Module<br/>Odoo Core]
    mail[Mail Module<br/>Odoo Core]
  
    ams_base[ams_base<br/>Foundation Module]
    ams[ams<br/>Core Access Management]
    ams_bs[ams_bs<br/>BioStar Integration]
    ams_vm[ams_vm<br/>Visitor Management]
    ams_mep[ams_mep<br/>MEP Customizations]
  
    auth_saml[auth_saml<br/>SAML Authentication<br/>OCA Module]
  
    ams_base --> hr
    ams_base --> mail
    ams_base --> base
    ams --> ams_base
    ams_bs --> ams
    ams_vm --> ams
    ams_mep --> ams_vm
    ams_mep --> ams_bs
    ams_mep -.-> auth_saml
  
    style ams_mep fill:#ff9999,stroke:#333,stroke-width:3px
    style auth_saml fill:#ff99,stroke:#333,stroke-width:2px
```

### 2.2 Core Components

| Component          | Purpose             | Key Responsibilities                                         |
| ------------------ | ------------------- | ------------------------------------------------------------ |
| **ams_base** | Foundation Layer    | Abstract models, common utilities, base API framework        |
| **ams**      | Core AMS            | Access control entities, user management, device abstraction |
| **ams_bs**   | BioStar Integration | Hardware API clients, device synchronization, event logging  |
| **ams_vm**   | Visitor Management  | Visitor registration, invitation workflow, visit tracking    |
| **ams_mep**  | MEP Customizations  | Government-specific UI/UX, security enhancements, reporting  |

---

## 3. Technology Stack

### 3.1 Core Technologies

- **Framework**: Odoo 18 Community Edition
- **Language**: Python 3.11+
- **Database**: PostgreSQL 15
- **Web Server**: Nginx with Proxy Manager
- **Containerization**: Docker & Docker Compose

### 3.2 Integration Technologies

- **Access Control**: BioStar 2 API (REST/JSON)
- **Authentication**: SAML 2.0 (pysaml2 library)
- **Email**: SMTP Integration
- **QR Codes**: Python QR Code generation
- **Reporting**: Odoo Report Engine (QWeb)

### 3.3 Security Technologies

- **Authentication**: (Forced SAML) |   (Odoo Portal) is option
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: TLS/SSL, Database encryption
- **Session Management**: Secure session handling
- **Audit Logging**: Comprehensive audit logging

---

## 4. System Architecture Diagrams

### Application Architecture

```mermaid
flowchart TB
    subgraph "Presentation Layer"
        web[Web Interface / Odoo Backend]
        dashboard[Dashboard & Analytics]
    end

    subgraph "Business Logic Layer"
        business_logic[AMS Modules: MEP, VM, BS, Core]
    end

    subgraph "Data Layer"
        models[Odoo ORM Models]
        db[(PostgreSQL Database)]
    end

    subgraph "Integration Layer"
        biostar[BioStar 2 REST API]
        saml[SAML Identity Provider]
        email[SMTP Email Service]
    end

    web --> business_logic
    dashboard --> business_logic
    business_logic --> models
    models --> db
    business_logic --> biostar
    business_logic -.-> saml
    business_logic --> email
```

---

## 5. Data Flow Architecture

### 5.1 Visitor Registration and Access Flow

```mermaid
sequenceDiagram
    participant E as Employee
    participant W as Web Interface
    participant VM as AMS VM Module
    participant MEP as AMS MEP Module
    participant BS as AMS BS Module
    participant Bio as BioStar 2
    participant Email as SMTP Server
    participant V as Visitor
    participant Device as Access Device

    E->>W: Create Invitation Request
    W->>MEP: Process Custom Fields
    MEP->>VM: Create Invitation Record
    VM->>VM: Generate QR Code
    VM->>Email: Send Invitation Email
    Email->>V: Deliver QR Code
  
    V->>Device: Present QR Code
    Device->>Bio: Validate Access
    Bio->>BS: Query Access Rights
    BS->>VM: Verify Invitation Status
    VM->>BS: Confirm Access
    BS->>Bio: Grant/Deny Access
    Bio->>Device: Open/Close Gate
    Device->>BS: Log Access Event
    BS->>VM: Create Visit Record
```

---

## 6. Integration Points

### 6.1 BioStar 2 Integration

- **API Type**: REST/JSON
- **Authentication**: Session-based tokens
- **Endpoints**: Users, Devices, Access Control, Events
- **Sync Frequency**: Real-time for events, scheduled for master data
- **Data Flow**: Push data from server to BioStar

### 6.2 SAML Authentication Integration

- **Provider**: OCA auth_saml module
- **Protocol**: SAML 2.0
- **Identity Provider**: Government LDAP/AD
- **User Mapping**: Email-based user matching
- **Session Management**: Integrated with Odoo session handling

### 6.3 Email Integration

- **Protocol**: SMTP
- **Templates**: QWeb-based email templates
- **Attachments**: QR codes, invitation badges
- **Localization**: Arabic and English support

---

## 7. Security Architecture

### 7.1 Authentication Layers

1. **SAML Authentication**: Primary authentication via government IdP
2. **Odoo Portal Authentication**: Secondary authentication for portal users (disabled)

### 7.2 Security Controls (MEP Module)

- **Debug Access Restriction**: Admin-only debug mode access
- **Rate Limiting**: Failed login attempt protection
- **Session Management**: Configurable timeout settings
- **Audit Logging**: Comprehensive security event tracking
- **Database Manager Restrictions**: Limited access to DB management

### 7.3 Data Protection

- **Encryption**: TLS/SSL for data in transit
- **Database Security**: PostgreSQL role-based access
- **Personal Data**: GDPR-compliant visitor data handling
- **Audit Trail**: Complete visitor activity logging

---

## 8. Performance and Scalability

### 8.1 System Specifications

- **Production VM1**: 8 cores, 16GB RAM, 250GB storage
- **Production VM2**: 8 cores, 16GB RAM, 500GB storage
- **Network**: Static IPs, dedicated VLAN
- **Load Balancing**: Nginx reverse proxy

### 8.2 Scalability Considerations

- **Horizontal Scaling**: Multi-instance Odoo deployment capability
- **Database Optimization**: Indexed fields, query optimization
- **Caching**: Redis integration for session and data caching
- **CDN**: Static asset delivery optimization

---

## 9. Monitoring and Maintenance

### 9.1 System Monitoring

- **Application Monitoring**: Odoo built-in logging
- **Database Monitoring**: PostgreSQL performance metrics
- **Infrastructure Monitoring**: Docker container health checks
- **Security Monitoring**: Failed login attempts, suspicious activities

### 9.2 Backup and Recovery

- **Database Backups**: Automated daily PostgreSQL dumps
- **Application Backups**: Docker volume snapshots
- **Configuration Backups**: Git-based configuration management
- **Recovery Procedures**: Documented disaster recovery plans

---

## 10. Development and Deployment

### 10.1 Development Workflow

- **Version Control**: Git with feature branch workflow
- **Code Quality**: Python linting, Odoo coding standards
- **Documentation**: Inline code documentation

### 10.2 Deployment Pipeline

- **Containerization**: Docker-based deployment
- **Environment Management**: Development, staging, production
- **Configuration Management**: Environment-specific configs
- **Rollback Procedures**: Quick rollback capabilities

---

## 11. Conclusion

The AMS MEP system provides a robust, secure, and scalable visitor management solution tailored for government requirements. The modular architecture ensures maintainability and extensibility while the comprehensive security framework addresses government-grade security needs.

The system successfully integrates multiple technologies and external systems to provide a seamless visitor experience while maintaining strict security controls and audit capabilities.

---

## 12. API Documentation

### 12.1 BioStar 2 API Integration

#### Authentication Endpoints

- **Login**: `POST /api/login`
- **Session Management**: Header-based `bs-session-id`
- **Token Refresh**: Automatic 1-hour token renewal

#### Core API Endpoints

| Endpoint               | Method   | Purpose                 | Module                           |
| ---------------------- | -------- | ----------------------- | -------------------------------- |
| `/api/users`         | GET/POST | User management         | ams_bs.biostar_user_api_client   |
| `/api/cards`         | GET/POST | User management         | ams_bs.biostar_user_api_client   |
| `/api/devices`       | GET      | Device information      | ams_bs.biostar_device_api_client |
| `/api/doors`         | GET      | Access control doors    | ams_bs.biostar_ac_api_client     |
| `/api/events`        | GET      | Access events           | ams_bs.biostar_device_api_client |
| `/api/access_groups` | GET      | Access group management | ams_bs.biostar_ac_api_client     |

### 12.2 Internal API Structure

#### Model Inheritance Hierarchy

```python
# Base Abstract Model
ams_base.abstract_model
├── ams_base.api_model
│   ├── ams.access_group
│   ├── ams.device
│   └── ams.ams_user
├── ams_vm.base_request
│   ├── ams_vm.invitation
│   └── ams_vm.visit
└── ams_vm.visitor
```

#### Key Model Relationships

- **Visitor** → **Invitation** (One-to-Many)
- **Invitation** → **Visit** (One-to-Many)
- **Visitor** → **AMS User** → **Card** (One-to-One chain)
- **Device** → **Door** → **Access Group** (Many-to-Many)

---

## 13. Glossary

| Term                   | Definition                                                          |
| ---------------------- | ------------------------------------------------------------------- |
| **AMS**          | Access Management System - Core system for managing physical access |
| **BioStar 2**    | Suprema's access control platform for biometric devices             |
| **MEP**          | Ministry of Economy and Planning - Government entity                |
| **QR Code**      | Quick Response code used for visitor identification                 |
| **SAML**         | Security Assertion Markup Language for authentication               |
| **IdP**          | Identity Provider - SAML authentication server                      |
| **ORM**          | Object-Relational Mapping - Odoo's database abstraction layer       |
| **API Client**   | Software component for communicating with external APIs             |
| **Sync Engine**  | Component responsible for data synchronization                      |
| **Visit Record** | Database record tracking a visitor's entry/exit                     |

---

**Document Control:**

- **Created**: 2025-03-08
- **Last Modified**: 2025-05-11
- **Review Cycle**: Quarterly
- **Approval**: Pending stakeholder review
- **Version**: 1.1
- **Classification**: Internal Use
- **Distribution**: Architecture Team, Development Team, Stakeholders
