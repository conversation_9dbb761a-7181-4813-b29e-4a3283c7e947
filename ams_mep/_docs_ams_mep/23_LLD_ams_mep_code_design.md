# Low-Level Design: AMS MEP Module Code Design

- **Document Version:** 1.0
- **Date:** 2024-05-22
- **Author:** AI Assistant
- **Target Audience:** Developers, Technical Leads

---

## 1. Introduction

This document outlines the low-level code design for the `ams_mep` module within the Access Management System (AMS) built on Odoo 18. The primary focus is on the module's structure, key classes, methods, and their interactions, specifically detailing how `ams_mep` extends and customizes the core `ams_vm` (Visitor Management) module. This document avoids specific implementation details, focusing instead on the design principles and architectural choices.

---

## 2. Module Overview and Dependencies

The `ams_mep` module is designed to provide specific customizations and enhancements required by the Ministry of Economy and Planning (MEP) on top of the existing `ams_vm` module. It inherits and extends functionalities from `ams_vm` and, indirectly, from `ams` and `ams_base`.

```mermaid
graph TD;
    ams_base[ams_base <br>Foundation Module]
    ams[ams <br>Core Access Management]
    ams_vm[ams_vm <br>Visitor Management]
    ams_mep[ams_mep <br>MEP Customizations]

    ams_base --> ams
    ams --> ams_vm
    ams_vm --> ams_mep

    style ams_mep fill:#ff9999,stroke:#333,stroke-width:3px
```

---

## 3. Core Data Models and Extensions

The `ams_mep` module primarily extends existing models from `ams_vm` to add MEP-specific fields, constraints, and business logic. The key models extended are `Invitation` and `Visitor`.

### 3.1. Invitation Model (`ams_vm.invitation` extension)

The `ams_mep` module extends the `ams_vm.invitation` model to incorporate MEP-specific requirements for invitation management. This includes additional fields for enhanced validation, approval workflows, and specific data points relevant to government visitor protocols.

**Key Extensions:**

*   **Fields:**
    *   `mep_approval_status`: (Selection, 'Pending', 'Approved', 'Rejected') - Tracks the MEP-specific approval state.
    *   `mep_risk_score`: (Integer) - Calculated risk score for the invitation, influencing approval.
    *   `mep_reason_for_visit`: (Char) - Specific reason for the visit as per MEP guidelines.
    *   `mep_required_documents_attached`: (Boolean) - Indicates if all required documents for MEP are attached.

*   **Methods (Overridden or New):**
    *   `_compute_mep_approval_status()`: Computes the MEP-specific approval status based on `mep_risk_score` and other criteria.
    *   `action_mep_submit_for_approval()`: Initiates the MEP-specific approval workflow, potentially triggering notifications or tasks.
    *   `_mep_validate_invitation_data()`: Adds MEP-specific validation rules, such as checking for mandatory fields or document attachments.
    *   `_mep_calculate_risk_score()`: Calculates a risk score for the invitation based on visitor type, visit purpose, and duration.

### 3.2. Visitor Model (`ams_vm.visitor` extension)

Extensions to the `ams_vm.visitor` model in `ams_mep` focus on additional visitor attributes and validation rules pertinent to MEP's visitor management policies.

**Key Extensions:**

*   **Fields:**
    *   `mep_id_type`: (Selection, 'National ID', 'Passport', 'Driving License') - Type of identification provided by the visitor.
    *   `mep_id_number`: (Char) - The identification number of the visitor.
    *   `mep_nationality`: (Many2one to `res.country`) - Visitor's nationality, important for specific regulations.
    *   `mep_is_vip`: (Boolean) - Flag to identify VIP visitors, potentially affecting access rules.

*   **Methods (Overridden or New):**
    *   `_mep_validate_visitor_identity()`: Validates the visitor's identity against MEP-specific rules, potentially including format checks or external lookups.
    *   `_mep_assign_visitor_category()`: Assigns a category (e.g., 'Official', 'Contractor', 'General Public') to the visitor based on their profile and visit purpose.
    *   `_mep_check_blacklist()`: Checks if the visitor is on a MEP-specific blacklist.

---

## 4. Business Logic and Workflows

The `ams_mep` module introduces specific business logic primarily through method overrides and new methods on the extended models, as well as through dedicated wizard models for complex processes.

### 4.1. Invitation Lifecycle Workflow (MEP Specific)

This workflow details the stages an invitation goes through, with MEP-specific additions for approval and validation.

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Draft
    Draft --> Submitted: Submit for Approval
    Submitted --> Approved: MEP Approval
    Submitted --> Rejected: MEP Rejection
    Approved --> Confirmed: Visitor Confirms
    Confirmed --> Active: Visit Date Reached
    Active --> Completed: Visitor Exits
    Active --> Cancelled: Invitation Cancelled
    Rejected --> Cancelled: Rejected
    Cancelled --> [*]
    Completed --> [*]

    state MEP_Specific_Validations <<choice>>
    Submitted --> MEP_Specific_Validations
    MEP_Specific_Validations --> Approved: All Validations Pass
    MEP_Specific_Validations --> Rejected: Validation Fails
```

**Key Process Steps:**

1.  **Invitation Creation**: Initial data entry, potentially with MEP-specific default values or constraints.
2.  **MEP-Specific Validations**: Before or during submission, additional checks are performed (e.g., against blacklists, specific ID formats, or required fields for MEP).
3.  **Approval Decision Logic**: A custom logic, potentially involving a risk scoring algorithm, determines if an invitation requires approval and if it's approved or rejected. This logic considers factors like visitor type, visit purpose, and duration.

### 4.2. Visitor Creation Workflow (MEP Specific)

This workflow outlines the process of creating a visitor record, including MEP-specific data capture and validation.

```mermaid
sequenceDiagram
    participant U as User
    participant W as Web Interface
    participant MEP as AMS MEP Module
    participant VM as AMS VM Module
    participant DB as Database

    U->>W: Initiate Visitor Creation
    W->>MEP: Provide Visitor Data (incl. MEP fields)
    MEP->>MEP: _mep_validate_visitor_identity()
    alt Validation Successful
        MEP->>VM: Create/Update Visitor Record
        VM->>DB: Save Visitor Data
        DB-->>VM: Confirmation
        VM-->>MEP: Confirmation
        MEP-->>W: Success
    else Validation Failed
        MEP-->>W: Error: Invalid MEP Data
    end
```

**Handling Multiple Visitors:**

*   **Main Visitor**: The primary contact for an invitation.
*   **Additional Visitors**: Associated with the main visitor, sharing some invitation details but potentially having individual MEP-specific attributes or validation requirements.

---

## 5. API Integration Design (`ams_bs` Module)

The `ams_mep` module leverages the `ams_bs` module for integration with BioStar 2 access control systems. The `ams_bs` module provides a structured way to interact with the BioStar 2 API without exposing the underlying complexities to `ams_mep`.

### 5.1. `ams_bs` API Overview

The `ams_bs` module acts as an abstraction layer for the BioStar 2 REST API. It encapsulates the logic for authentication, request handling, error management, and data serialization/deserialization. `ams_mep` interacts with `ams_bs` through well-defined Python methods, not directly with HTTP requests.

**Key Components within `ams_bs` (Conceptual):**

*   **API Clients**: Dedicated classes for different BioStar 2 API domains (e.g., `BiostarUserAPIClient`, `BiostarACAPIClient`, `BiostarDeviceAPIClient`). These clients handle specific endpoints.
*   **Data Transfer Objects (DTOs)**: Python classes representing the structure of data exchanged with BioStar 2 (e.g., `UserResponse`, `Door`, `Event`). These ensure type safety and facilitate data manipulation.
*   **Authentication Manager**: Manages session tokens, login, and token refresh mechanisms.

### 5.2. Interaction Pattern from `ams_mep` to `ams_bs`

`ams_mep` will call methods exposed by `ams_bs` to perform operations related to access control, user synchronization, and event logging. For example, when an invitation is approved, `ams_mep` might trigger `ams_bs` to create or update a user in BioStar 2 and assign them to an access group.

```mermaid
sequenceDiagram
    participant MEP as AMS MEP Module
    participant VM as AMS VM Module
    participant BS as AMS BS Module
    participant Bio as BioStar 2 API

    MEP->>VM: Invitation Approved
    VM->>BS: create_biostar_user(visitor_data)
    BS->>BS: Authenticate with BioStar
    BS->>Bio: POST /api/users
    Bio-->>BS: User Created Response
    BS->>BS: create_access_group_assignment(user_id, access_group_id)
    BS->>Bio: POST /api/access_groups/assign_users
    Bio-->>BS: Assignment Response
    BS-->>VM: Success/Error
    VM-->>MEP: Success/Error
```

**Example `ams_bs` API Calls (Conceptual):**

*   `ams_bs.biostar_user_api_client.create_user(user_data)`: Creates a new user in BioStar 2.
*   `ams_bs.biostar_ac_api_client.assign_user_to_access_group(user_id, group_id)`: Assigns a user to a specific access group.
*   `ams_bs.biostar_device_api_client.get_device_status(device_id)`: Retrieves the status of a BioStar device.

---

## 6. User Interface (UI) Extensions

`ams_mep` will extend Odoo's standard views (forms, trees, searches) for `Invitation` and `Visitor` models to include the new MEP-specific fields and actions. This involves modifying existing XML views or creating new ones that inherit from the base `ams_vm` views.

**Key UI Elements:**

*   **Invitation Form View**: Addition of MEP-specific fields, potentially new buttons for MEP approval workflows.
*   **Visitor Form View**: Inclusion of MEP-specific identity fields and related information.
*   **Reporting**: Custom reports or dashboards for MEP-specific visitor statistics and compliance.

---

## 7. Conclusion

This Low-Level Design document for the `ams_mep` module details its architectural approach to extending the `ams_vm` module. By focusing on model extensions, specific business logic, and clear API interactions with `ams_bs`, the `ams_mep` module provides the necessary customizations for the Ministry of Economy and Planning's visitor management requirements while maintaining a clean separation of concerns and leveraging the existing AMS framework.