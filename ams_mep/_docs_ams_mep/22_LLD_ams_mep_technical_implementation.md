# Technical Architecture Document

## AMS MEP - System Design and Invitation Workflow

- **Document Version:** 2.0
- **Date:** 2025-01-15
- **Author:** Technical Architecture Team
- **Target Audience:** Solution Architects, Technical Leads, System Designers

---

## 1. Executive Summary

This Technical Architecture document provides a high-level design overview of the AMS MEP (Access Management System - Ministry of Economy and Planning) module, focusing on system architecture, class relationships, and the invitation lifecycle workflow. It serves as a blueprint for understanding the system's structure and core business processes.

### 1.1 Document Scope

- **Primary Focus**: `ams_mep` module architecture and invitation workflow
- **Dependencies**: `ams_vm`, `ams_bs`, `ams_base`, `ams` module relationships
- **Integration Points**: BioStar 2 API, SAML authentication, Email services
- **Core Workflow**: Invitation lifecycle from creation to visitor access

---

## 2. System Architecture Overview

### 2.1 Module Dependencies and Inheritance Structure

```mermaid
classDiagram
    class BaseAbstractModel {
        <<abstract>>
        +company_id
        +_get_tz()
        +call_model_method_safely()
        +send_auto_refresh()
    }
    
    class BaseAPIModel {
        <<abstract>>
        +name
        +synced
        +last_sync_date
        +action_sync()
        +action_unsync()
    }
    
    class BaseRequest {
        <<abstract>>
        +state
        +status
        +start_date
        +end_date
        +action_approve()
        +action_reject()
    }
    
    class AccessGroup {
        +is_visitor_group
        +access_level_ids
        +user_ids
    }
    
    class Visitor {
        +partner_id
        +national_id
        +organization
        +create()
    }
    
    class Invitation {
        +visitor_id
        +qr_code
        +need_approval
        +search_visitors()
        +add_visitor()
        +approve_visitor()
    }
    
    class InvitationVisitor {
        +visitor_id_number
        +visit_duration_display
        +badge_report_action()
    }
    
    BaseAbstractModel <|-- BaseAPIModel
    BaseAbstractModel <|-- BaseRequest
    BaseAPIModel <|-- AccessGroup
    BaseAbstractModel <|-- Visitor
    BaseRequest <|-- Invitation
    BaseAbstractModel <|-- InvitationVisitor
    
    Invitation --> Visitor : visitor_id
    Invitation --> AccessGroup : access_groups_ids
    InvitationVisitor --> Invitation : invitation_id
```

### 2.2 Core Module Dependencies

#### 2.2.1 ams_base Module
- **BaseAbstractModel**: Foundation abstract class providing common functionality
  - Core methods: timezone handling, safe method calls, auto-refresh
- **BaseAPIModel**: API synchronization capabilities
  - Core methods: sync/unsync operations, error handling

#### 2.2.2 ams Module
- **AccessGroup**: Manages visitor and employee access permissions
- **AccessLevel**: Defines door schedules and access hierarchies
- **AMSUser**: User management with employee/visitor types
- **Card**: Physical access card management

#### 2.2.3 ams_vm Module
- **BaseRequest**: Abstract workflow model for approval processes
- **Visitor**: Visitor information management
- **Invitation**: Core invitation workflow model

### 2.3 AMS MEP Module Extensions

#### 2.3.1 MEP-Specific Invitation Model

**Core Functionality:**
- Extends `ams_vm.invitation` with MEP-specific requirements
- Manages visitor ID number validation
- Handles information confirmation workflow

**Key Methods:**
- `search_visitors()`: Advanced visitor search with MEP criteria
- `add_visitor()`: MEP-specific visitor addition logic
- `approve_visitor()`: MEP approval workflow
- `_check_information_confirmed()`: Information validation

#### 2.3.2 MEP Visitor Extensions

**Core Functionality:**
- Extends `ams_vm.visitor` with MEP validation rules
- Implements national/resident ID validation
- Handles invitation-visitor relationships

**Key Methods:**
- `create()`: Enhanced visitor creation with MEP constraints
- `_check_id_number_digits_and_length()`: ID validation

#### 2.3.3 Invitation-Visitor Relationship

**Core Functionality:**
- Manages many-to-many relationship between invitations and visitors
- Provides visit duration calculations
- Handles badge report generation

**Key Methods:**
- `badge_report_action()`: Generate visitor badges
- `visit_duration_display`: Computed visit duration

---

## 3. Invitation Lifecycle Workflow

### 3.1 Workflow Overview

The invitation workflow in AMS MEP follows a structured process from creation to visitor access, incorporating MEP-specific validation and approval mechanisms.

```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Pending : Submit Request
    Pending --> Approved : Auto/Manual Approval
    Pending --> Rejected : Rejection
    Approved --> Running : Visitor Arrives
    Running --> Finished : Visitor Exits
    Rejected --> Draft : Reset
    Finished --> [*]
    
    state Pending {
        [*] --> RiskAssessment
        RiskAssessment --> AutoApproval : Low Risk
        RiskAssessment --> ManualApproval : High Risk
        AutoApproval --> [*]
        ManualApproval --> [*]
    }
```

### 3.2 Invitation Creation Process

#### 3.2.1 Initial Request
1. **Host User** creates invitation request
2. **Visitor Information** validation (MEP-specific ID rules)
3. **Access Groups** assignment based on visit purpose
4. **Risk Assessment** calculation
5. **QR Code** generation for approved invitations

#### 3.2.2 MEP-Specific Validations
- **ID Number Validation**: National/Resident ID must be 10+ digits
- **Information Confirmation**: Host must confirm data accuracy
- **Organization Verification**: External organizations trigger higher risk scores

### 3.3 Approval Decision Logic

#### 3.3.1 Risk Scoring Algorithm

**Risk Factors:**
- Visitor organization type (+2 for external contractors)
- Visit duration (+1 for >4hrs, +2 for >8hrs)
- Access level requested (+3 for high-security areas)
- Multiple visitors (+1 per additional visitor)
- After-hours visits (+2 for outside 7AM-6PM)

**Decision Matrix:**
- Risk Score ≥ 4: **Manual Approval Required**
- Risk Score ≥ 2 + Non-Manager: **Manual Approval Required**
- Risk Score < 2: **Auto-Approval**

### 3.4 Visitor Management Process

#### 3.4.1 Visitor Creation Workflow

```mermaid
sequenceDiagram
    participant Host as Host User
    participant System as AMS MEP
    participant Visitor as Visitor Model
    participant Invitation as Invitation Model
    participant Validation as MEP Validator
    
    Host->>System: Create Invitation
    System->>Host: Request Visitor ID
    Host->>System: Enter Visitor ID Number
    System->>Validation: Validate ID Format
    
    alt ID Valid Format
        Validation->>System: ID Format OK
        System->>Visitor: Search Existing Visitor
        
        alt Visitor Exists
            Visitor->>System: Return Visitor Record
            System->>Invitation: Link Existing Visitor
        else Visitor Not Found
            System->>Host: Show Create Visitor Form
            Host->>Visitor: Create New Visitor
            Visitor->>Validation: Apply MEP Constraints
            Validation->>Visitor: Validate & Save
            Visitor->>Invitation: Link New Visitor
        end
    else ID Invalid Format
        Validation->>System: Validation Error
        System->>Host: Show Error Message
    end
    
    System->>Host: Confirm Information
    Host->>System: Confirm Data Accuracy
    System->>Invitation: Submit for Approval
```

#### 3.4.2 Multiple Visitor Handling

**Main Visitor:**
- Directly linked to invitation via `visitor_id` field
- Primary contact for the visit
- Required for invitation approval

**Additional Visitors:**
- Managed through `invitation_visitor` relationship
- Each additional visitor creates separate record
- Inherits access permissions from main invitation

### 3.5 QR Code and Access Management

#### 3.5.1 QR Code Generation
- **Format**: 12-digit numeric code
- **Uniqueness**: System-wide unique validation
- **Generation**: Automatic on invitation approval
- **Distribution**: Email integration with QR image

#### 3.5.2 Access Control Integration
- **BioStar 2 API**: Synchronization with physical access system
- **Access Groups**: MEP-specific visitor access levels
- **Time-based Access**: Automatic activation/deactivation
- **Audit Trail**: Complete access log tracking

---

## 4. Security and Configuration

### 4.1 MEP Security Features

#### 4.1.1 Access Control
- **Debug Restrictions**: Admin-only debug access
- **Database Manager**: Restricted database management
- **Rate Limiting**: Failed login attempt protection
- **Session Management**: Configurable timeout (default: 8 hours)
- **User Creation**: Admin-restricted user creation

#### 4.1.2 Security Configuration
- **Configurable Parameters**: All security settings via system parameters
- **Runtime Configuration**: Dynamic security policy updates
- **Audit Logging**: Security event tracking
- **Compliance**: Government security standard adherence

### 4.2 Integration Architecture

#### 4.2.1 BioStar 2 API Integration
- **Authentication**: Session-based API authentication
- **Auto-refresh**: Automatic token renewal
- **Error Handling**: Comprehensive error management
- **Synchronization**: Real-time access control sync

#### 4.2.2 SAML Authentication
- **Government IdP**: Integration with government identity provider
- **User Provisioning**: Automatic user creation from SAML attributes
- **Session Management**: Secure session handling
- **Attribute Mapping**: SAML to Odoo user attribute mapping

---

## 4. BioStar 2 API Integration Architecture

### 4.1 API Client Architecture Overview

The MEP system integrates with BioStar 2 access control system through a layered API client architecture:

#### 4.1.1 Base API Client
- **Authentication Management**: Automatic token refresh and session management
- **Configuration Properties**: Dynamic configuration from company settings
- **Error Handling**: Comprehensive exception handling and logging
- **Core Methods**: `login()`, `get_default_headers()`, `get_response()`

#### 4.1.2 Specialized API Clients

**Access Control Client (`BiostarACAPIClient`)**:
- Door management operations
- Schedule configuration
- Access group management
- Core Methods: `get_ac_doors()`, `get_ac_schedules()`, `create_access_group()`

**User Management Client (`BiostarUserAPIClient`)**:
- User CRUD operations
- Group assignment management
- User synchronization
- Core Methods: `get_users()`, `create_user()`, `update_user()`, `delete_user()`

**Device Management Client (`BiostarDeviceAPIClient`)**:
- Device monitoring and control
- Event retrieval and processing
- Real-time device communication
- Core Methods: `get_devices()`, `get_events()`, `control_device()`

### 4.2 Data Transfer Objects (DTOs)

Structured response handling through dataclasses:
- **Response Models**: `UserResponse`, `DoorsResponse`, `DevicesResponse`, `EventsResponse`
- **Entity Models**: `User`, `Door`, `Device`, `Event`
- **Error Handling**: Standardized error response structure

---

## 5. Security Architecture

### 5.1 Enhanced Security Controller

The MEP security controller provides comprehensive protection through multiple layers:

#### 5.1.1 Security Features

**IP Blocking and Rate Limiting**:
- Failed attempt tracking with configurable thresholds
- Automatic IP blocking with time-based expiration
- In-memory cache for performance optimization
- Core Methods: `_record_failed_attempt()`, `_is_ip_blocked()`

**Suspicious Pattern Detection**:
- SQL injection pattern detection
- Path traversal attack prevention
- Script injection monitoring
- Core Methods: `_detect_suspicious_patterns()`

**Access Control Enforcement**:
- Debug mode restrictions for admin users only
- Database manager access control
- Route-level security validation
- Core Routes: `/web/debug`, `/web/database/manager`, `/web/security/status`

**Security Event Logging**:
- Comprehensive audit trail
- Real-time security event capture
- Severity-based event classification
- Core Methods: `_log_security_event()`

### 5.2 Security Logging Architecture

#### 5.2.1 Security Event Model (`ams.security_log`)

**Event Types**:
- Authentication events (login success/failure, logout)
- Access control events (debug access, database manager access)
- Security violations (blocked attempts, suspicious patterns)
- Administrative events (user creation/deletion, permission changes)

**Event Classification**:
- **Info**: Normal operations (successful logins, user creation)
- **Warning**: Potential issues (failed logins, session timeouts)
- **Error**: Security violations (unauthorized access attempts)
- **Critical**: Serious threats (suspicious pattern detection)

**Access Control**: Restricted to system administrators only
```

---

## 6. Critical Workflow Sequence Diagrams

### 6.1 SAML Authentication Integration Flow

#### 6.1.1 SAML Authentication Sequence

```mermaid
sequenceDiagram
    participant User as Government Employee
    participant Browser as Web Browser
    participant Odoo as Odoo Application
    participant SAML as SAML Controller
    participant IdP as Government IdP
    participant LDAP as LDAP/Active Directory
    participant Session as Session Manager

    Note over User,Session: Phase 1: Initial Access Request
    User->>Browser: Access Odoo Portal
    Browser->>Odoo: GET /web
    Odoo->>SAML: Check authentication
    SAML->>SAML: No valid session found
    SAML->>Browser: Redirect to SAML SSO
    Browser->>IdP: GET /sso/login

    Note over User,Session: Phase 2: Identity Provider Authentication
    IdP->>Browser: Display login form
    Browser->>User: Show government login page
    User->>Browser: Enter credentials
    Browser->>IdP: POST credentials
    IdP->>LDAP: Validate credentials
    LDAP-->>IdP: Return user attributes

    alt Authentication Success
        IdP->>IdP: Generate SAML assertion
        IdP->>Browser: POST SAML response to Odoo
        Browser->>SAML: POST /auth_saml/signin
    else Authentication Failed
        IdP->>Browser: Show error message
        Browser-->>User: Display authentication error
    end

    Note over User,Session: Phase 3: SAML Response Processing
    SAML->>SAML: Validate SAML assertion
    SAML->>SAML: Extract user attributes (email, name, groups)
    SAML->>Odoo: Search for existing user by email

    alt User Exists
        Odoo-->>SAML: Return user record
        SAML->>Session: Create authenticated session
    else User Not Found
        SAML->>Odoo: Create new user with SAML attributes
        Odoo-->>SAML: Return new user record
        SAML->>Session: Create authenticated session
    end

    Note over User,Session: Phase 4: Session Establishment
    Session->>Session: Set session timeout (8 hours default)
    Session->>Browser: Set session cookies
    SAML->>Browser: Redirect to /web
    Browser->>Odoo: GET /web (with session)
    Odoo->>Browser: Display Odoo interface
    Browser-->>User: Show authenticated portal
```

### 6.2 QR Code Generation and Validation

#### 6.2.1 QR Code Lifecycle Management

```mermaid
sequenceDiagram
    participant Invitation as Invitation Model
    participant QR_Gen as QR Code Generator
    participant Visitor as Visitor Model
    participant Email as Email Service
    participant Device as Access Device
    participant Validator as QR Validator
    participant Visit as Visit Model

    Note over Invitation,Visit: Phase 1: QR Code Generation
    Invitation->>QR_Gen: Generate unique QR code
    QR_Gen->>QR_Gen: Create 12-digit numeric code
    QR_Gen->>QR_Gen: Check uniqueness in database

    loop Until Unique Code Found
        QR_Gen->>QR_Gen: Generate new code
        QR_Gen->>Invitation: Check if code exists
        Invitation-->>QR_Gen: Return existence status
    end

    QR_Gen-->>Invitation: Return unique QR code
    Invitation->>Invitation: Store QR code in qr_code field

    Note over Invitation,Visit: Phase 2: QR Code Distribution
    Invitation->>Email: Prepare invitation email
    Email->>Email: Generate QR code image
    Email->>Email: Embed QR code in email template
    Email->>Email: Send email to visitor
    Email-->>Invitation: Confirm email delivery

    Note over Invitation,Visit: Phase 3: QR Code Validation at Access Point
    Device->>Device: Scan QR code from visitor
    Device->>Validator: Send QR code for validation
    Validator->>Invitation: Search by QR code

    alt QR Code Found
        Invitation-->>Validator: Return invitation record
        Validator->>Validator: Check invitation status
        Validator->>Validator: Check date/time validity
        Validator->>Visitor: Check visitor status

        alt All Validations Pass
            Validator->>Visit: Create visit record
            Visit->>Visit: Set entry timestamp
            Validator-->>Device: Grant access
            Device->>Device: Open gate/door
        else Validation Failed
            Validator-->>Device: Deny access
            Device->>Device: Log denied attempt
        end
    else QR Code Not Found
        Validator-->>Device: Invalid QR code
        Device->>Device: Log invalid attempt
    end

    Note over Invitation,Visit: Phase 4: Visit Tracking
    Device->>Device: Monitor for exit scan
    Device->>Validator: Process exit QR scan
    Validator->>Visit: Find active visit record
    Visit->>Visit: Set exit timestamp
    Visit->>Visit: Calculate visit duration
    Visit-->>Validator: Confirm visit completion
```

---

## 7. Business Logic Architecture

### 7.1 Invitation Approval Decision Tree

#### 7.1.1 Risk-Based Approval Algorithm

The MEP system implements a sophisticated risk scoring algorithm to determine approval requirements:

**Risk Factors and Scoring**:
- **Visitor Organization** (0-2 points): External contractors, vendors, consultants
- **Visit Duration** (0-2 points): Extended visits (>4 hours = 1 point, >8 hours = 2 points)
- **Access Level** (0-3 points): High-security areas (Executive Floor, Server Room, Finance)
- **Multiple Visitors** (0-1 point): Group visits requiring additional oversight
- **After Hours** (0-2 points): Visits outside business hours (before 7 AM or after 6 PM)

**Decision Logic**:
- **High Risk** (≥4 points): Always requires approval
- **Medium Risk** (2-3 points): Requires approval unless requester is invitation manager
- **Low Risk** (<2 points): Auto-approved

**Approval Authority Hierarchy**:
1. **System Administrators**: Universal approval rights
2. **Invitation Managers**: Can approve all invitations
3. **Department Heads**: Can approve within their department
4. **Regular Users**: Cannot approve invitations

### 7.2 Access Control Validation Architecture

#### 7.2.1 Multi-Layer Validation Process

The system implements an 8-layer validation process for visitor access:

**Validation Layers**:
1. **QR Code Validation**: Verify code exists and is valid
2. **Invitation Status**: Ensure invitation is approved
3. **Date/Time Validation**: Check visit timing constraints
4. **Device/Door Access**: Validate access group permissions
5. **Schedule Validation**: Verify time-based access rules
6. **Visitor Status**: Confirm visitor account is active
7. **Concurrent Visit**: Prevent multiple active visits
8. **Visit Record Creation**: Log successful access

**Core Methods**:
- `validate_visitor_access()`: Entry validation and visit creation
- `validate_exit_access()`: Exit validation and visit completion
- `_calculate_duration()`: Visit duration computation

**Return Values**: `(is_allowed: bool, reason: str, visit_id: int)`

---

## 8. Conclusion

### 8.1 Architecture Summary

The AMS MEP Technical Architecture provides a comprehensive framework for managing government visitor access through:

**Core Components**:
- **Modular Design**: Built on `ams_base`, `ams`, and `ams_vm` foundations with MEP-specific extensions
- **Security-First Approach**: Multi-layer validation, risk-based approval, and comprehensive audit logging
- **Integration Architecture**: Seamless SAML authentication and BioStar 2 API integration
- **Workflow Management**: Automated invitation lifecycle with approval workflows

**Key Architectural Strengths**:
1. **Scalability**: Modular design supports future enhancements
2. **Security**: Defense-in-depth approach with multiple validation layers
3. **Compliance**: Comprehensive audit trails and access controls
4. **Integration**: Standards-based authentication and access control systems
5. **User Experience**: Streamlined workflows with automated processes

### 8.2 Technical Benefits

**For Government Operations**:
- Automated visitor management reducing manual overhead
- Enhanced security through risk-based approval algorithms
- Complete audit trails for compliance requirements
- Integration with existing government identity systems

**For System Administrators**:
- Centralized configuration and monitoring
- Comprehensive security event logging
- Flexible access control management
- Automated QR code generation and validation

**For End Users**:
- Simplified invitation creation process
- Automated approval workflows
- Real-time status tracking
- Mobile-friendly QR code access

### 8.3 Future Considerations

The architecture is designed to accommodate:
- Additional authentication providers
- Enhanced biometric integration
- Advanced analytics and reporting
- Mobile application development
- API extensions for third-party integrations

---

*This document serves as the technical architecture reference for the AMS MEP module, focusing on system design, workflow management, and integration patterns rather than implementation details.*
