/** @odoo-module **/

import {Component, useRef} from "@odoo/owl";
import {registry} from "@web/core/registry";
import {DashboardItemCount} from "./dashboard_item_count";
import {_t} from "@web/core/l10n/translation";


class AMSDashboard extends Component {
    static template = "ams.AwesomeDashboard";
    static components = {DashboardItemCount};

    setup() {
        this.upComingSelect = useRef("upComingSelect");
        console.log("dashboard >> setup >> upComingSelect:", this.upComingSelect);

    }


    get getTranslatedButtonLabel() {
        return _t("Show More")
    }

    get translateRequestDashboardTitle() {
        return _t("Upcoming Invitations:");
    }

    // get translateVisitDashboardTitle() {
    //     return _t("Today Visits");
    // }


    // getDateDomain() {
    //     const currentDate = this.currentDate || new Date().toISOString().split('T')[0];
    //     console.log("dashboard >> upComingSelect:", this.upComingSelect);
    //
    //     const upComingSelectValue = this.upComingSelect.el;
    //     console.log("dashboard >> upComingSelectValue:", this.upComingSelect.el);
    //
    //
    //     const calculateFutureDate = (daysToAdd, isMonth = false) => {
    //         const futureDate = new Date();
    //         if (isMonth) {
    //             futureDate.setMonth(futureDate.getMonth() + daysToAdd);
    //         } else {
    //             futureDate.setDate(futureDate.getDate() + daysToAdd);
    //         }
    //         return futureDate.toISOString().split('T')[0];
    //     };
    //
    //     switch (upComingSelectValue) {
    //         case 'today':
    //             return [['date', '=', currentDate]];
    //         case 'tomorrow':
    //             return [['date', '=', calculateFutureDate(1)]];
    //         case 'nextWeek':
    //             return [['date', '>=', currentDate], ['date', '<', calculateFutureDate(7)]];
    //         case 'nextMonth':
    //             return [['date', '>=', currentDate], ['date', '<', calculateFutureDate(1, true)]];
    //         case 'all':
    //             return [['date', '>=', currentDate]];
    //         default:
    //             return [];
    //     }
    // }


    getRequestDashboardItems() {
        // const dateDomain = this.getDateDomain();
        // console.log("dashboard >> dateDomain:", dateDomain);
        return [
            {
                label: _t("Pending Invitations"),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'pending']],
                icon: "fa fa-hourglass-half",
                chartIcon: "fa fa-pie-chart",
                color: "bg-primary",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Need Approval Invitations"),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'need_approval']],
                icon: "fa fa-clock-o",
                chartIcon: "fa fa-area-chart",
                color: "bg-warning",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Approved Invitations"),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'approved']],
                icon: "fa fa-thumbs-up",
                chartIcon: "fa fa-line-chart",
                color: "bg-success",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Confirmed Invitations"),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'confirmed']],
                icon: "fa fa-check-circle",
                chartIcon: "fa fa-pie-chart",
                color: "bg-info",
                views: [[false, "list"], [false, "form"]],
            },
            {
                label: _t("Rejected Invitations"),
                model: "ams_vm.invitation",
                domain: [['state', '=', 'rejected']],
                icon: "fa fa-thumbs-down",
                chartIcon: "fa fa-bar-chart",
                color: "bg-danger",
                views: [[false, "list"], [false, "form"]],
            }
        ];
    }

    // getVisitDashboardItems() {
    //     const currentDate = this.currentDate || new Date().toISOString().split('T')[0];
    //
    //     return [
    //         {
    //             label: _t("Pending Visits"),
    //             model: "ams_vm.visit",
    //             domain: [['state', '=', 'pending'], ['date', '=', currentDate]],
    //             icon: "fa fa-hourglass-half",
    //             chartIcon: "fa fa-bar-chart",
    //             color: "bg-dark",
    //             views: [[false, "list"], [false, "form"]],
    //         },
    //         {
    //             label: _t("Approved Visits"),
    //             model: "ams_vm.visit",
    //             domain: [['state', '=', 'approved'], ['date', '=', currentDate]],
    //             icon: "fa fa-thumbs-up",
    //             chartIcon: "fa fa-area-chart",
    //             color: "bg-info",
    //             views: [[false, "list"], [false, "form"]],
    //         },
    //         {
    //             label: _t("Confirmed Visits"),
    //             model: "ams_vm.visit",
    //             domain: [['state', '=', 'confirmed'], ['date', '=', currentDate]],
    //             icon: "fa fa-flag-checkered",
    //             chartIcon: "fa fa-pie-chart",
    //             color: "bg-primary",
    //             views: [[false, "list"], [false, "form"]],
    //         },
    //     ];
    // }

}


registry.category("actions").add("ams.dashboard", AMSDashboard);
