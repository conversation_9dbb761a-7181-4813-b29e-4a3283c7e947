<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ams.AwesomeDashboard">
        <!-- Scrollable container for the entire dashboard -->
        <div class="lp-dashboard_scrollable_container">
            <div class="container-fluid o_secy_container m-3">
                <!-- Section for Requests -->

                <h3 style="display: flex; align-items: center; white-space: nowrap; font-size: 24px; color: #000;">
                    <i class="fa fa-tachometer ms-2 text-primary mx-1"></i><t
                        t-esc="translateRequestDashboardTitle"/>
<!--                    <select t-ref="upComingSelect"  t-att-id="props.inputId" style="margin: 0 10px; padding: 4px 8px; font-size: 18px; width: auto; display: inline-block; border: 1px solid #ccc; border-radius: 4px;">-->
<!--                        <option value="today" selected="1">Today</option>-->
<!--                        <option value="tomorrow">Tomorrow</option>-->
<!--                        <option value="nextWeek">Next Week</option>-->
<!--                        <option value="nextMonth">Next Month</option>-->
<!--                        <option value="all">All</option>-->
<!--                    </select>-->
                </h3>

                <div class="row justify-content-start mx-2 mt-5">
                        <t t-foreach="getRequestDashboardItems()" t-as="requestItem" t-key="requestItem.label">
                            <DashboardItemCount
                                    size="16"
                                    label="requestItem.label"
                                    icon="requestItem.icon"
                                    chartIcon="requestItem.chartIcon"
                                    buttonLabel="this.getTranslatedButtonLabel"
                                    color="requestItem.color"
                                    model="requestItem.model"
                                    domain="requestItem.domain"
                                    views="requestItem.views"
                            />
                        </t>
                    </div>

            </div>

            <!--            <div class="container-fluid o_secy_containe m-3">-->
            <!--                 <hr class="my-4"/>-->
            <!--                &lt;!&ndash; Section for Visits &ndash;&gt;-->

            <!--                <h3><i class="fa fa-tachometer ms-2 text-primary mx-1"></i><t-->
            <!--                        t-esc="translateVisitDashboardTitle"/></h3>-->
            <!--                <div class="row justify-content-start mx-2 mt-5">-->
            <!--                    <t t-foreach="getVisitDashboardItems()" t-as="visitItem" t-key="visitItem.label">-->
            <!--                        <DashboardItemCount-->
            <!--                                size="16"-->
            <!--                                label="visitItem.label"-->
            <!--                                icon="visitItem.icon"-->
            <!--                                chartIcon="visitItem.chartIcon"-->
            <!--                                buttonLabel="this.getTranslatedButtonLabel"-->
            <!--                                color="visitItem.color"-->
            <!--                                model="visitItem.model"-->
            <!--                                domain="visitItem.domain"-->
            <!--                                views="visitItem.views"-->
            <!--                        />-->
            <!--                    </t>-->
            <!--                </div>-->
            <!--            </div>-->
        </div>
    </t>
</templates>
