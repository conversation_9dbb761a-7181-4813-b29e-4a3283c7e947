<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: visitor_view_form-->
    <record id="visitor_view_form" model="ir.ui.view">
        <field name="name">ams_vm.visitor.form</field>
        <field name="model">ams_vm.visitor</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="first_name" required="1"/>
                            <field name="last_name" required="1"/>
                            <field name="name" string="full name" invisible="1"/>
                            <field name="sex"  required="True"/>
                            <field name="company_type" invisible="True"/>
                            <field name="company_id" invisible="True"/>
                            <field name="organization"/>

                        </group>
                        <group>
                            <field name="partner_id" invisible="1" readonly="1" required="False"/>
                            <field name="id_type_id" required="1" options="{'no_create': True , 'no_open': True}"
                                   widget="selection"/>
                            <field name="id_number" required="1"/>
                            <field name="nationality" required="1" options="{'no_create': True, 'no_open': True}"
                                   widget="many2one" context="{'load_all_countries': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Contact Information">
                            <group>
                                <!--                                <group>-->
                                <!--                                    <span class="o_form_label o_td_label" name="address_name">-->
                                <!--                                        <b>Address</b>-->
                                <!--                                    </span>-->
                                <!--                                    <div class="o_address_format">-->
                                <!--                                        <field name="street" placeholder="Street..." class="o_address_street"/>-->
                                <!--                                        <field name="street2" placeholder="Street 2..." class="o_address_street"/>-->
                                <!--                                        <field name="city" placeholder="City" class="o_address_city"/>-->
                                <!--                                        <field name="state_id" class="o_address_state" placeholder="State"-->
                                <!--                                               options="{'no_open': True, 'no_quick_create': True}"-->
                                <!--                                               context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>-->
                                <!--                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>-->
                                <!--                                        <div name="partner_address_country" class="d-flex justify-content-between">-->
                                <!--                                            <field name="country_id" placeholder="Country" class="o_address_country"-->
                                <!--                                                   options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                </group>-->
                                <group>
                                    <field name="phone" widget="phone" invisible="1"/>
                                    <field name="mobile" widget="phone" required="1"/>
                                    <field name="email"  placeholder="e.g. <EMAIL>"/>
                                    <!--                                    <field name="website" string="Website" widget="url" placeholder="e.g. https://www.odoo.com"/>-->
                                    <field name="avatar_128" invisible="1"/>
                                    <field name="image_1920" widget='image' class="oe_avatar"
                                           options='{"preview_image": "avatar_128"}'/>
                                </group>
                            </group>
                        </page>

                        <page name="technical" string="Technical Info" groups="base.group_no_one">
                            <group name="row1">
                                <group name="technical_col1">
                                    <field name="name" required="1" string="full name"/>
                                    <field name="qr_code"/>
                                    <field name="card_id" readonly="1"/>
                                    <field name="enroll_number" readonly="1"/>
                                    <field name="synced"/>
                                    <!--                                    <field name="partner_id" readonly="1"/>-->

                                </group>
                                <group name="technical_col2">
                                    <button name="action_regenerate_qr_code" type="object" string="Regenerate QR Code"
                                            icon="fa-refresh"
                                            class="btn-primary"
                                            invisible=" qr_code == ''"/>

                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter groups="base.group_system,ams_base.ams_group_manager"/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: visitor_view_list-->
    <record id="visitor_view_list" model="ir.ui.view">
        <field name="name">ams_vm.visitor.list</field>
        <field name="model">ams_vm.visitor</field>
        <field name="arch" type="xml">
            <list string="Visitor">
                <header>
                    <button name="action_copy_id_from_partner" type="object" string="Copy ID from Partner"
                            groups="base.group_no_one"/>
                </header>
                <field name="color" string="Status" widget="color"/>
                <field name="name"/>
                <field name="email"/>
                <field name="enroll_number" optional="hide"/>
                <field name="phone" optional="hide"/>
                <field name="company_id" optional="hide"/>
                <field name="organization"/>
                <field name="partner_id" optional="hide"/>
                <field name="id_number" optional="show"/>
                <field name="id_type_id" optional="show"/>
                <field name="sex" optional="show"/>
                <field name="nationality" optional="hide"/>
                <field name="enroll_number" optional="hide"/>
                <field name="qr_code" optional="hide"/>
                <field name="create_uid" optional="hide"/>
                <!--                <field name="employee_id" optional="hide"/>-->
                <!--                <field name="last_department_id" optional="hide"/>-->
                <field name="synced" optional="show"/>
                <field name="activate" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: visitor_view_kanban-->
    <record id="visitor_view_kanban" model="ir.ui.view">
        <field name="name">ams_vm.visitor.kanban</field>
        <field name="model">ams_vm.visitor</field>
        <field name="arch" type="xml">
            <kanban sample="1">
                <field name="id"/>
                <field name="color"/>
                <field name="display_name"/>
                <field name="title"/>
                <field name="email"/>
                <field name="parent_id"/>
                <field name="is_company"/>
                <field name="function"/>
                <field name="phone"/>
                <field name="street"/>
                <field name="street2"/>
                <field name="zip"/>
                <field name="city"/>
                <field name="country_id"/>
                <field name="mobile"/>
                <field name="state_id"/>
                <field name="category_id"/>
                <field name="avatar_128"/>
                <field name="type"/>
                <field name="active"/>
                <field name="id_number"/>
                <field name="id_type_id"/>
                <field name="partner_id"/>
                <field name="is_visitor"/>

                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill o_res_partner_kanban">
                            <t t-if="!record.is_company.raw_value">
                                <t t-set="avatar_image"
                                   t-value="kanban_image('res.partner', 'avatar_128', record.partner_id.raw_value)"/>
                                <div class="o_kanban_image_fill_left d-none d-md-block">
                                    <img t-attf-src="#{avatar_image}" t-att-alt="record.partner_id.value"/>
                                    <!--                                    <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value"-->
                                    <!--                                         t-att-alt="record.parent_id.value"-->
                                    <!--                                         t-att-src="kanban_image('res.partner', 'avatar_128', record.parent_id.raw_value)"/>-->
                                </div>
                                <div class="o_kanban_image d-md-none d-block">
                                    <img t-attf-src="#{avatar_image}" t-att-alt="record.id.value"/>
                                    <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value"
                                         t-att-alt="record.parent_id.value"
                                         t-att-src="kanban_image('res.partner', 'avatar_128', record.parent_id.raw_value)"/>
                                </div>
                            </t>
                            <t t-else="">
                                <div class="o_kanban_image_fill_left d-none d-md-block o_kanban_image_full">
                                    <img t-attf-src="#{kanban_image('res.partner', 'avatar_128', record.id.raw_value)}"
                                         role="img" t-att-alt="record.id.value"/>
                                </div>
                                <div class="o_kanban_image d-md-none d-block o_kanban_image_full">
                                    <img t-attf-src="#{kanban_image('res.partner', 'avatar_128', record.id.raw_value)}"
                                         role="img" t-att-alt="record.id.value"/>
                                </div>
                            </t>
                            <div class="ribbon ribbon-top-right" invisible="active">
                                <span class="text-bg-danger">Archived</span>
                            </div>
                            <div class="oe_kanban_details d-flex flex-column justify-content-between">
                                <div>
                                    <strong class="o_kanban_record_title oe_partner_heading">
                                        <field name="display_name"/>
                                    </strong>
                                    <div class="o_kanban_tags_section oe_kanban_partner_categories"/>
                                    <ul>
                                        <li t-if="record.parent_id.raw_value and !record.function.raw_value">
                                            <field name="parent_id"/>
                                        </li>
                                        <li t-elif="!record.parent_id.raw_value and record.function.raw_value">
                                            <field name="function"/>
                                        </li>
                                        <li t-elif="record.parent_id.raw_value and record.function.raw_value">
                                            <field name="function"/>
                                            at
                                            <field name="parent_id"/>
                                        </li>
                                        <li t-if="record.city.raw_value or record.country_id.raw_value">
                                            <t t-if="record.city.raw_value">
                                                <field name="city"/>
                                                <t t-if="record.country_id.raw_value">,</t>
                                            </t>
                                            <t t-if="record.country_id.raw_value">
                                                <field name="country_id"/>
                                            </t>
                                        </li>
                                        <li t-if="record.email.raw_value" class="o_text_overflow">
                                            <field name="email"/>
                                        </li>
                                        <li t-if="record.id_type_id.raw_value">
                                            <span>
                                                <strong>
                                                    <field name="id_type_id"/>
                                                </strong>
                                            </span>
                                            <span class="mx-1">
                                                <field name="id_number"/>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left"/>
                                    <div class="oe_kanban_bottom_right"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!--TODO[IMP]: contact_view_search-->
    <record id="visitor_view_search" model="ir.ui.view">
        <field name="name">ams_vm.visitor.search.inherit</field>
        <field name="model">ams_vm.visitor</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="partner_id"/>
                <filter name="my_visitors" string="My Visitors"
                        domain="[('create_uid', '=', uid)]"
                />
            </xpath>
        </field>
    </record>

    <!--TODO[IMP]: base_visitor_request_action-->
    <record id="visitor_action" model="ir.actions.act_window">
        <field name="name">Visitors</field>
        <field name="res_model">ams_vm.visitor</field>
        <field name="view_mode">list,form,search,kanban</field>
        <field name="context">{'search_default_my_visitors': 1, 'search_default_available': 1, 'default_is_visitor': 1,
                               'apply_followup_domain': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first visitor!
            </p>
            <p>
                Create visitor
            </p>
        </field>
    </record>


    <record id="visitor_server_action" model="ir.actions.server">
        <field name="name">Visitors</field>
        <field name="model_id" ref="model_ams_vm_visitor"/>
        <field name="binding_model_id" ref="model_ams_vm_visitor"/>
        <field name="state">code</field>
        <field name="code">
            action = env['ams_vm.visitor'].sudo().action_open_views(
                {
                    'action_ref': 'ams_vm.visitor_action',
                    'use_domain_follow_up_visibility': True
                }
            )

        </field>
        <field name="type">ir.actions.server</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
    </record>
</odoo>
