<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: base_request_view_form-->
    <record id="base_visitor_request_view_form" model="ir.ui.view">
        <field name="name">ams_vm.base_request.form</field>
        <field name="model">ams_vm.base_request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_reset" type="object" string="Reset" icon="fa-refresh" class="btn-primary"
                            invisible="state in ['pending' ,'confirmed'] or not is_current_user_requester"/>
                    <button name="action_request_approve" type="object" string="Request Approve" icon="fa-paper-plane"
                            class="btn-primary"
                            invisible="state != 'pending' or has_approval_access"/>
                    <button name="action_approve" type="object" string="Submit Request" icon="fa-paper-plane"
                            class="btn-success"
                            invisible="current_employee_level != 'user' or state not in ['pending', 'need_approval'] or not has_approval_access or (state == 'pending' and not is_current_user_requester)"
                            groups="!ams_vm.group_security_approval"/>

                    <button name="action_approve" type="object" string="Approve" icon="fa-check" class="btn-success"
                            invisible="current_employee_level == 'user' or state not in ['pending', 'need_approval'] or not has_approval_access or (state == 'pending' and not is_current_user_requester)"
                            groups="!ams_vm.group_security_approval"/>


                    <button name="action_reject" type="object" string="Reject" icon="fa-trash" class="btn-danger"
                            invisible="state != 'need_approval' or not has_approval_access"
                            groups="!ams_vm.group_security_approval"/>
                    <button name="action_cancel" type="object" string="Cancel" icon="fa-close" class="btn-warning"
                            invisible="state not in ['need_approval' ,'approved'] or not is_current_user_requester"/>
                    <button name="action_confirm" type="object" string="Confirm" icon="fa-check" class="btn-info"
                            invisible="state != 'approved' " groups="ams_vm.group_security_approval"/>

                    <button name="action_confirm" type="object" string="Confirm" icon="fa-check" class="btn-info"
                            invisible="not (state == 'approved' and  is_event == True) "
                            groups="ams_vm.group_event_request"/>

                    <button name="action_confirm" type="object" string="Confirm" icon="fa-check" class="btn-info"
                            invisible="not (state == 'approved' and  is_event == True) "
                            groups="ams_vm.group_event_manager"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Standard"
                            bg_color="bg-success"/>
                    <div name="button_box"></div>
                    <div name="alert_box"></div>

                    <div class="border-bottom rounded  p-2 bg-light  mb-2"
                         invisible="not request_uid or state in ['pending']">
                        <div class="row">
                            <!-- First Row -->
                            <div class="col-lg-6 col-md-6 col-sm-12 d-flex align-items-center">
                                <i class="fa fa-hashtag text-primary me-2" title="Request Number"></i>
                                <span><field name="name"/></span>
                            </div>

                            <div class="col-lg-6 col-md-6 col-sm-12 d-flex align-items-center">
                                <i class="fa fa-user text-primary me-2" title="Request By"></i>
                                <span><field name="request_uid" options="{'no_open': 'is_readonly'}"/></span>
                                <i class="fa fa-calendar  mx-2" title="Request Date"></i>
                                <span class="text-muted"><field name="request_date"/></span>
                            </div>
                            <!-- Second Row -->
                            <div class="col-lg-6 col-md-6 col-sm-12 d-flex align-items-center">
                                <i class="fa fa-tasks text-primary me-2" title="Status"></i>
                                <span>
                                    <field name="status" decoration-success="status == 'finished'"
                                           decoration-info="status == 'pending'"
                                           decoration-warning="status == 'running'"/>
                                </span>

                                <i class="fa fa-envelope text-primary mx-2" title="Email Status"></i>
                                <span class="text-info">
                                    <field name="email_state" decoration-info="email_state == 'pending'"
                                           decoration-success="email_state == 'sent'"
                                           decoration-danger="email_state == 'failed'"/>
                                </span>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 d-flex align-items-center"
                                 invisible="not response_uid">
                                <i class="fa fa-edit text-primary me-2" title="Response By"></i>
                                <span><field name="response_uid" options="{'no_open': 'is_readonly'}"/></span>
                                <i class="fa fa-calendar  mx-2" title="Last Response Date"></i>
                                <span class="text-muted"><field name="response_date"/></span>
                            </div>
                        </div>
                    </div>
                    <group>
                        <group>
                            <field name="subject" readonly="is_readonly"/>
                            <!--                            <field name="visitor_id" readonly="is_readonly" options="{'no_open': 'is_readonly'}"/>-->
                            <field name="visitor_id" readonly="is_readonly"/>

                            <field name="visitors_count" invisible="is_event == False" readonly="is_readonly or 0 "/>
                            <field name="related_company" readonly="is_readonly"
                                   invisible="schedule_type == 'single' or is_event == True"/>
                        </group>
                        <group>
                            <field name="schedule_type" readonly="is_readonly"/>
                            <field name="start_date" readonly="is_readonly"/>
                            <field name="duration" widget="float_time" readonly="is_readonly"/>
                            <field name="end_date" readonly="is_readonly"/>
                            <field name="approval_uid" readonly="is_readonly"
                                   invisible="state != 'pending' or has_approval_access"/>
                            <field name="state" invisible="1" readonly="is_readonly"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Request Info" name="request_info">
                            <group name="request_info_main_group">
                                <group name="request_info_col1">
                                    <field name="commit"/>
                                </group>
                                <group name="request_info_col2">
                                    <field name="date"/>
                                </group>

                            </group>
                            <group>
                                <group name="request_info_col3">

                                </group>

                            </group>
                        </page>


                        <page name="technical" string="Technical Info" groups="base.group_no_one">
                            <group name="row1">
                                <group name="technical_col1">
                                    <field name="qr_code"/>
                                    <field name="need_approval"/>
                                    <field name="is_current_user_requester"/>
                                    <field name="has_approval_access"/>
                                    <field name="current_employee_level"/>
                                    <field name="is_readonly"/>
                                </group>
                                <group name="technical_col2">
                                    <field name="company_id" optional="hide"/>
                                    <field name="schedule_type" readonly="1"/>
                                    <field name="is_event" readonly="1"/>
                                    <field name="visitors_count"/>
                                    <field name="approval_uid" readonly="1"/>
                                    <button name="action_reset" type="object" string="Reset" icon="fa-refresh"
                                            class="btn-primary"/>

                                </group>

                            </group>

                        </page>

                    </notebook>
                    <!--    <group>-->
                    <!--                                <group name="request_info_col3">-->

                    <!--                                </group>-->

                    <!--                            </group>-->
                </sheet>
                <chatter groups="base.group_system,ams_base.ams_group_manager"/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: base_request_view_list-->
    <record id="base_visitor_request_view_list" model="ir.ui.view">
        <field name="name">ams_vm.base_request.list</field>
        <field name="model">ams_vm.base_request</field>
        <field name="arch" type="xml">
            <list string="Invitation List">
                <field name="name"/>
                <field name="visitor_id"/>
                <field name="subject" optional="show"/>
                <field name="start_date" optional="show"/>
                <field name="end_date" optional="hide"/>
                <field name="date" optional="hide"/>
                <field name="state" optional="show"
                       decoration-bf="state == 'pending'"
                       decoration-warning="state == 'need_approval'"
                       decoration-info="state == 'approved'"
                       decoration-success="state == 'confirmed'"
                       decoration-danger="state == 'rejected'"
                       decoration-muted="state == 'cancelled'"
                />
                <field name="status" optional="show" decoration-success="status == 'finished'"
                       decoration-info="status == 'pending'" decoration-warning="status == 'running'"/>
                <field name="email_state" optional="show" decoration-info="email_state == 'pending'"
                       decoration-success="email_state == 'sent'" decoration-danger="email_state == 'failed'"/>
                <field name="request_uid" optional="hide"/>
                <field name="response_uid" optional="hide"/>
                <field name="request_date" optional="hide"/>
                <field name="response_date" optional="hide"/>
                <field name="commit" optional="hide"/>
                <field name="create_uid" optional="hide"/>
                <field name="last_department_id" optional="hide"/>
                <field name="department_id" optional="hide"/>
                <field name="dept_depth" optional="hide" groups="base.group_no_one"/>
                <field name="last_dept_depth" optional="hide" groups="base.group_no_one"/>
                <field name="schedule_type" optional="hide"/>
                <field name="is_event" optional="hide"/>
                <field name="visitors_count" optional="hide"/>
                <field name="related_company" optional="hide"/>
                <field name="approval_uid" optional="hide"/>
                <field name="company_id" optional="hide"/>
                <field name="qr_code" optional="hide"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: base_request_view_search-->
    <record id="base_visitor_request_view_search" model="ir.ui.view">
        <field name="name">ams_vm.base_request.search</field>
        <field name="model">ams_vm.base_request</field>
        <field name="arch" type="xml">

            <search>
                <field name="name"/>
                <field name="visitor_id"/>
                <field name="state"/>

                <group expand="1" string="Group By">
                    <filter name="today" string="Today" domain="[('date', '>=', context_today())]"/>
                    <separator/>
                    <filter name="group_by_visitor" string="Visitor" context="{'group_by': 'visitor_id'}"/>
                    <filter name="group_by_schedule_type" string="Schedule Type"
                            context="{'group_by': 'schedule_type'}"/>
                    <filter name="pending" string="Pending" domain="[('state', '=', 'pending')]"/>
                    <filter name="need_approval" string="Need Approval" domain="[('state', '=', 'need_approval')]"/>
                    <filter name="approved" string="Approved" domain="[('state', '=', 'approved')]"/>
                    <filter name="rejected" string="Rejected" domain="[('state', '=', 'rejected')]"/>
                    <filter name="confirmed" string="Confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter name="cancelled" string="Cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <separator/>
                    <filter name="schedule_type_single"
                            string="Single Day"
                            domain="[('schedule_type', '=', 'single')]"/>
                    <filter name="schedule_type_multiple"
                            string="Multiple Days"
                            domain="[('schedule_type', '=', 'multiple')]"/>
                    <separator/>
                    <filter name="is_event"
                            string="Event"
                            domain="[('is_event', '=', True)]"/>
                </group>
            </search>

        </field>
    </record>
</odoo>
