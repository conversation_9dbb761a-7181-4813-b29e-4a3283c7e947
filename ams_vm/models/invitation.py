# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class Invitation(models.Model):
    # region ---------------------- Private Attributes --------------------------------
    _name = 'ams_vm.invitation'
    _description = 'Invitation'
    _inherit = ['ams_vm.base_request']
    _rec_name = "rec_name"

    # endregion

    # region ----------------------  Properties ------------------------------------
    # endregion

    # region ---------------------- Default Methods ------------------------------------
    # endregion

    # region ---------------------- Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(string="Request No", required=True, readonly=True, index=True, default='New', copy=False)
    rec_name = fields.Char(compute='_compute_rec_name', store=False)
    duration = fields.Float(
        string="Duration",
        compute="_compute_duration",
        store=True,
        help="Duration in hours for single schedule, days for multiple schedule",
        default=1
    )
    room_name = fields.Char(string="Room Name")
    location_address = fields.Char(string="Location", help="Building-Floor")
    host_work_mobile = fields.Char(string="Host Work Mobile", related="request_uid.mobile_phone",readonly=False,related_sudo=True)
    host_work_phone = fields.Char(string="Host Work Extension", related="request_uid.work_phone",readonly=False,related_sudo=True)
    qr_code = fields.Char(string="QR Code", copy=False, readonly=True,search='_search_qrcode')
    qr_codes = fields.Char(compute='_compute_qr_codes', search='_search_qrcode')

    # endregion

    # region  Relational
    total_visits = fields.Integer(string="Total Visits", compute='_compute_visits_count')
    visitor_id = fields.Many2one('ams_vm.visitor', string='Main Visitor', ondelete='restrict')

    # Related fields for mobile and phone
    visitor_mobile = fields.Char(related='visitor_id.mobile', string='Mobile', store=True)
    visitor_phone = fields.Char(related='visitor_id.phone', string='Phone', store=True)
    # endregion

    # endregion

    # region ---------------------- Compute methods ------------------------------------
    @api.depends('name', 'subject')
    def _compute_rec_name(self):
        for record in self:
            record.rec_name = record.subject or record.name
            if record.name and  record.subject:
                record.rec_name = f"{record.name} - {record.subject}"

    def _compute_visits_count(self):
        for rec in self:
            rec.total_visits = self.env['ams_vm.visit'].search_count([('invitation_id', '=', rec.id)])

    def _compute_qr_codes(self):
        for rec in self:
            rec.qr_codes = ''.join([visitor.qr_code for visitor in self.invitation_visitor_ids])

    # endregion

    # region ---------------------- CRUD Methods -------------------------------------
    def create(self, vals):
        if isinstance(vals, dict):
            vals = [vals]  # convert dict to list of dicts
        for val in vals:
            if val.get('name', 'New') == 'New':
                val['name'] = self.env['ir.sequence'].next_by_code('ams.seq_invitation') or 'New'
        res = super(Invitation, self).create(vals)
        return res

    # endregion

    # region ---------------------- Action Methods -------------------------------------
    def action_send_invitation_email(self):
        # Send invitation email for each visitor
        for invitation_visitor_id in self.invitation_visitor_ids:
            invitation_visitor_id.sudo().action_send_invitation_email()

    def action_open_visit_view(self):
        self.ensure_one()  # Ensure the action is only for a single record
        """Open the list view of memos related to entry record."""
        return {
            'name': 'Visits',
            'type': 'ir.actions.act_window',
            'res_model': 'ams_vm.visit',
            'view_mode': 'list,form',
            'domain': [('invitation_id', '=', self.id)],
            'target': 'current',
        }

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- Business Methods -------------------------------------
    @api.model
    def print_all_visitor_badges(self, invitation_ids):
        # Collect all visitors associated with the invitations
        invitations = self.browse(invitation_ids)
        visitors = invitations.mapped('invitation_visitor_ids')

        # Raise a warning if no visitors are found
        if not visitors:
            raise UserError(_("No visitors found for the selected invitations."))

        # Get the badge report action from the first visitor
        report_action = visitors[0].badge_report_action

        # Prepare the action to print the badges
        action = report_action.report_action(visitors.ids, config=False)
        action['type'] = 'ir.actions.report'
        return action

    def _search_qrcode(self, operator,value):
        """
        Search for invitations by main visitor's QR code or any guest's QR code.
        1-search in ams_vm.invitation_visitor model by qr_code (qr_code should be index=True)
        2-careful in qr_coded field which contain search method it should be computed

        return invitation which contain visitor qr_code
        """
        domain = []
        if value:

            invitation_visitors = self.env['ams_vm.invitation_visitor'].search_fetch(
                            [('qr_code', operator, value)], ['invitation_id'])
            domain=[('id', 'in', invitation_visitors.mapped('invitation_id').ids)]

        return domain

    @api.depends("start_date", "end_date", "schedule_type")
    def _compute_duration(self):
        for record in self:
            if not (record.start_date and record.end_date):
                record.duration = 0
                continue

            if record.schedule_type == "multiple":
                # Calculate duration in days for multiple schedule type
                date_difference = (
                    record.end_date - record.start_date
                ).days + 1  # Include both start and end dates
                record.duration = date_difference
            else:
                # For single schedule type, calculate duration in hours
                duration_hours = (
                    record.end_date - record.start_date
                ).total_seconds() / 3600
                record.duration = duration_hours

    # @api.model
    # def name_search(self, name='', args=None, operator='ilike', limit=100):
    #     domain = args or []
    #     # optimization for a SOL services name_search, to avoid joining on sale_order with too many lines
    #     if domain and ('is_service', '=', True) in domain and operator in ('like', 'ilike') and limit is not None:
    #         sols = self.search_fetch(
    #             domain, ['display_name'], limit=limit, order='order_id.id DESC, sequence, id',
    #         )
    #         return [(sol.id, sol.display_name) for sol in sols]
    #     return super().name_search(name, domain, operator, limit)

    # def get_dashboard_domain(self):
    #     date_domain = self.get_date_domain()
    #     follow_up_domain = self.follow_up_domain
    #     domain = date_domain + follow_up_domain
    #     return domain
    #     # domain  =  [('date', '=', fields.Date.today())]
    #     # return follow_up_domain
    #
    # def get_dashboard_count_item_value(self, state=''):
    #     domain =self.get_dashboard_count_item_domain(state)
    #     return self.search_count(domain)
    # endregion
